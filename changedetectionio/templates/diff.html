{% extends 'base.html' %}
{% from '_helpers.html' import render_field, render_checkbox_field, render_button %}
{% block content %}
<script>
    const screenshot_url="{{url_for('static_content', group='screenshot', filename=uuid)}}";
    {% if last_error_screenshot %}
    const error_screenshot_url="{{url_for('static_content', group='screenshot', filename=uuid, error_screenshot=1) }}";
    {% endif %}

    const highlight_submit_ignore_url="{{url_for('ui.ui_edit.highlight_submit_ignore_url', uuid=uuid)}}";

</script>
<script src="{{url_for('static_content', group='js', filename='diff-overview.js')}}" defer></script>

<div id="settings">
    <form class="pure-form " action="" method="GET" id="diff-form">
        <fieldset class="diff-fieldset">
            {% if versions|length >= 1 %}
                <strong>Compare</strong>
                <del class="change"><span>from</span></del>
                <select id="diff-version" name="from_version" class="needs-localtime">
                    {% for version in versions|reverse %}
                        <option value="{{ version }}" {% if version== from_version %} selected="" {% endif %}>
                            {{ version }}
                        </option>
                    {% endfor %}
                </select>
                <ins class="change"><span>to</span></ins>
                <select id="current-version" name="to_version" class="needs-localtime">
                    {% for version in versions|reverse %}
                        <option value="{{ version }}" {% if version== to_version %} selected="" {% endif %}>
                            {{ version }}
                        </option>
                    {% endfor %}
                </select>
                <button type="submit" class="pure-button pure-button-primary reset-margin">Go</button>
            {% endif %}
        </fieldset>
        <fieldset>
            <strong>Style</strong>
            <label for="diffWords" class="pure-checkbox">
                <input type="radio" name="diff_type" id="diffWords" value="diffWords"> Words</label>
            <label for="diffLines" class="pure-checkbox">
                <input type="radio" name="diff_type" id="diffLines" value="diffLines" checked=""> Lines</label>

            <label for="diffChars" class="pure-checkbox">
                <input type="radio" name="diff_type" id="diffChars" value="diffChars"> Chars</label>
            <!-- @todo - when mimetype is JSON, select this by default? -->
            <label for="diffJson" class="pure-checkbox">
                <input type="radio" name="diff_type" id="diffJson" value="diffJson"> JSON</label>

            <span>
        <!-- https://github.com/kpdecker/jsdiff/issues/389 ? -->
        <label for="ignoreWhitespace" class="pure-checkbox" id="label-diff-ignorewhitespace">
            <input type="checkbox" id="ignoreWhitespace" name="ignoreWhitespace"> Ignore Whitespace</label>
    </span>
        </fieldset>
    </form>

</div>

<div id="diff-jump">
    <a id="jump-next-diff" title="Jump to next difference">Jump</a>
</div>

<script src="{{url_for('static_content', group='js', filename='tabs.js')}}" defer></script>
<div class="tabs">
    <ul>
        {% if last_error_text %}<li class="tab" id="error-text-tab"><a href="#error-text">Error Text</a></li> {% endif %}
        {% if last_error_screenshot %}<li class="tab" id="error-screenshot-tab"><a href="#error-screenshot">Error Screenshot</a></li> {% endif %}
        <li class="tab" id=""><a href="#text">Text</a></li>
        <li class="tab" id="screenshot-tab"><a href="#screenshot">Screenshot</a></li>
        <li class="tab" id="extract-tab"><a href="#extract">Extract Data</a></li>
    </ul>
</div>

<div id="diff-ui">
    <div class="tab-pane-inner" id="error-text">
        <div class="snapshot-age error">{{watch_a.error_text_ctime|format_seconds_ago}} seconds ago</div>
        <pre>
            {{ last_error_text }}
        </pre>
    </div>

    <div class="tab-pane-inner" id="error-screenshot">
        <div class="snapshot-age error">{{watch_a.snapshot_error_screenshot_ctime|format_seconds_ago}} seconds ago</div>
        <img id="error-screenshot-img"  style="max-width: 80%" alt="Current error-ing screenshot from most recent request" >
    </div>

     <div class="tab-pane-inner" id="text">
         {% if password_enabled_and_share_is_off %}
           <div class="tip">Pro-tip: You can enable <strong>"share access when password is enabled"</strong> from settings</div>
         {% endif %}

         <div class="snapshot-age">{{watch_a.snapshot_text_ctime|format_timestamp_timeago}}</div>

         <table>
             <tbody>
             <tr>
                 <!-- just proof of concept copied straight from github.com/kpdecker/jsdiff -->
                 <td id="a" style="display: none;">{{from_version_file_contents}}</td>
                 <td id="b" style="display: none;">{{to_version_file_contents}}</td>
                 <td id="diff-col">
                     <span id="result" class="highlightable-filter"></span>
                 </td>
             </tr>
             </tbody>
         </table>
         Diff algorithm from the amazing <a href="https://github.com/kpdecker/jsdiff">github.com/kpdecker/jsdiff</a>
     </div>
     <div class="tab-pane-inner" id="screenshot">
         <div class="tip">
             For now, Differences are performed on text, not graphically, only the latest screenshot is available.
         </div>
         {% if is_html_webdriver %}
           {% if screenshot %}
            <div class="snapshot-age">{{watch_a.snapshot_screenshot_ctime|format_timestamp_timeago}}</div>
            <img style="max-width: 80%" id="screenshot-img" alt="Current screenshot from most recent request" >
           {% else %}
              No screenshot available just yet! Try rechecking the page.
           {% endif %}
         {% else %}
           <strong>Screenshot requires Playwright/WebDriver enabled</strong>
         {% endif %}
     </div>
    <div class="tab-pane-inner" id="extract">
        <form id="extract-data-form" class="pure-form pure-form-stacked edit-form"
              action="{{ url_for('ui.ui_views.diff_history_page', uuid=uuid) }}#extract"
              method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

            <p>This tool will extract text data from all of the watch history.</p>

            <div class="pure-control-group">
                {{ render_field(extract_form.extract_regex) }}
                <span class="pure-form-message-inline">
                    A <strong>RegEx</strong> is a pattern that identifies exactly which part inside of the text that you want to extract.<br>

                    <p>
                        For example, to extract only the numbers from text &dash;<br>
                        <strong>Raw text</strong>: <code>Temperature <span style="color: red">5.5</span>°C in Sydney</code><br>
                        <strong>RegEx to extract:</strong> <code>Temperature <span style="color: red">([0-9\.]+)</span></code><br>
                    </p>
                    <p>
                        <a href="https://RegExr.com/">Be sure to test your RegEx here.</a>
                    </p>
                    <p>
                        Each RegEx group bracket <code>()</code> will be in its own column, the first column value is always the date.
                    </p>
                </span>
            </div>
            <div class="pure-control-group">
                {{ render_button(extract_form.extract_submit_button) }}
            </div>
        </form>
    </div>
</div>

<script>
    const newest_version_timestamp = {{newest_version_timestamp}};
</script>
<script src="{{url_for('static_content', group='js', filename='diff.min.js')}}"></script>

<script src="{{url_for('static_content', group='js', filename='diff-render.js')}}"></script>


{% endblock %}
