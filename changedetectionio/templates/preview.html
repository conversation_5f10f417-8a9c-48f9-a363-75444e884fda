{% extends 'base.html' %}

{% block content %}
    <script>
        const screenshot_url = "{{url_for('static_content', group='screenshot', filename=uuid)}}";
        const triggered_line_numbers = {{ triggered_line_numbers|tojson }};
        {% if last_error_screenshot %}
            const error_screenshot_url = "{{url_for('static_content', group='screenshot', filename=uuid, error_screenshot=1) }}";
        {% endif %}
        const highlight_submit_ignore_url = "{{url_for('ui.ui_edit.highlight_submit_ignore_url', uuid=uuid)}}";
    </script>
    <script src="{{url_for('static_content', group='js', filename='plugins.js')}}"></script>
    <script src="{{ url_for('static_content', group='js', filename='diff-overview.js') }}" defer></script>
    <script src="{{ url_for('static_content', group='js', filename='preview.js') }}" defer></script>
    <script src="{{ url_for('static_content', group='js', filename='tabs.js') }}" defer></script>
    {% if versions|length >= 2 %}
        <div id="settings" style="text-align: center;">
            <form class="pure-form " action="" method="POST">
                <fieldset>
                    <label for="preview-version">Select timestamp</label> <select id="preview-version"
                                                                                 name="from_version"
                                                                                 class="needs-localtime">
                    {% for version in versions|reverse %}
                        <option value="{{ version }}" {% if version == current_version %} selected="" {% endif %}>
                            {{ version }}
                        </option>
                    {% endfor %}
                </select>
                    <button type="submit" class="pure-button pure-button-primary">Go</button>

                </fieldset>
            </form>
                <br>
                <strong>Keyboard: </strong><a href="" class="pure-button pure-button-primary" id="btn-previous">
                &larr; Previous</a> &nbsp; <a class="pure-button pure-button-primary" id="btn-next" href="">
                &rarr; Next</a>
        </div>
    {% endif %}

    <div class="tabs">
        <ul>
            {% if last_error_text %}
                <li class="tab" id="error-text-tab"><a href="#error-text">Error Text</a></li> {% endif %}
            {% if last_error_screenshot %}
                <li class="tab" id="error-screenshot-tab"><a href="#error-screenshot">Error Screenshot</a>
                </li> {% endif %}
            {% if history_n > 0 %}
                <li class="tab" id="text-tab"><a href="#text">Text</a></li>
                <li class="tab" id="screenshot-tab"><a href="#screenshot">Screenshot</a></li>
            {% endif %}
        </ul>
    </div>


    <div id="diff-ui">
        <div class="tab-pane-inner" id="error-text">
            <div class="snapshot-age error">{{ watch.error_text_ctime|format_seconds_ago }} seconds ago</div>
            <pre>
            {{ last_error_text }}
        </pre>
        </div>

        <div class="tab-pane-inner" id="error-screenshot">
            <div class="snapshot-age error">{{ watch.snapshot_error_screenshot_ctime|format_seconds_ago }} seconds ago
            </div>
            <img id="error-screenshot-img" style="max-width: 80%"
                 alt="Current erroring screenshot from most recent request">
        </div>

        <div class="tab-pane-inner" id="text">
            <div class="snapshot-age">{{ current_version|format_timestamp_timeago }}</div>
            <span class="tip"><strong>Pro-tip</strong>: Highlight text to add to ignore filters</span>

            <table>
                <tbody>
                <tr>
                    <td id="diff-col" class="highlightable-filter">
                        <pre style="border-left: 2px solid #ddd;">
{{ content }}
                        </pre>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>

        <div class="tab-pane-inner" id="screenshot">
            <div class="tip">
                For now, Differences are performed on text, not graphically, only the latest screenshot is available.
            </div>
            <br>
            {% if is_html_webdriver %}
                {% if screenshot %}
                    <div class="snapshot-age">{{ watch.snapshot_screenshot_ctime|format_timestamp_timeago }}</div>
                    <img style="max-width: 80%" id="screenshot-img" alt="Current screenshot from most recent request">
                {% else %}
                    No screenshot available just yet! Try rechecking the page.
                {% endif %}
            {% else %}
                <strong>Screenshot requires Playwright/WebDriver enabled</strong>
            {% endif %}
        </div>
    </div>
{% endblock %}
