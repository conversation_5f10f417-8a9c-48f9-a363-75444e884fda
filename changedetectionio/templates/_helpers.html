{% macro render_field(field) %}
  <div {% if field.errors %} class="error" {% endif %}>{{ field.label }}</div>
  <div {% if field.errors %} class="error" {% endif %}>{{ field(**kwargs)|safe }}
  {% if field.errors %}
    <ul class=errors>
    {% for error in field.errors %}
      <li>{{ error }}</li>
    {% endfor %}
    </ul>
  {% endif %}
  </div>
{% endmacro %}

{% macro render_checkbox_field(field) %}
  <div class="checkbox {% if field.errors %} error {% endif %}">
  {{ field(**kwargs)|safe }} {{ field.label }}
  {% if field.errors %}
    <ul class=errors>
    {% for error in field.errors %}
      <li>{{ error }}</li>
    {% endfor %}
    </ul>
  {% endif %}
  </div>
{% endmacro %}


{% macro render_simple_field(field) %}
  <span class="label {% if field.errors %}error{% endif %}">{{ field.label }}</span>
  <span {% if field.errors %} class="error" {% endif %}>{{ field(**kwargs)|safe }}
  {% if field.errors %}
    <ul class=errors>
    {% for error in field.errors %}
      <li>{{ error }}</li>
    {% endfor %}
    </ul>
  {% endif %}
  </span>
{% endmacro %}


{% macro render_nolabel_field(field) %}
    <span>
    {{ field(**kwargs)|safe }}
        {% if field.errors %}
            <span class="error">
      {% if field.errors %}
          <ul class=errors>
        {% for error in field.errors %}
            <li>{{ error }}</li>
        {% endfor %}
        </ul>
      {% endif %}
      </span>
        {% endif %}
    </span>
{% endmacro %}


{% macro render_button(field) %}
  {{ field(**kwargs)|safe }}
{% endmacro %}

{% macro render_conditions_fieldlist_of_formfields_as_table(fieldlist, table_id="rulesTable") %}
  <div class="fieldlist_formfields" id="{{ table_id }}">
    <div class="fieldlist-header">
      {% for subfield in fieldlist[0] %}
        <div class="fieldlist-header-cell">{{ subfield.label }}</div>
      {% endfor %}
      <div class="fieldlist-header-cell">Actions</div>
    </div>
    <div class="fieldlist-body">
      {% for form_row in fieldlist %}
        <div class="fieldlist-row {% if form_row.errors %}error-row{% endif %}">
          {% for subfield in form_row %}
            <div class="fieldlist-cell">

              {{ subfield()|safe }}
              {% if subfield.errors %}
                <ul class="errors">
                  {% for error in subfield.errors %}
                    <li class="error">{{ error }}</li>
                  {% endfor %}
                </ul>
              {% endif %}
            </div>
          {% endfor %}
          <div class="fieldlist-cell fieldlist-actions">
            <button type="button" class="addRuleRow" title="Add a row/rule after">+</button>
            <button type="button" class="removeRuleRow" title="Remove this row/rule">-</button>
            <button type="button" class="verifyRuleRow" title="Verify this rule against current snapshot">✓</button>
          </div>
        </div>
      {% endfor %}
    </div>
  </div>
{% endmacro %}


{% macro playwright_warning() %}
    <p><strong>Error - This watch needs Chrome (with playwright/sockpuppetbrowser), but Chrome based fetching is not enabled.</strong> Alternatively try our <a href="https://changedetection.io">very affordable subscription based service which has all this setup for you</a>.</p>
    <p>You may need to <a href="https://github.com/dgtlmoon/changedetection.io/blob/09ebc6ec6338545bdd694dc6eee57f2e9d2b8075/docker-compose.yml#L31">Enable playwright environment variable</a> and uncomment the <strong>sockpuppetbrowser</strong> in the <a href="https://github.com/dgtlmoon/changedetection.io/blob/master/docker-compose.yml">docker-compose.yml</a> file.</p>
    <br>
{% endmacro %}

{% macro only_playwright_type_watches_warning() %}
    <p><strong>Sorry, this functionality only works with Playwright/Chrome enabled watches.<br>You need to <a href="#request">Set the fetch method to Playwright/Chrome mode and resave</a> and have the SockpuppetBrowser/Playwright or Selenium enabled.</strong></p><br>
{% endmacro %}

{% macro render_time_schedule_form(form, available_timezones, timezone_default_config) %}
    <style>
    .day-schedule *, .day-schedule select {
        display: inline-block;
    }

    .day-schedule label[for*="time_schedule_limit-"][for$="-enabled"] {
        min-width: 6rem;
        font-weight: bold;
    }
    .day-schedule label {
        font-weight: normal;
    }

    .day-schedule table label {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    #timespan-warning, input[id*='time_schedule_limit-timezone'].error {
        color: #ff0000;
    }
    .day-schedule.warning table {
        background-color: #ffbbc2;
    }
    ul#day-wrapper {
        list-style: none;
    }
    #timezone-info > * {
        display: inline-block;
    }

    #scheduler-icon-label {
        background-position: left center;
        background-repeat: no-repeat;
        background-size: contain;
        display: inline-block;
        vertical-align: middle;
        padding-left: 50px;
        background-image: url({{ url_for('static_content', group='images', filename='schedule.svg') }});
    }
    #timespan-warning {
        display: none;
    }
    </style>
    <br>

    {% if timezone_default_config %}
    <div>
        <span id="scheduler-icon-label" style="">
            {{ render_checkbox_field(form.time_schedule_limit.enabled) }}
            <div class="pure-form-message-inline">
                Set a hourly/week day schedule
            </div>
        </span>

    </div>
    <br>
    <div id="schedule-day-limits-wrapper">
        <label>Schedule time limits</label><a data-template="business-hours"
                                              class="set-schedule pure-button button-secondary button-xsmall">Business
        hours</a>
        <a data-template="weekend" class="set-schedule pure-button button-secondary button-xsmall">Weekends</a>
        <a data-template="reset" class="set-schedule pure-button button-xsmall">Reset</a><br>
        <br>

        <ul id="day-wrapper">
            {% for day in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] %}
                <li class="day-schedule" id="schedule-{{ day }}">
                    {{ render_nolabel_field(form.time_schedule_limit[day]) }}
                </li>
            {% endfor %}
            <li id="timespan-warning">Warning, one or more of your 'days' has a duration that would extend into the next day.<br>
            This could have unintended consequences.</li>
            <li id="timezone-info">
                {{ render_field(form.time_schedule_limit.timezone, placeholder=timezone_default_config) }} <span id="local-time-in-tz"></span>
                <datalist id="timezones" style="display: none;">
                    {% for timezone in available_timezones %}
                        <option value="{{ timezone }}">{{ timezone }}</option>
                    {% endfor %}
                </datalist>
            </li>
        </ul>
    <br>
        <span class="pure-form-message-inline">
         <a href="https://changedetection.io/tutorials">More help and examples about using the scheduler</a>
        </span>
    </div>
    {% else %}
        <span class="pure-form-message-inline">
            Want to use a time schedule? <a href="{{url_for('settings.settings_page')}}#timedate">First confirm/save your Time Zone Settings</a>
        </span>
        <br>
    {% endif %}

{% endmacro %}