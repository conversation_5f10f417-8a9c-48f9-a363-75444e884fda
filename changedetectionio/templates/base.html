<!DOCTYPE html>
<html lang="en" data-darkmode="{{ get_darkmode_state() }}">

  <head>
    <meta charset="utf-8" >
    <meta name="viewport" content="width=device-width, initial-scale=1.0" >
    <meta name="description" content="Self hosted website change detection." >
    <title>Change Detection{{extra_title}}</title>
    {% if app_rss_token %}
      <link rel="alternate" type="application/rss+xml" title="Changedetection.io » Feed{% if active_tag_uuid %}- {{active_tag.title}}{% endif %}" href="{{ url_for('rss.feed', tag=active_tag_uuid , token=app_rss_token)}}" >
    {% endif %}
    <link rel="stylesheet" href="{{url_for('static_content', group='styles', filename='pure-min.css')}}" >
    <link rel="stylesheet" href="{{url_for('static_content', group='styles', filename='styles.css')}}?v={{ get_css_version() }}" >
    {% if extra_stylesheets %}
      {% for m in extra_stylesheets %}
        <link rel="stylesheet" href="{{ m }}?ver={{ get_css_version() }}" >
      {% endfor %}
    {% endif %}

    <link rel="apple-touch-icon" sizes="180x180" href="{{url_for('static_content', group='favicons', filename='apple-touch-icon.png')}}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{url_for('static_content', group='favicons', filename='favicon-32x32.png')}}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{url_for('static_content', group='favicons', filename='favicon-16x16.png')}}">
    <link rel="manifest" href="{{url_for('static_content', group='favicons', filename='site.webmanifest')}}">
    <link rel="mask-icon" href="{{url_for('static_content', group='favicons', filename='safari-pinned-tab.svg')}}" color="#5bbad5">
    <link rel="shortcut icon" href="{{url_for('static_content', group='favicons', filename='favicon.ico')}}">
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="msapplication-config" content="favicons/browserconfig.xml">
    <meta name="theme-color" content="#ffffff">
    <script>
        const csrftoken="{{ csrf_token() }}";
        const socketio_url="{{ get_socketio_path() }}/socket.io";
        const is_authenticated = {% if current_user.is_authenticated or not has_password %}true{% else %}false{% endif %};
    </script>
    <script src="{{url_for('static_content', group='js', filename='jquery-3.6.0.min.js')}}"></script>
    <script src="{{url_for('static_content', group='js', filename='csrf.js')}}" defer></script>
    <script src="{{url_for('static_content', group='js', filename='feather-icons.min.js')}}" defer></script>
    {% if socket_io_enabled %}
    <script src="{{url_for('static_content', group='js', filename='socket.io.min.js')}}"></script>
    <script src="{{url_for('static_content', group='js', filename='realtime.js')}}" defer></script>
    {% endif %}
  </head>

  <body class="">
    <div class="header">
    <div class="pure-menu-fixed" style="width: 100%;">
      <div class="home-menu pure-menu pure-menu-horizontal" id="nav-menu">

        {% if has_password and not current_user.is_authenticated %}
          <a class="pure-menu-heading" href="https://changedetection.io" rel="noopener">
            <strong>Change</strong>Detection.io</a>
        {% else %}
          <a class="pure-menu-heading" href="{{url_for('watchlist.index')}}">
            <strong>Change</strong>Detection.io</a>
        {% endif %}
        {% if current_diff_url %}
          <a class="current-diff-url" href="{{ current_diff_url }}">
            <span style="max-width: 30%; overflow: hidden">{{ current_diff_url }}</span></a>
        {% else %}
          {% if new_version_available and not(has_password and not current_user.is_authenticated) %}
            <span id="new-version-text" class="pure-menu-heading">
              <a href="https://changedetection.io">A new version is available</a>
            </span>
          {% endif %}
        {% endif %}

        <ul class="pure-menu-list" id="top-right-menu">
          {% if current_user.is_authenticated or not has_password %}
            {% if not
            current_diff_url %}
              <li class="pure-menu-item">
                <a href="{{ url_for('tags.tags_overview_page')}}" class="pure-menu-link">GROUPS</a>
              </li>
              <li class="pure-menu-item">
                <a href="{{ url_for('settings.settings_page')}}" class="pure-menu-link">SETTINGS</a>
              </li>
              <li class="pure-menu-item">
                <a href="{{ url_for('imports.import_page')}}" class="pure-menu-link">IMPORT</a>
              </li>
              <li class="pure-menu-item">
                <a href="{{ url_for('backups.index')}}" class="pure-menu-link">BACKUPS</a>
              </li>
            {% else %}
              <li class="pure-menu-item">
                <a href="{{ url_for('ui.ui_edit.edit_page', uuid=uuid, next='diff') }}" class="pure-menu-link">EDIT</a>
              </li>
            {% endif %}
          {% else %}
            <li class="pure-menu-item">
              <a class="pure-menu-link" href="https://changedetection.io">Website Change Detection and Notification.</a>
            </li>
          {% endif %}
          {% if current_user.is_authenticated %}
            <li class="pure-menu-item">
              <a href="{{url_for('logout')}}" class="pure-menu-link">LOG OUT</a>
            </li>
          {% endif %}
          {% if current_user.is_authenticated or not has_password %}
          <li class="pure-menu-item pure-form" id="search-menu-item">
            <!-- We use GET here so it offers people a chance to set bookmarks etc -->
            <form name="searchForm" action="" method="GET">
              <input id="search-q" class="" name="q" placeholder="URL or Title {% if active_tag_uuid %}in '{{ active_tag.title }}'{% endif %}" required="" type="text" value="">
              <input name="tags" type="hidden" value="{% if active_tag_uuid %}{{active_tag_uuid}}{% endif %}">
              <button class="toggle-button " id="toggle-search" type="button" title="Search, or Use Alt+S Key" >
                {% include "svgs/search-icon.svg" %}
              </button>
            </form>
          </li>
          {% endif %}
          <li class="pure-menu-item">
            <button class="toggle-button" id ="toggle-light-mode" type="button" title="Toggle Light/Dark Mode">
              <span class="visually-hidden">Toggle light/dark mode</span>
              <span class="icon-light">
                {% include "svgs/light-mode-toggle-icon.svg" %}
              </span>
              <span class="icon-dark">
                {% include "svgs/dark-mode-toggle-icon.svg" %}
              </span>
            </button>
          </li>
          <li class="pure-menu-item" id="heart-us">
                <svg
                   fill="#ff0000"
                   class="bi bi-heart"
                   preserveAspectRatio="xMidYMid meet"
                   viewBox="0 0 16.9 16.1"
                   id="svg-heart"
                   xmlns="http://www.w3.org/2000/svg"
                   >
                  <path id="heartpath" d="M 5.338316,0.50302766 C 0.71136983,0.50647126 -3.9576371,7.2707777 8.5004254,15.503028 23.833425,5.3700277 13.220206,-2.5384409 8.6762066,1.6475589 c -0.060791,0.054322 -0.11943,0.1110064 -0.1757812,0.1699219 -0.057,-0.059 -0.1157813,-0.116875 -0.1757812,-0.171875 C 7.4724566,0.86129334 6.4060729,0.50223298 5.338316,0.50302766 Z"
                     style="fill:var(--color-background);fill-opacity:1;stroke:#ff0000;stroke-opacity:1" />
                </svg>

          </li>
          <li class="pure-menu-item">
            <a class="github-link" href="https://github.com/dgtlmoon/changedetection.io">
              {% include "svgs/github.svg" %}
            </a>
          </li>
        </ul>
      </div>
      <div id="pure-menu-horizontal-spinner"></div>
      </div>

    </div>


    {% if hosted_sticky %}
      <div class="sticky-tab" id="hosted-sticky">
        <a href="https://changedetection.io/?ref={{guid}}">Let us host your instance!</a>
      </div>
    {% endif %}
    {% if left_sticky %}
      <div class="sticky-tab" id="left-sticky">
        <a href="{{url_for('ui.ui_views.preview_page', uuid=uuid)}}">Show current snapshot</a><br>
          Visualise <strong>triggers</strong> and <strong>ignored text</strong>
      </div>
    {% endif %}
    {% if right_sticky %}
      <div class="sticky-tab" id="right-sticky">{{ right_sticky }}</div>
    {% endif %}
    <section class="content">
        <div id="overlay">
            <div class="content">
                <h4>Try our Chrome extension</h4>
                <p>
                    <a id="chrome-extension-link"
                       title="Chrome Extension - Web Page Change Detection with changedetection.io!"
                       href="https://chromewebstore.google.com/detail/changedetectionio-website/kefcfmgmlhmankjmnbijimhofdjekbop">
                        <img alt="Chrome store icon" src="{{url_for('static_content', group='images', filename='google-chrome-icon.png')}}">
                        Chrome Webstore
                    </a>
                </p>
                Easily add the current web-page from your browser directly into your changedetection.io tool, more great features coming soon!
                <h4>Changedetection.io needs your support!</h4>
                <p>
                    You can help us by supporting changedetection.io on these platforms;
                </p>
                <p>
                <ul>
                    <li>
                        <a href="https://alternativeto.net/software/changedetection-io/about/" title="Web page change detection at alternativeto.net">Rate us at
                        AlternativeTo.net</a>
                    </li>
                <li>
                    <a href="https://github.com/dgtlmoon/changedetection.io" title="Web page change detection on GitHub">Star us on GitHub</a>
                </li>
                <li>
                    <a rel="nofollow" href="https://twitter.com/change_det_io" title="Web page change detection on Twitter">Follow us at Twitter/X</a>
                </li>
                <li>
                    <a rel="nofollow" href="https://www.g2.com/products/changedetection-io/reviews" title="Web page change detection reviews at G2">G2 Software reviews</a>
                </li>
                <li>
                    <a rel="nofollow" href="https://www.linkedin.com/company/changedetection-io" title="Visit web page change detection at LinkedIn">Check us out on LinkedIn</a>
                </li>
                <li>
                    And tell your friends and colleagues :)
                </li>
                </ul>
                <p>
                    The more popular changedetection.io is, the more time we can dedicate to adding amazing features!
                </p>
                <p>
                    Many thanks :)<br>
                </p>
                <p>
                    <i>changedetection.io team</i>
                </p>
            </div>
        </div>
        <header>
        {% block header %}{% endblock %}
      </header>

      {% with messages = get_flashed_messages(with_categories = true) %}
      {% if
      messages %}
        <ul class="messages">
          {% for category, message in messages %}
            <li class="{{ category }}">{{ message }}</li>
          {% endfor %}
        </ul>
      {% endif %}
      {% endwith %}
      {% if session['share-link'] %}
        <ul class="messages with-share-link">
          <li class="message">
            Share this link:
            <span id="share-link">{{ session['share-link'] }}</span>
            <img style="height: 1em; display: inline-block" src="{{url_for('static_content', group='images', filename='copy.svg')}}" >
          </li>
        </ul>
      {% endif %}
      {% block content %}{% endblock %}
    </section>
    <script src="{{url_for('static_content', group='js', filename='toggle-theme.js')}}" defer></script>

    <div id="checking-now-fixed-tab" style="display: none;"><span class="spinner"></span><span>&nbsp;Checking now</span></div>
    <div id="realtime-conn-error" style="display:none">Offline</div>
  </body>

</html>
