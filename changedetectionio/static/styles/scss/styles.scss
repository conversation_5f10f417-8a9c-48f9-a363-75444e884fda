/*
 * -- BASE STYLES --
 */

@use "parts/variables";
@use "parts/arrows";
@use "parts/browser-steps";
@use "parts/extra_proxies";
@use "parts/extra_browsers";
@use "parts/pagination";
@use "parts/spinners";
@use "parts/darkmode";
@use "parts/menu";
@use "parts/love";
@use "parts/preview_text_filter";
@use "parts/watch_table";
@use "parts/watch_table-mobile";
@use "parts/edit";
@use "parts/conditions_table";
@use "parts/lister_extra";
@use "parts/socket";
@use "parts/visualselector";


body {
  color: var(--color-text);
  background: var(--color-background-page);
  font-family: Helvetica Neue, Helvetica, Lucida Grande, Arial, Ubuntu, Cantarell, Fira Sans, sans-serif;
}

.visually-hidden {
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

// Row icons like chrome, pdf, share, etc
.status-icon {
  display: inline-block;
  height: 1rem;
  vertical-align: middle;
}

.pure-table-even {
  background: var(--color-background);
}

/* Some styles from https://css-tricks.com/ */
a {
  text-decoration: none;
  color: var(--color-link);
}

a.github-link {
  color: var(--color-icon-github);
  margin: 0 1rem 0 0.5rem;

  svg {
    fill: currentColor;
  }

  &:hover {
    color: var(--color-icon-github-hover);
  }
}

#search-q {
  opacity: 0;
  -webkit-transition: all .9s ease;
  -moz-transition: all .9s ease;
  transition: all .9s ease;
  width: 0;
  display: none;
  &.expanded {
    width: auto;
    display: inline-block;

    opacity: 1;
  }
}
#search-result-info {
  color: #fff;
}

button.toggle-button {
  vertical-align: middle;
  background: transparent;
  border: none;
  cursor: pointer;

  color: var(--color-icon-github);

  &:hover {
    color: var(--color-icon-github-hover);
  }

  svg {
    fill: currentColor;
  }

  .icon-light {
    display: block;
  }


}

.pure-menu-horizontal {
  background: var(--color-background);
  padding: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#pure-menu-horizontal-spinner {
  height: 3px;
  background: linear-gradient(-75deg, #ff6000, #ff8f00, #ffdd00, #ed0000);
  background-size: 400% 400%;
  width: 100%;
  animation: gradient 200s ease infinite;
}

body.spinner-active {
  #pure-menu-horizontal-spinner {
    animation: gradient 1s ease infinite;
  }
}

@keyframes gradient {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}
.pure-menu-heading {
  color: var(--color-text-menu-heading);
}

.pure-menu-link {
  color: var(--color-text-menu-link);

  &:hover {
    background-color: var(--color-background-menu-link-hover);
    color: var(--color-text-menu-link-hover);
  }
}


.tab-pane-inner {
  // .tab-pane-inner will have the #id that the tab button jumps/anchors to
  scroll-margin-top: 200px;
}

section.content {
  padding-top: 100px;
  padding-bottom: 1em;
  flex-direction: column;
  display: flex;
  align-items: center;
  justify-content: center;
}

code {
  background: var(--color-background-code);
  color: var(--color-text);
}

.inline-tag {
  white-space: nowrap;
  border-radius: 5px;
  padding: 2px 5px;
  margin-right: 4px;
}

.watch-tag-list {
  color: var(--color-white);
  background: var(--color-text-watch-tag-list);
  @extend .inline-tag;
}

@media (min-width: 768px) {
  .box {
    margin: 0 1em !important;
  }
}

.box {
  max-width: 100%;
  margin: 0 0.3em;
  flex-direction: column;
  display: flex;
  justify-content: center;
}


#post-list-buttons {
  text-align: right;
  padding: 0px;
  margin: 0px;

  li {
    display: inline-block;
  }

  a {
    border-top-left-radius: initial;
    border-top-right-radius: initial;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
  }
}


body:after {
  content: "";
  background: linear-gradient(130deg, var(--color-background-gradient-first), var(--color-background-gradient-second) 41.07%, var(--color-background-gradient-third) 84.05%);
}

body:after,
body:before {
  display: block;
  height: 650px;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: -1;
}

body::after {
  opacity: 0.91;
}

body::before {
  // background-image set in base.html so it works with reverse proxies etc
  content: "";
}

body:after,
body:before {
  -webkit-clip-path: polygon(100% 0, 0 0, 0 77.5%, 1% 77.4%, 2% 77.1%, 3% 76.6%, 4% 75.9%, 5% 75.05%, 6% 74.05%, 7% 72.95%, 8% 71.75%, 9% 70.55%, 10% 69.3%, 11% 68.05%, 12% 66.9%, 13% 65.8%, 14% 64.8%, 15% 64%, 16% 63.35%, 17% 62.85%, 18% 62.6%, 19% 62.5%, 20% 62.65%, 21% 63%, 22% 63.5%, 23% 64.2%, 24% 65.1%, 25% 66.1%, 26% 67.2%, 27% 68.4%, 28% 69.65%, 29% 70.9%, 30% 72.15%, 31% 73.3%, 32% 74.35%, 33% 75.3%, 34% 76.1%, 35% 76.75%, 36% 77.2%, 37% 77.45%, 38% 77.5%, 39% 77.3%, 40% 76.95%, 41% 76.4%, 42% 75.65%, 43% 74.75%, 44% 73.75%, 45% 72.6%, 46% 71.4%, 47% 70.15%, 48% 68.9%, 49% 67.7%, 50% 66.55%, 51% 65.5%, 52% 64.55%, 53% 63.75%, 54% 63.15%, 55% 62.75%, 56% 62.55%, 57% 62.5%, 58% 62.7%, 59% 63.1%, 60% 63.7%, 61% 64.45%, 62% 65.4%, 63% 66.45%, 64% 67.6%, 65% 68.8%, 66% 70.05%, 67% 71.3%, 68% 72.5%, 69% 73.6%, 70% 74.65%, 71% 75.55%, 72% 76.35%, 73% 76.9%, 74% 77.3%, 75% 77.5%, 76% 77.45%, 77% 77.25%, 78% 76.8%, 79% 76.2%, 80% 75.4%, 81% 74.45%, 82% 73.4%, 83% 72.25%, 84% 71.05%, 85% 69.8%, 86% 68.55%, 87% 67.35%, 88% 66.2%, 89% 65.2%, 90% 64.3%, 91% 63.55%, 92% 63%, 93% 62.65%, 94% 62.5%, 95% 62.55%, 96% 62.8%, 97% 63.3%, 98% 63.9%, 99% 64.75%, 100% 65.7%);
  clip-path: polygon(100% 0, 0 0, 0 77.5%, 1% 77.4%, 2% 77.1%, 3% 76.6%, 4% 75.9%, 5% 75.05%, 6% 74.05%, 7% 72.95%, 8% 71.75%, 9% 70.55%, 10% 69.3%, 11% 68.05%, 12% 66.9%, 13% 65.8%, 14% 64.8%, 15% 64%, 16% 63.35%, 17% 62.85%, 18% 62.6%, 19% 62.5%, 20% 62.65%, 21% 63%, 22% 63.5%, 23% 64.2%, 24% 65.1%, 25% 66.1%, 26% 67.2%, 27% 68.4%, 28% 69.65%, 29% 70.9%, 30% 72.15%, 31% 73.3%, 32% 74.35%, 33% 75.3%, 34% 76.1%, 35% 76.75%, 36% 77.2%, 37% 77.45%, 38% 77.5%, 39% 77.3%, 40% 76.95%, 41% 76.4%, 42% 75.65%, 43% 74.75%, 44% 73.75%, 45% 72.6%, 46% 71.4%, 47% 70.15%, 48% 68.9%, 49% 67.7%, 50% 66.55%, 51% 65.5%, 52% 64.55%, 53% 63.75%, 54% 63.15%, 55% 62.75%, 56% 62.55%, 57% 62.5%, 58% 62.7%, 59% 63.1%, 60% 63.7%, 61% 64.45%, 62% 65.4%, 63% 66.45%, 64% 67.6%, 65% 68.8%, 66% 70.05%, 67% 71.3%, 68% 72.5%, 69% 73.6%, 70% 74.65%, 71% 75.55%, 72% 76.35%, 73% 76.9%, 74% 77.3%, 75% 77.5%, 76% 77.45%, 77% 77.25%, 78% 76.8%, 79% 76.2%, 80% 75.4%, 81% 74.45%, 82% 73.4%, 83% 72.25%, 84% 71.05%, 85% 69.8%, 86% 68.55%, 87% 67.35%, 88% 66.2%, 89% 65.2%, 90% 64.3%, 91% 63.55%, 92% 63%, 93% 62.65%, 94% 62.5%, 95% 62.55%, 96% 62.8%, 97% 63.3%, 98% 63.9%, 99% 64.75%, 100% 65.7%)
}

.button-small {
  font-size: 85%;
}

.button-xsmall {
  font-size: 70%;
}

.fetch-error {
  padding-top: 1em;
  font-size: 80%;
  max-width: 400px;
  display: block;
}

.pure-button-primary,
a.pure-button-primary,
.pure-button-selected,
a.pure-button-selected {
  background-color: var(--color-background-button-primary);
}

.button-secondary {
  color: var(--color-text-button);
  border-radius: 4px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.button-success {
  background: var(--color-background-button-success);
}

.button-tag {
  background: var(--color-background-button-tag);
  color: var(--color-text-button);
  font-size: 65%;
  border-bottom-left-radius: initial;
  border-bottom-right-radius: initial;
  margin-right: 4px;
  &.active {
    background: var(--color-background-button-tag-active);
    font-weight: bold;
  }

}

.button-error {
  background: var(--color-background-button-error);
  color: var(--color-text-button-error);
}

.button-warning {
  background: var(--color-background-button-warning);
  color: var(--color-text-button-warning);
}

.button-secondary {
  background: var(--color-background-button-secondary);
}

.button-cancel {
  background: var(--color-background-button-cancel);
}

.messages {
  li {
    list-style: none;
    padding: 1em;
    border-radius: 10px;
    color: var(--color-text-messages);
    font-weight: bold;

    &.message {
      background: var(--color-background-messages-message);
    }

    &.error {
      background: var(--color-background-messages-error);
    }

    &.notice {
      background: var(--color-background-messages-notice);
    }
  }

  &.with-share-link {
    >*:hover {
      cursor: pointer;
    }
  }
}

.notifications-wrapper {
  padding-top: 0.5rem;
  #notification-test-log {
    padding-top: 1rem;
    white-space: pre-wrap;
    word-break: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    box-sizing: border-box;
  }
}

label {
 &:hover {
   cursor: pointer;
 }  
}

#notification-customisation {
  border: 1px solid var(--color-border-notification);
  padding: 0.5rem;
  border-radius: 5px;
}

#notification-error-log {
  border: 1px solid var(--color-border-notification);
  padding: 1rem;
  border-radius: 5px;
  overflow-wrap: break-word;
}

#token-table {

  &.pure-table td,
  &.pure-table th {
    font-size: 80%;
  }
}

// Some field colouring for transperant field
.pure-form input[type=text].transparent-field {
  background-color:  var(--color-background-new-watch-input-transparent) !important;
  color: var(--color-white) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  &::placeholder {
    opacity: 0.5;
    color: rgba(255, 255, 255, 0.7);
    font-weight: lighter;
  }
}

#new-watch-form {
  background: var(--color-background-new-watch-form);
  padding: 1em;
  border-radius: 10px;
  margin-bottom: 1em;
  max-width: 100%;

  #url {
    &::placeholder {
      font-weight: bold;
    }
  }

  input {
    display: inline-block;
    margin-bottom: 5px;
  }

  input:not(.pure-button) {
    background-color: var(--color-background-new-watch-input);
    color: var(--color-text-new-watch-input);
  }

  .label {
    display: none;
  }

  legend {
    color: var(--color-text-legend);
    font-weight: bold;
  }


  #watch-add-wrapper-zone {
    @media only screen and (min-width: 760px) {
      display: flex;
      gap: 0.3rem;
      flex-direction: row;
      min-width: 70vw;
    }
    /* URL field grows always, other stay static in width */
    > span {
      flex-grow: 0;

      input {
        width: 100%;
        padding-right: 1em;
      }

      &:first-child {
        flex-grow: 1;
      }
    }

    @media only screen and (max-width: 760px) {
      #url {
        width: 100%;
      }
    }
  }

  #watch-group-tag {
    font-size: 0.9rem;
    padding: 0.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--color-white);
    label, input {
      margin: 0;
    }

    input {
      flex: 1;
    }
  }
}


#diff-col {
  padding-left: 40px;
}

#diff-jump {
  position: fixed;
  left: 0px;
  top: 120px;
  background: var(--color-background);
  padding: 10px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  box-shadow: 1px 1px 4px var(--color-shadow-jump);

  a {
    color: var(--color-link);
    cursor: pointer;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -o-user-select: none;
  }
}

footer {
  padding: 10px;
  background: var(--color-background);
  color: var(--color-text-footer);
  text-align: center;
}

#feed-icon {
  vertical-align: middle;
}

#top-right-menu {
  // Just let flex overflow the x axis for now
  /*
      position: absolute;
      right: 0px;
      background: linear-gradient(to right, #fff0, #fff 10%);
      padding-left: 20px;
      padding-right: 10px;
      */
}

.sticky-tab {
  position: absolute;
  top: 60px;
  font-size: 65%;
  background: var(--color-background);
  padding: 10px;

  &#left-sticky {
    left: 0;
    position: fixed;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    box-shadow: 1px 1px 4px var(--color-shadow-jump);
  }

  &#right-sticky {
    right: 0px;
  }

  &#hosted-sticky {
    right: 0px;
    top: 100px;
    font-weight: bold;
  }
}

#new-version-text a {
  color: var(--color-link-new-version);
}

.watch-controls {
  color: #f8321b;

  .state-on {
    img {
      opacity: 0.8;
    }
  }

  /* default */
  img {
    opacity: 0.2;
  }

  img {
    &:hover {
      transition: opacity 0.3s;
      opacity: 0.8;
    }
  }
}

.monospaced-textarea {
  textarea {
    width: 100%;
    font-family: monospace;
    white-space: pre;
    overflow-wrap: normal;
    // No scrollbars until needed.
    overflow-x: auto;
  }
}


.pure-form {
  fieldset {
    padding-top: 0px;

    ul {
      padding-bottom: 0px;
      margin-bottom: 0px;
    }
  }

  .pure-control-group,
  .pure-group,
  .pure-controls {
    padding-bottom: 1em;

    div {
      margin: 0px;
    }

    .checkbox {
      >* {
        display: inline;
        vertical-align: middle;
      }

      >label {
        padding-left: 5px;
      }
    }

    legend {
      color: var(--color-text-legend);
    }
  }

  /* The input fields with errors */
  .error {
    input {
      background-color: var(--color-error-input);
    }
  }

  /* The list of errors */
  ul.errors {
    padding: .5em .6em;
    border: 1px solid var(--color-error-list);
    border-radius: 4px;
    vertical-align: middle;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;

    li {
      margin-left: 1em;
      color: var(--color-error-list);
    }
  }

  label {
    font-weight: bold;
  }

  textarea {
    width: 100%;
  }

  .inline-radio {
    ul {
      margin: 0px;
      list-style: none;

      li {
        display: flex;
        align-items: center;
        gap: 1em;
      }
    }
  }
}


@media only screen and (max-width: 760px),
(min-device-width: 768px) and (max-device-width: 1024px) {
  .edit-form {
    padding: 0.5em;
    margin: 0;
  }

  #nav-menu {
    overflow-x: scroll;
  }
}


@media only screen and (max-width: 760px),
(min-device-width: 768px) and (max-device-width: 800px) {

  div.sticky-tab#hosted-sticky {
    top: 60px;
    left: 0px;
    right: auto;
  }

  section.content {
    padding-top: 110px;
  }

  // Make the tabs easier to hit, they will be all nice and horizontal
  div.tabs.collapsable ul li {
    display: block;
    border-radius: 0px;
    margin-right: 0px;
  }

  input[type='text'] {
    width: 100%;
  }

}

.pure-table {
  border-color: var(--color-border-table-cell);

  thead {
    background-color: var(--color-background-table-thead);
    color: var(--color-text);
    border-bottom: 1px solid var(--color-background-table-thead);
  }

  td,
  th {
    border-left-color: var(--color-border-table-cell);
  }
}

.pure-table-striped {
  tr:nth-child(2n-1) {
    td {
      background-color: var(--color-table-stripe);
    }
  }
}

.pure-form input[type=color],
.pure-form input[type=date],
.pure-form input[type=datetime-local],
.pure-form input[type=datetime],
.pure-form input[type=email],
.pure-form input[type=month],
.pure-form input[type=number],
.pure-form input[type=password],
.pure-form input[type=search],
.pure-form input[type=tel],
.pure-form input[type=text],
.pure-form input[type=time],
.pure-form input[type=url],
.pure-form input[type=week],
.pure-form select,
.pure-form textarea {
  border: var(--color-border-input);
  box-shadow: inset 0 1px 3px var(--color-shadow-input);
  background-color: var(--color-background-input);
  color: var(--color-text-input);

  &:active {
    background-color: var(--color-background-input);
  }
}

input::placeholder,
textarea::placeholder {
  color: var(--color-text-input-placeholder);
}


/** Desktop vs mobile input field strategy
- We dont use 'size' with <input> because `size` is too unreliable to override, and will often push-out
- Rely always on width in CSS
*/
/** Set max width for input field */
.m-d {
  min-width: 100%;
}

@media only screen and (min-width: 761px) {

  /* m-d is medium-desktop */
  .m-d {
    min-width: 80%;
  }
}


.tabs {
  ul {
    margin: 0px;
    padding: 0px;
    display: block;

    li {
      margin-right: 3px;
      display: inline-block;
      color: var(--color-text-tab);
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      background-color: var(--color-background-tab);

      &:not(.active) {
        &:hover {
          background-color: var(--color-background-tab-hover);
        }
      }

      &.active,
      :target {
        background-color: var(--color-background);

        a {
          color: var(--color-text-tab-active);
          font-weight: bold;
        }
      }

      a {
        display: block;
        padding: 0.8em;
        color: var(--color-text-tab);
      }
    }
  }
}

$form-edge-padding: 20px;

.pure-form-stacked {
  >div:first-child {
    display: block;
  }
}

.login-form {
  .inner {
    background: var(--color-background);
    ;
    padding: $form-edge-padding;
    border-radius: 5px;
  }
}

.tab-pane-inner {

  &:not(:target) {
    display: none;
  }

  &:target {
    display: block;
  }

  // doesnt need padding because theres another row of buttons/activity
  padding: 0px;
}

.beta-logo {
  height: 50px;
  // looks better when it's hanging off a little
  right: -3px;
  top: -3px;
  position: absolute;
}

#selector-header {
  padding-bottom: 1em;
}

body.full-width {
  .edit-form {
    width: 95%;
  }
}

.edit-form {
  min-width: 70%;
  /* so it cant overflow */
  max-width: 95%;

  .box-wrap {
    position: relative;
  }

  .inner {
    background: var(--color-background);
    padding: $form-edge-padding;
  }

  #actions {
    display: block;
    background: var(--color-background);
  }

  /* Make action buttons have consistent size and spacing */
  #actions .pure-control-group {
    display: flex;
    gap: 0.625em;
    flex-wrap: wrap;
  }

  .pure-form-message-inline {
    padding-left: 0;
    color: var(--color-text-input-description);
    code {
      font-size: .875em;
    }
  }
}

.border-fieldset {
  h3 {
    margin-top: 0;
  }
  border: 1px solid #ccc;
  padding: 1rem;
  border-radius: 5px;
  margin-bottom: 1rem;
  fieldset:last-of-type {
    padding-bottom: 0;
    .pure-control-group {
      padding-bottom: 0;
    }
  }
}



ul {
  padding-left: 1em;
  padding-top: 0px;
  margin-top: 4px;
}

.time-check-widget {
  tr {
    display: inline;

    input[type="number"] {
      width: 5em;
    }
  }
}

@media only screen and (max-width: 760px) {
  .time-check-widget {
    tbody {
      display: grid;
      grid-template-columns: auto 1fr auto 1fr;
      gap: 0.625em 0.3125em;
      align-items: center;
    }    
    tr {
      display: contents; 
      th {
        text-align: right;
        padding-right: 5px;
      }
      input[type="number"] {
        width: 100%;
        max-width: 5em;
      }
    }
  }
}

#webdriver_delay {
    width: 5em;
}

#api-key {
  &:hover {
    cursor: pointer;
  }
}

#api-key-copy {
  color: var(--color-api-key);
}

.button-green {
  background-color: var(--color-background-button-green);
}

.button-red {
  background-color: var(--color-background-button-red);
}

.noselect {
  -webkit-touch-callout: none;
  /* iOS Safari */
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Old versions of Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  /* Non-prefixed version, currently
    supported by Chrome, Edge, Opera and Firefox */
}

.snapshot-age {
  padding: 4px;
  margin: 0.5rem 0;
  background-color: var(--color-background-snapshot-age);
  border-radius: 3px;
  font-weight: bold;
  margin-bottom: 4px;

  &.error {
    background-color: var(--color-error-background-snapshot-age);
    color: var(--color-error-text-snapshot-age);
  }
}

#checkbox-operations {
  background: var(--color-background-checkbox-operations);
  padding: 1em;
  border-radius: 10px;
  margin-bottom: 1em;
  display: none;
  button {
    /* some space if they wrap the page */
    margin-bottom: 3px;
    margin-top: 3px;
    /* vertically center icon and text */
    display: inline-flex;
    align-items: center;
  }
}

.checkbox-uuid {
  >* {
    vertical-align: middle;
  }
}

.inline-warning {
  >span {
    display: inline-block;
    vertical-align: middle;
  }

  img.inline-warning-icon {
    display: inline;
    height: 26px;
    vertical-align: middle;
  }

  border: 1px solid var(--color-border-warning);
  padding: 0.5rem;
  border-radius: 5px;
  color: var(--color-warning);
}

/* automatic price following helpers */
.tracking-ldjson-price-data {
  background-color: var(--color-background-button-green);
  color: #000;
  opacity: 0.6;
  @extend .inline-tag;
}

.ldjson-price-track-offer {
  a.pure-button {
    border-radius: 3px;
    padding: 3px;
    background-color: var(--color-background-button-green);
  }

  font-weight: bold;
  font-style: italic;
}

.price-follow-tag-icon {
  display: inline-block;
  height: 0.8rem;
  vertical-align: middle;
}


#quick-watch-processor-type {
  ul#processor {
    color: #fff;
    padding-left: 0px;
    li {
      list-style: none;
      font-size: 0.9rem;
      display: grid;
      grid-template-columns: auto 1fr;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
    }
  }
  label, input {
    padding: 0;
    margin: 0;
  }
}

.restock-label {
  &.in-stock {
    background-color: var(--color-background-button-green);
    color: #fff;
  }
  &.not-in-stock {
    background-color: var(--color-background-button-cancel);
    color: #777;
  }
  &.error {
    background-color: var(--color-background-button-error);
    color: #fff;
    opacity: 0.7;
  }

  svg {
    vertical-align: middle;
  }

  @extend .inline-tag;
}

#chrome-extension-link {
  img {
    height: 21px;
    padding: 2px;
    vertical-align: middle;
  }

  padding: 9px;
  border: 1px solid var(--color-grey-800);
  border-radius: 10px;
  vertical-align: middle;
}

#realtime-conn-error {
  position: absolute;
  bottom: 0;
  left: 30px;
  background: var(--color-warning);
  padding: 10px;
  font-size: 0.8rem;
  color: #fff;
}
