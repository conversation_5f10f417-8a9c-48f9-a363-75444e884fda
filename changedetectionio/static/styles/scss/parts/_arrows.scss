.arrow {
  border: solid #1b98f8;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;

  &.right {
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
  }

  &.left {
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
  }

  &.up, &.asc {
    transform: rotate(-135deg);
    -webkit-transform: rotate(-135deg);
  }

  &.down, &.desc {
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
  }
}
