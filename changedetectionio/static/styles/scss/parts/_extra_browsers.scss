ul#requests-extra_browsers {
  list-style: none;
  /* tidy up the table to look more "inline" */
  li {
    > label {
      display: none;
    }

  }

  /* each proxy entry is a `table` */
  table {
    tr {
      display: table-row; // default display for small screens
      input[type=text] {
        width: 100%;
      }
    }
  }
  
  // apply inline display for larger screens
  @media only screen and (min-width: 1280px) {
    table {
      tr {
        display: inline;
        input[type=text] {
          width: 100%;
        }
      }
    }
  }
}

#extra-browsers-setting {
  border: 1px solid var(--color-grey-800);
  border-radius: 4px;
  margin: 1em;
   padding: 1em;
}