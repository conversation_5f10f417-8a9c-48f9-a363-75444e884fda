<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns="http://www.w3.org/2000/svg"
   version="1.1"
   id="Capa_1"
   x="0px"
   y="0px"
   viewBox="0 0 15 14.998326"
   xml:space="preserve"
   width="15"
   height="14.998326"><metadata
   id="metadata39"><rdf:RDF><cc:Work
       rdf:about=""><dc:format>image/svg+xml</dc:format><dc:type
         rdf:resource="http://purl.org/dc/dcmitype/StillImage" /><dc:title></dc:title></cc:Work></rdf:RDF></metadata><defs
   id="defs37" />
<path
   id="path2"
   style="fill:#1b98f8;fill-opacity:1;stroke-width:0.0292893"
   d="M 7.4975161,6.5052867e-4 C 4.549072,-0.04028702 1.7055675,1.8548221 0.58868606,4.5801341 -0.57739762,7.2574642 0.02596981,10.583326 2.069916,12.671949 4.0364753,14.788409 7.2763651,15.56067 9.989207,14.57284 12.801145,13.617602 14.87442,10.855325 14.985833,7.8845744 15.172496,4.9966544 13.49856,2.1100704 10.911002,0.8209349 9.8598067,0.28073592 8.6791261,-0.00114855 7.4975161,6.5052867e-4 Z M 6.5602569,10.251923 c -0.00509,0.507593 -0.5693885,0.488472 -0.9352002,0.468629 -0.3399386,0.0018 -0.8402048,0.07132 -0.9297965,-0.374189 -0.015842,-1.8973128 -0.015872,-3.7979649 0,-5.6952784 0.1334405,-0.5224315 0.7416869,-0.3424086 1.1377562,-0.374189 0.3969969,-0.084515 0.8245634,0.1963256 0.7272405,0.6382917 0,1.7789118 0,3.5578239 0,5.3367357 z m 3.7490371,0 c -0.0051,0.507593 -0.5693888,0.488472 -0.9352005,0.468629 -0.3399386,0.0018 -0.8402048,0.07132 -0.9297965,-0.374189 -0.015842,-1.8973128 -0.015872,-3.7979649 0,-5.6952784 0.1334405,-0.5224315 0.7416869,-0.3424086 1.1377562,-0.374189 0.3969969,-0.084515 0.8245638,0.1963256 0.7272408,0.6382917 0,1.7789118 0,3.5578239 0,5.3367357 z" />
<g
   id="g4"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g6"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g8"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g10"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g12"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g14"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g16"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g18"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g20"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g22"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g24"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g26"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g28"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g30"
   transform="translate(-0.01903604,0.02221043)">
</g>
<g
   id="g32"
   transform="translate(-0.01903604,0.02221043)">
</g>
</svg>
