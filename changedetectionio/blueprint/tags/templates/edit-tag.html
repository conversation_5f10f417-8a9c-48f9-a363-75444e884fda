{% extends 'base.html' %}
{% block content %}
{% from '_helpers.html' import render_field, render_checkbox_field, render_button %}
{% from '_common_fields.html' import render_common_settings_form %}
<script>
    const notification_base_url="{{url_for('ui.ui_notification.ajax_callback_send_notification_test', mode="group-settings")}}";
</script>

<script src="{{url_for('static_content', group='js', filename='tabs.js')}}" defer></script>
<script>

/*{% if emailprefix %}*/
    /*const email_notification_prefix=JSON.parse('{{ emailprefix|tojson }}');*/
/*{% endif %}*/

{% set has_tag_filters_extra='' %}

</script>

<script src="{{url_for('static_content', group='js', filename='watch-settings.js')}}" defer></script>
<script src="{{url_for('static_content', group='js', filename='notifications.js')}}" defer></script>

<div class="edit-form monospaced-textarea">

    <div class="tabs collapsable">
        <ul>
            <li class="tab" id=""><a href="#general">General</a></li>
            <li class="tab"><a href="#filters-and-triggers">Filters &amp; Triggers</a></li>
            {% if extra_tab_content %}
            <li class="tab"><a href="#extras_tab">{{ extra_tab_content }}</a></li>
            {% endif %}
            <li class="tab"><a href="#notifications">Notifications</a></li>
        </ul>
    </div>

    <div class="box-wrap inner">
        <form class="pure-form pure-form-stacked"
              action="{{ url_for('tags.form_tag_edit', uuid=data.uuid) }}" method="POST">
             <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

            <div class="tab-pane-inner" id="general">
                <fieldset>
                    <div class="pure-control-group">
                        {{ render_field(form.title, placeholder="https://...", required=true, class="m-d") }}
                    </div>
                </fieldset>
            </div>

            <div class="tab-pane-inner" id="filters-and-triggers">
                <p>These settings are <strong><i>added</i></strong> to any existing watch configurations.</p>
                {% include "edit/include_subtract.html" %}
                <div class="text-filtering border-fieldset">
                    <h3>Text filtering</h3>
                    {% include "edit/text-options.html" %}
                </div>
            </div>

        {# rendered sub Template #}
        {% if extra_form_content %}
            <div class="tab-pane-inner" id="extras_tab">
            {{ extra_form_content|safe }}
            </div>
        {% endif %}
            <div class="tab-pane-inner" id="notifications">
                <fieldset>
                    <div  class="pure-control-group inline-radio">
                      {{ render_checkbox_field(form.notification_muted) }}
                    </div>
                    {% if 1 %}
                    <div class="pure-control-group inline-radio">
                      {{ render_checkbox_field(form.notification_screenshot) }}
                        <span class="pure-form-message-inline">
                            <strong>Use with caution!</strong> This will easily fill up your email storage quota or flood other storages.
                        </span>
                    </div>
                    {% endif %}
                    <div class="field-group" id="notification-field-group">
                        {% if has_default_notification_urls %}
                        <div class="inline-warning">
                            <img class="inline-warning-icon" src="{{url_for('static_content', group='images', filename='notice.svg')}}" alt="Look out!" title="Lookout!" >
                            There are <a href="{{ url_for('settings.settings_page')}}#notifications">system-wide notification URLs enabled</a>, this form will override notification settings for this watch only &dash; an empty Notification URL list here will still send notifications.
                        </div>
                        {% endif %}
                        <a href="#notifications" id="notification-setting-reset-to-default" class="pure-button button-xsmall" style="right: 20px; top: 20px; position: absolute; background-color: #5f42dd; border-radius: 4px; font-size: 70%; color: #fff">Use system defaults</a>

                        {{ render_common_settings_form(form, emailprefix, settings_application, extra_notification_token_placeholder_info) }}
                    </div>
                </fieldset>
            </div>

            <div id="actions">
                <div class="pure-control-group">
                    {{ render_button(form.save_button) }}
                </div>
            </div>
        </form>
    </div>
</div>

{% endblock %}
