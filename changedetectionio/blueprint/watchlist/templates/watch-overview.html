{%- extends 'base.html' -%}
{%- block content -%}
{%- from '_helpers.html' import render_simple_field, render_field, render_nolabel_field, sort_by_title -%}
<script src="{{url_for('static_content', group='js', filename='jquery-3.6.0.min.js')}}"></script>
<script src="{{url_for('static_content', group='js', filename='watch-overview.js')}}" defer></script>
<script>let nowtimeserver={{ now_time_server }};</script>
<script>let favicon_baseURL="{{ url_for('static_content', group='favicon', filename="PLACEHOLDER")}}";</script>
<script>
// Initialize Feather icons after the page loads
document.addEventListener('DOMContentLoaded', function() {
    feather.replace();
});
</script>
<style>
.checking-now .last-checked {
    background-image: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.05) 40%, rgba(0,0,0,0.1) 100%);
    background-size: 0 100%;
    background-repeat: no-repeat;
    transition: background-size 0.9s ease
}
</style>
<div class="box" id="form-quick-watch-add">

    <form class="pure-form" action="{{ url_for('ui.ui_views.form_quick_watch_add', tag=active_tag_uuid) }}" method="POST" id="new-watch-form">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" >
        <fieldset>
            <legend>Add a new web page change detection watch</legend>
            <div id="watch-add-wrapper-zone">
                    {{ render_nolabel_field(form.url, placeholder="https://...", required=true) }}
                    {{ render_nolabel_field(form.watch_submit_button, title="Watch this URL!" ) }}
                    {{ render_nolabel_field(form.edit_and_watch_submit_button, title="Edit first then Watch") }}
            </div>
            <div id="watch-group-tag">
               {{ render_field(form.tags, value=active_tag.title if active_tag_uuid else '', placeholder="Watch group / tag", class="transparent-field") }}
            </div>
            <div id="quick-watch-processor-type">
                {{ render_simple_field(form.processor) }}
            </div>

        </fieldset>
        <span style="color:#eee; font-size: 80%;"><img alt="Create a shareable link" style="height: 1em;display:inline-block;" src="{{url_for('static_content', group='images', filename='spread-white.svg')}}" > Tip: You can also add 'shared' watches. <a href="https://github.com/dgtlmoon/changedetection.io/wiki/Sharing-a-Watch">More info</a></span>
    </form>
</div>
<div class="box">
    <form class="pure-form" action="{{ url_for('ui.form_watch_list_checkbox_operations') }}" method="POST" id="watch-list-form">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" >
    <input type="hidden" id="op_extradata" name="op_extradata" value="" >
    <div id="checkbox-operations">
        <button class="pure-button button-secondary button-xsmall"  name="op" value="pause"><i data-feather="pause" style="width: 14px; height: 14px; stroke: white; margin-right: 4px;"></i>Pause</button>
        <button class="pure-button button-secondary button-xsmall"  name="op" value="unpause"><i data-feather="play" style="width: 14px; height: 14px; stroke: white; margin-right: 4px;"></i>UnPause</button>
        <button class="pure-button button-secondary button-xsmall"  name="op" value="mute"><i data-feather="volume-x" style="width: 14px; height: 14px; stroke: white; margin-right: 4px;"></i>Mute</button>
        <button class="pure-button button-secondary button-xsmall"  name="op" value="unmute"><i data-feather="volume-2" style="width: 14px; height: 14px; stroke: white; margin-right: 4px;"></i>UnMute</button>
        <button class="pure-button button-secondary button-xsmall" name="op" value="recheck"><i data-feather="refresh-cw" style="width: 14px; height: 14px; stroke: white; margin-right: 4px;"></i>Recheck</button>
        <button class="pure-button button-secondary button-xsmall" name="op" value="assign-tag" id="checkbox-assign-tag"><i data-feather="tag" style="width: 14px; height: 14px; stroke: white; margin-right: 4px;"></i>Tag</button>
        <button class="pure-button button-secondary button-xsmall" name="op" value="mark-viewed"><i data-feather="eye" style="width: 14px; height: 14px; stroke: white; margin-right: 4px;"></i>Mark viewed</button>
        <button class="pure-button button-secondary button-xsmall" name="op" value="notification-default"><i data-feather="bell" style="width: 14px; height: 14px; stroke: white; margin-right: 4px;"></i>Use default notification</button>
        <button class="pure-button button-secondary button-xsmall" name="op" value="clear-errors"><i data-feather="x-circle" style="width: 14px; height: 14px; stroke: white; margin-right: 4px;"></i>Clear errors</button>
        <button class="pure-button button-secondary button-xsmall" style="background: #dd4242;" name="op" value="clear-history"><i data-feather="trash-2" style="width: 14px; height: 14px; stroke: white; margin-right: 4px;"></i>Clear/reset history</button>
        <button class="pure-button button-secondary button-xsmall" style="background: #dd4242;" name="op" value="delete"><i data-feather="trash" style="width: 14px; height: 14px; stroke: white; margin-right: 4px;"></i>Delete</button>
    </div>
    {%- if watches|length >= pagination.per_page -%}
        {{ pagination.info }}
    {%- endif -%}
    {%- if search_q -%}<div id="search-result-info">Searching "<strong><i>{{search_q}}</i></strong>"</div>{%- endif -%}
    <div>
        <a href="{{url_for('watchlist.index')}}" class="pure-button button-tag {{'active' if not active_tag_uuid }}">All</a>

    <!-- tag list -->
    {%- for uuid, tag in tags -%}
        {%- if tag != "" -%}
            <a href="{{url_for('watchlist.index', tag=uuid) }}" class="pure-button button-tag {{'active' if active_tag_uuid == uuid }}">{{ tag.title }}</a>
        {%- endif -%}
    {%- endfor -%}
    </div>

    {%- set sort_order = sort_order or 'asc' -%}
    {%- set sort_attribute = sort_attribute or 'last_changed'  -%}
    {%- set pagination_page = request.args.get('page', 0) -%}
    {%- set cols_required = 6 -%}
    {%- set any_has_restock_price_processor = datastore.any_watches_have_processor_by_name("restock_diff") -%}
    {%- if any_has_restock_price_processor -%}
        {%- set cols_required = cols_required + 1 -%}
    {%- endif -%}

    <div id="watch-table-wrapper">

        <table class="pure-table pure-table-striped watch-table">
            <thead>
            <tr>
                {%- set link_order = "desc" if sort_order  == 'asc' else "asc" -%}
                {%- set arrow_span = "" -%}
                <th><input style="vertical-align: middle" type="checkbox" id="check-all" > <a class="{{ 'active '+link_order if sort_attribute == 'date_created' else 'inactive' }}"  href="{{url_for('watchlist.index', sort='date_created', order=link_order, tag=active_tag_uuid)}}"># <span class='arrow {{link_order}}'></span></a></th>
                <th>
                    <a class="{{ 'active '+link_order if sort_attribute == 'paused' else 'inactive' }}" href="{{url_for('watchlist.index', sort='paused', order=link_order, tag=active_tag_uuid)}}"><i data-feather="pause" style="vertical-align: bottom; width: 14px; height: 14px;  margin-right: 4px;"></i><span class='arrow {{link_order}}'></span></a>
                    &nbsp;
                    <a class="{{ 'active '+link_order if sort_attribute == 'notification_muted' else 'inactive' }}" href="{{url_for('watchlist.index', sort='notification_muted', order=link_order, tag=active_tag_uuid)}}"><i data-feather="volume-2" style="vertical-align: bottom; width: 14px; height: 14px;  margin-right: 4px;"></i><span class='arrow {{link_order}}'></span></a>
                </th>
                <th><a class="{{ 'active '+link_order if sort_attribute == 'label' else 'inactive' }}" href="{{url_for('watchlist.index', sort='label', order=link_order, tag=active_tag_uuid)}}">Website <span class='arrow {{link_order}}'></span></a></th>
             {%- if any_has_restock_price_processor -%}
                <th>Restock &amp; Price</th>
             {%- endif -%}
                <th><a class="{{ 'active '+link_order if sort_attribute == 'last_checked' else 'inactive' }}" href="{{url_for('watchlist.index', sort='last_checked', order=link_order, tag=active_tag_uuid)}}"><span class="hide-on-mobile">Last</span> Checked <span class='arrow {{link_order}}'></span></a></th>
                <th><a class="{{ 'active '+link_order if sort_attribute == 'last_changed' else 'inactive' }}" href="{{url_for('watchlist.index', sort='last_changed', order=link_order, tag=active_tag_uuid)}}"><span class="hide-on-mobile">Last</span> Changed <span class='arrow {{link_order}}'></span></a></th>
                <th class="empty-cell"></th>
            </tr>
            </thead>
            <tbody>
            {%- if not watches|length -%}
            <tr>
                <td colspan="{{ cols_required }}" style="text-wrap: wrap;">No website watches configured, please add a URL in the box above, or <a href="{{ url_for('imports.import_page')}}" >import a list</a>.</td>
            </tr>
            {%- endif -%}

            {%- for watch in (watches|sort(attribute=sort_attribute, reverse=sort_order == 'asc'))|pagination_slice(skip=pagination.skip) -%}
                {%- set checking_now = is_checking_now(watch) -%}
                {%- set history_n = watch.history_n -%}
                {%- set has_favicon = watch.get_favicon_filename() -%}
                {#  Mirror in changedetectionio/static/js/realtime.js for the frontend #}
                {%- set row_classes = [
                    loop.cycle('pure-table-odd', 'pure-table-even'),
                    'processor-' ~ watch['processor'],
                    'has-error' if watch.compile_error_texts()|length > 2 else '',
                    'paused' if watch.paused is defined and watch.paused != False else '',
                    'unviewed' if watch.has_unviewed else '',
                    'has-restock-info' if watch.has_restock_info else 'no-restock-info',
                    'has-favicon' if has_favicon else '',
                    'in-stock' if watch.has_restock_info and watch['restock']['in_stock'] else '',
                    'not-in-stock' if watch.has_restock_info and not watch['restock']['in_stock'] else '',
                    'queued' if watch.uuid in queued_uuids else '',
                    'checking-now' if checking_now else '',
                    'notification_muted' if watch.notification_muted else '',
                    'single-history' if history_n == 1 else '',
                    'multiple-history' if history_n >= 2 else '',                    
                ] -%}
            <tr id="{{ watch.uuid }}" data-watch-uuid="{{ watch.uuid }}" class="{{ row_classes | reject('equalto', '') | join(' ') }}">
                <td class="inline checkbox-uuid" ><div><input name="uuids"  type="checkbox" value="{{ watch.uuid}} " > <span class="counter-i">{{ loop.index+pagination.skip }}</span></div></td>
                <td class="inline watch-controls">
                    <div>
                    <a class="ajax-op state-off pause-toggle" data-op="pause" href="{{url_for('watchlist.index', op='pause', uuid=watch.uuid, tag=active_tag_uuid)}}"><img src="{{url_for('static_content', group='images', filename='pause.svg')}}" alt="Pause checks" title="Pause checks" class="icon icon-pause" ></a>
                    <a class="ajax-op state-on pause-toggle"  data-op="pause" style="display: none" href="{{url_for('watchlist.index', op='pause', uuid=watch.uuid, tag=active_tag_uuid)}}"><img src="{{url_for('static_content', group='images', filename='play.svg')}}" alt="UnPause checks" title="UnPause checks" class="icon icon-unpause" ></a>
                    <a class="ajax-op state-off mute-toggle" data-op="mute" href="{{url_for('watchlist.index', op='mute', uuid=watch.uuid, tag=active_tag_uuid)}}"><img src="{{url_for('static_content', group='images', filename='bell-off.svg')}}" alt="Mute notification" title="Mute notification" class="icon icon-mute" ></a>
                    <a class="ajax-op state-on mute-toggle" data-op="mute"  style="display: none" href="{{url_for('watchlist.index', op='mute', uuid=watch.uuid, tag=active_tag_uuid)}}"><img src="{{url_for('static_content', group='images', filename='bell-off.svg')}}" alt="UnMute notification" title="UnMute notification" class="icon icon-mute" ></a>
                    </div>
                </td>

                <td class="title-col inline">
                    <div class="flex-wrapper">
                        <div>{# A page might have hundreds of these images, set IMG options for lazy loading, don't set SRC if we dont have it so it doesnt fetch the placeholder'  #}
                            <img alt="Favicon thumbnail" style="display: none;" class="favicon" loading="lazy" decoding="async" fetchpriority="low" {% if has_favicon %} src="{{url_for('static_content', group='favicon', filename=watch.uuid)}}" {% else %} src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw=="{%  endif %} />
                        </div>
                        <div>
                        <span class="watch-title">
                            {{watch.title if watch.title is not none and watch.title|length > 0 else watch.url}}&nbsp;<a class="external" target="_blank" rel="noopener" href="{{ watch.link.replace('source:','') }}">&nbsp;</a>
                        </span>
                            <div class="error-text" style="display:none;">{{ watch.compile_error_texts(has_proxies=datastore.proxy_list) }}</div>
                            {%- if watch['processor'] == 'text_json_diff'  -%}
                                {%- if watch['has_ldjson_price_data'] and not watch['track_ldjson_price_data']  -%}
                                <div class="ldjson-price-track-offer">Switch to Restock & Price watch mode? <a href="{{url_for('price_data_follower.accept', uuid=watch.uuid)}}" class="pure-button button-xsmall">Yes</a> <a href="{{url_for('price_data_follower.reject', uuid=watch.uuid)}}" class="">No</a></div>
                                {%- endif -%}
                            {%- endif -%}
                            {%- if watch['processor'] == 'restock_diff' -%}
                                <span class="tracking-ldjson-price-data" title="Automatically following embedded price information"><img src="{{url_for('static_content', group='images', filename='price-tag-icon.svg')}}"  class="status-icon price-follow-tag-icon" > Price</span>
                            {%- endif -%}
                            {%- for watch_tag_uuid, watch_tag in datastore.get_all_tags_for_watch(watch['uuid']).items() -%}
                              <span class="watch-tag-list">{{ watch_tag.title }}</span>
                            {%- endfor -%}
                        </div>
                    <div class="status-icons">
                            <a class="link-spread" href="{{url_for('ui.form_share_put_watch', uuid=watch.uuid)}}"><img src="{{url_for('static_content', group='images', filename='spread.svg')}}" class="status-icon icon icon-spread" title="Create a link to share watch config with others" ></a>
                            {%- if watch.get_fetch_backend == "html_webdriver"
                                 or ( watch.get_fetch_backend == "system" and system_default_fetcher == 'html_webdriver'  )
                                 or "extra_browser_" in watch.get_fetch_backend
                            -%}
                            <img class="status-icon" src="{{url_for('static_content', group='images', filename='google-chrome-icon.png')}}" alt="Using a Chrome browser" title="Using a Chrome browser" >
                            {%- endif -%}
                            {%- if watch.is_pdf  -%}<img class="status-icon" src="{{url_for('static_content', group='images', filename='pdf-icon.svg')}}" alt="Converting PDF to text" >{%- endif -%}
                            {%- if watch.has_browser_steps -%}<img class="status-icon status-browsersteps" src="{{url_for('static_content', group='images', filename='steps.svg')}}" alt="Browser Steps is enabled" >{%- endif -%}

                    </div>
                    </div>
                </td>
{%- if any_has_restock_price_processor -%}
                <td class="restock-and-price">
                    {%- if watch['processor'] == 'restock_diff'  -%}
                        {%- if watch.has_restock_info -%}
                            <span class="restock-label {{'in-stock' if watch['restock']['in_stock'] else 'not-in-stock' }}" title="Detecting restock and price">
                                <!-- maybe some object watch['processor'][restock_diff] or.. -->
                                 {%- if watch['restock']['in_stock']-%}  In stock {%- else-%}  Not in stock {%- endif -%}
                            </span>
                        {%- endif -%}

                        {%- if watch.get('restock') and watch['restock']['price'] != None -%}
                            {%- if watch['restock']['price'] != None -%}
                                <span class="restock-label price" title="Price">
                                {{ watch['restock']['price']|format_number_locale }} {{ watch['restock']['currency'] }}
                                </span>
                            {%- endif -%}
                        {%- elif not watch.has_restock_info -%}
                            <span class="restock-label error">No information</span>
                        {%- endif -%}
                    {%- endif -%}
                </td>
{%- endif -%}
            {#last_checked becomes fetch-start-time#}
                <td class="last-checked" data-timestamp="{{ watch.last_checked }}" data-fetchduration={{ watch.fetch_time }} data-eta_complete="{{ watch.last_checked+watch.fetch_time }}" >
                    <div class="spinner-wrapper" style="display:none;" >
                        <span class="spinner"></span><span>&nbsp;Checking now</span>
                    </div>
                    <span class="innertext">{{watch|format_last_checked_time|safe}}</span>
                </td>
                <td class="last-changed" data-timestamp="{{ watch.last_changed }}">{%- if watch.history_n >=2 and watch.last_changed >0 -%}
                    {{watch.last_changed|format_timestamp_timeago}}
                    {%- else -%}
                    Not yet
                    {%- endif -%}
                </td>
                <td class="buttons">
                    <div>
                    {%- set target_attr = ' target="' ~ watch.uuid ~ '"' if datastore.data['settings']['application']['ui'].get('open_diff_in_new_tab') else '' -%}
                    <a href="" class="already-in-queue-button recheck pure-button pure-button-primary" style="display: none;" disabled="disabled">Queued</a>
                    <a href="{{ url_for('ui.form_watch_checknow', uuid=watch.uuid, tag=request.args.get('tag')) }}" data-op='recheck' class="ajax-op recheck pure-button pure-button-primary">Recheck</a>
                    <a href="{{ url_for('ui.ui_edit.edit_page', uuid=watch.uuid, tag=active_tag_uuid)}}#general" class="pure-button pure-button-primary">Edit</a>
                    <a href="{{ url_for('ui.ui_views.diff_history_page', uuid=watch.uuid)}}" {{target_attr}} class="pure-button pure-button-primary history-link" style="display: none;">History</a>
                    <a href="{{ url_for('ui.ui_views.preview_page', uuid=watch.uuid)}}" {{target_attr}} class="pure-button pure-button-primary preview-link" style="display: none;">Preview</a>
                    </div>
                </td>
            </tr>
            {%- endfor -%}
            </tbody>
        </table>
        <ul id="post-list-buttons">
            <li id="post-list-with-errors" class="{%- if errored_count -%}has-error{%- endif -%}" style="display: none;" >
                <a href="{{url_for('watchlist.index', with_errors=1, tag=request.args.get('tag')) }}" class="pure-button button-tag button-error">With errors ({{ errored_count }})</a>
            </li>
            <li id="post-list-mark-views" class="{%- if has_unviewed -%}has-unviewed{%- endif -%}" style="display: none;" >
                <a href="{{url_for('ui.mark_all_viewed',with_errors=request.args.get('with_errors',0)) }}" class="pure-button button-tag " id="mark-all-viewed">Mark all viewed</a>
            </li>
        {%-  if active_tag_uuid -%}
            <li id="post-list-mark-views-tag">
                <a href="{{url_for('ui.mark_all_viewed', tag=active_tag_uuid) }}" class="pure-button button-tag " id="mark-all-viewed">Mark all viewed in '{{active_tag.title}}'</a>
            </li>
        {%-  endif -%}
            <li>
               <a href="{{ url_for('ui.form_watch_checknow', tag=active_tag_uuid, with_errors=request.args.get('with_errors',0)) }}" class="pure-button button-tag" id="recheck-all">Recheck
                all {% if active_tag_uuid %}  in '{{active_tag.title}}'{%endif%}</a>
            </li>
            <li>
                <a href="{{ url_for('rss.feed', tag=active_tag_uuid, token=app_rss_token)}}"><img alt="RSS Feed" id="feed-icon" src="{{url_for('static_content', group='images', filename='generic_feed-icon.svg')}}" height="15"></a>
            </li>
        </ul>
        {{ pagination.links }}
    </div>
    </form>
</div>
{%- endblock -%}