{% extends 'base.html' %}
{% block content %}
{% from '_helpers.html' import render_field %}
<script src="{{url_for('static_content', group='js', filename='tabs.js')}}" defer></script>
<div class="edit-form monospaced-textarea">

    <div class="tabs collapsable">
        <ul>
            <li class="tab" id=""><a href="#url-list">URL List</a></li>
            <li class="tab"><a href="#distill-io">Distill.io</a></li>
            <li class="tab"><a href="#xlsx">.XLSX &amp; Wachete</a></li>
        </ul>
    </div>

    <div class="box-wrap inner">
        <form class="pure-form" action="{{url_for('imports.import_page')}}" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="tab-pane-inner" id="url-list">
                <div class="pure-control-group">
                        Enter one URL per line, and optionally add tags for each URL after a space, delineated by comma
                        (,):
                        <br>
                        <p><strong>Example:  </strong><code>https://example.com tag1, tag2, last tag</code></p>
                        URLs which do not pass validation will stay in the textarea.
                </div>
                {{ render_field(form.processor, class="processor") }}
                
                <div class="pure-control-group">
                    <textarea name="urls" class="pure-input-1-2" placeholder="https://"
                              style="width: 100%;
                                font-family:monospace;
                                white-space: pre;
                                overflow-wrap: normal;
                                overflow-x: scroll;" rows="25">{{ import_url_list_remaining }}</textarea>
                 </div>
                 <div id="quick-watch-processor-type"></div>

            </div>

            <div class="tab-pane-inner" id="distill-io">



                    <div class="pure-control-group">
                        Copy and Paste your Distill.io watch 'export' file, this should be a JSON file.<br>
                        This is <i>experimental</i>, supported fields are <code>name</code>, <code>uri</code>, <code>tags</code>, <code>config:selections</code>, the rest (including <code>schedule</code>) are ignored.
                        <br>
                        <p>
                        How to export? <a href="https://distill.io/docs/web-monitor/how-export-and-import-monitors/">https://distill.io/docs/web-monitor/how-export-and-import-monitors/</a><br>
                        Be sure to set your default fetcher to Chrome if required.<br>
                        </p>
                    </div>


                    <textarea name="distill-io" class="pure-input-1-2" style="width: 100%;
                                font-family:monospace;
                                white-space: pre;
                                overflow-wrap: normal;
                                overflow-x: scroll;" placeholder="Example Distill.io JSON export file

{
    &quot;client&quot;: {
        &quot;local&quot;: 1
    },
    &quot;data&quot;: [
        {
            &quot;name&quot;: &quot;Unraid | News&quot;,
            &quot;uri&quot;: &quot;https://unraid.net/blog&quot;,
            &quot;config&quot;: &quot;{\&quot;selections\&quot;:[{\&quot;frames\&quot;:[{\&quot;index\&quot;:0,\&quot;excludes\&quot;:[],\&quot;includes\&quot;:[{\&quot;type\&quot;:\&quot;xpath\&quot;,\&quot;expr\&quot;:\&quot;(//div[@id='App']/div[contains(@class,'flex')]/main[contains(@class,'relative')]/section[contains(@class,'relative')]/div[@class='container']/div[contains(@class,'flex')]/div[contains(@class,'w-full')])[1]\&quot;}]}],\&quot;dynamic\&quot;:true,\&quot;delay\&quot;:2}],\&quot;ignoreEmptyText\&quot;:true,\&quot;includeStyle\&quot;:false,\&quot;dataAttr\&quot;:\&quot;text\&quot;}&quot;,
            &quot;tags&quot;: [],
            &quot;content_type&quot;: 2,
            &quot;state&quot;: 40,
            &quot;schedule&quot;: &quot;{\&quot;type\&quot;:\&quot;INTERVAL\&quot;,\&quot;params\&quot;:{\&quot;interval\&quot;:4447}}&quot;,
            &quot;ts&quot;: &quot;2022-03-27T15:51:15.667Z&quot;
        }
    ]
}
" rows="25">{{ original_distill_json }}</textarea>

            </div>
            <div class="tab-pane-inner" id="xlsx">
            <fieldset>
                <div class="pure-control-group">
                {{ render_field(form.xlsx_file, class="processor") }}
                </div>
                <div class="pure-control-group">
                    {{ render_field(form.file_mapping, class="processor") }}
                </div>
            </fieldset>
                <div class="pure-control-group">
                <span class="pure-form-message-inline">
                    Table of custom column and data types mapping for the <strong>Custom mapping</strong> File mapping type.
                </span>
                    <table style="border: 1px solid #aaa; padding: 0.5rem; border-radius: 4px;">
                        <tr>
                            <td><strong>Column #</strong></td>
                            {% for n in range(4) %}
                                <td><input type="number" name="custom_xlsx[col_{{n}}]" style="width: 4rem;" min="1"></td>
                            {%  endfor %}
                        </tr>
                        <tr>
                            <td><strong>Type</strong></td>
                            {% for n in range(4) %}
                                <td><select name="custom_xlsx[col_type_{{n}}]">
                                    <option value="" style="color: #aaa"> -- none --</option>
                                    <option value="url">URL</option>
                                    <option value="title">Title</option>
                                    <option value="include_filters">CSS/xPath filter</option>
                                    <option value="tag">Group / Tag name(s)</option>
                                    <option value="interval_minutes">Recheck time (minutes)</option>
                                </select></td>
                            {%  endfor %}
                        </tr>
                    </table>
                </div>
            </div>
            <button type="submit" class="pure-button pure-input-1-2 pure-button-primary">Import</button>
        </form>

    </div>
</div>

{% endblock %}