#!/usr/bin/env python3
"""
Test script to fetch WhatsApp Web content and parse messages
"""

import requests
import json
import time
from whatsapp_parser import WhatsAppMessageParser

def get_chrome_tabs():
    """Get list of Chrome tabs"""
    try:
        response = requests.get("http://localhost:9222/json", timeout=5)
        return response.json()
    except Exception as e:
        print(f"Error getting Chrome tabs: {e}")
        return []

def get_whatsapp_tab():
    """Find WhatsApp Web tab"""
    tabs = get_chrome_tabs()
    for tab in tabs:
        if 'web.whatsapp.com' in tab.get('url', ''):
            return tab
    return None

def get_page_content(tab_id):
    """Get page content using Chrome DevTools Protocol"""
    try:
        # Connect to the tab's WebSocket
        import websocket
        import threading
        
        ws_url = f"ws://localhost:9222/devtools/page/{tab_id}"
        
        # Simple synchronous approach
        response = requests.post(
            f"http://localhost:9222/json/runtime/evaluate",
            json={
                "expression": "document.documentElement.outerHTML"
            }
        )
        
        # Alternative: Use CDP directly
        import subprocess
        result = subprocess.run([
            'curl', '-X', 'POST',
            f'http://localhost:9222/json/runtime/evaluate',
            '-H', 'Content-Type: application/json',
            '-d', '{"expression": "document.documentElement.outerHTML"}'
        ], capture_output=True, text=True)
        
        return result.stdout
        
    except Exception as e:
        print(f"Error getting page content: {e}")
        return None

def test_with_playwright():
    """Test using Playwright to connect to existing Chrome"""
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            # Connect to existing Chrome instance
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            
            # Get the WhatsApp page
            pages = browser.contexts[0].pages
            whatsapp_page = None
            
            for page in pages:
                if 'web.whatsapp.com' in page.url:
                    whatsapp_page = page
                    break
            
            if not whatsapp_page:
                print("❌ WhatsApp Web tab not found")
                return None
            
            print(f"✅ Found WhatsApp tab: {whatsapp_page.title()}")
            
            # Get page content
            html_content = whatsapp_page.content()
            
            # Parse messages
            parser = WhatsAppMessageParser()
            messages = parser.parse_messages(html_content)
            
            if messages:
                print(f"📱 Found {len(messages)} messages:")
                for i, msg in enumerate(messages[-5:], 1):  # Show last 5 messages
                    print(f"  {i}. {msg}")
                
                # Save to file
                payload = parser.create_pipeline_payload(messages)
                
                with open('test_messages.json', 'w', encoding='utf-8') as f:
                    json.dump(payload, f, indent=2, ensure_ascii=False)
                
                print(f"💾 Saved {len(messages)} messages to test_messages.json")
                return payload
            else:
                print("❌ No messages found")
                return None
                
    except ImportError:
        print("❌ Playwright not installed. Install with: pip install playwright")
        return None
    except Exception as e:
        print(f"❌ Error with Playwright: {e}")
        return None

def test_simple_fetch():
    """Simple test to fetch page using requests"""
    try:
        # Get WhatsApp tab
        tab = get_whatsapp_tab()
        if not tab:
            print("❌ WhatsApp Web tab not found")
            return None
        
        print(f"✅ Found WhatsApp tab: {tab['title']}")
        print(f"🔗 URL: {tab['url']}")
        
        # For now, just show tab info
        return tab
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    print("🧪 Testing WhatsApp Message Parser")
    print("=" * 50)
    
    # Check Chrome debugging
    print("1. Checking Chrome debugging...")
    tabs = get_chrome_tabs()
    if not tabs:
        print("❌ Chrome debugging not available")
        exit(1)
    
    print(f"✅ Chrome debugging available - {len(tabs)} tabs open")
    
    # Find WhatsApp tab
    print("\n2. Looking for WhatsApp Web tab...")
    whatsapp_tab = get_whatsapp_tab()
    if not whatsapp_tab:
        print("❌ WhatsApp Web tab not found")
        print("Please open WhatsApp Web in Chrome")
        exit(1)
    
    print(f"✅ Found WhatsApp tab: {whatsapp_tab['title']}")
    
    # Test with Playwright
    print("\n3. Testing message parsing with Playwright...")
    result = test_with_playwright()
    
    if result:
        print("\n🎉 Test successful!")
        print(f"📊 Parsed {result['count']} messages")
        print("📁 Check test_messages.json for full output")
    else:
        print("\n⚠️  Test failed or no messages found")
        print("Make sure you're in a WhatsApp group with messages")
