#!/usr/bin/env python3
"""
WhatsApp New Messages Only Monitor
Only displays truly NEW messages that arrive after monitoring starts
"""

import subprocess
import sys
import json
import time
from datetime import datetime

class NewMessagesMonitor:
    def __init__(self, group_name="let me test, pls", interval=60):
        self.group_name = group_name
        self.interval = interval
        self.baseline_messages = None
        self.monitoring = False
        
    def run_playwright_script(self, script_content):
        """Run playwright script in a separate process"""
        try:
            with open('/tmp/monitor_script.py', 'w') as f:
                f.write(script_content)
            
            result = subprocess.run([sys.executable, '/tmp/monitor_script.py'], 
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                return json.loads(result.stdout)
            else:
                return {'success': False, 'error': result.stderr}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def get_all_messages(self):
        """Get ALL messages from the target group"""
        script = f'''
import json
from playwright.sync_api import sync_playwright
import time

try:
    with sync_playwright() as p:
        browser = p.chromium.connect_over_cdp("http://localhost:9222")
        context = browser.contexts[0]
        pages = context.pages
        
        whatsapp_page = None
        for page in pages:
            try:
                if 'web.whatsapp.com' in page.url:
                    page.query_selector('body')
                    whatsapp_page = page
                    break
            except:
                continue
        
        if not whatsapp_page:
            result = {{'success': False, 'error': 'WhatsApp page not found', 'messages': []}}
        else:
            try:
                whatsapp_page.wait_for_selector('#pane-side', timeout=10000)
                chat_elements = whatsapp_page.query_selector_all('#pane-side div[role="listitem"]')
            except:
                result = {{'success': False, 'error': 'Chat list not found', 'messages': []}}
                print(json.dumps(result))
                exit()
            
            target_index = -1
            for i, chat_elem in enumerate(chat_elements):
                try:
                    title_elem = chat_elem.query_selector('span[title]')
                    if title_elem:
                        title = title_elem.get_attribute('title') or title_elem.text_content()
                        if title and title.strip() == "{self.group_name}":
                            target_index = i
                            break
                except:
                    continue
            
            if target_index == -1:
                result = {{'success': False, 'error': 'Group not found', 'messages': []}}
            else:
                chat_elements[target_index].click()
                time.sleep(3)
                
                conv_panel = whatsapp_page.query_selector('#main')
                if not conv_panel:
                    result = {{'success': False, 'error': 'Conversation panel not found', 'messages': []}}
                else:
                    # Scroll up to load more messages
                    try:
                        for _ in range(3):
                            whatsapp_page.keyboard.press('PageUp')
                            time.sleep(0.3)
                    except:
                        pass
                    
                    message_elements = whatsapp_page.query_selector_all('div[role="row"]')
                    
                    messages = []
                    for i, msg_elem in enumerate(message_elements):
                        try:
                            text_selectors = ['span.selectable-text', '.selectable-text']
                            
                            message_text = None
                            for text_selector in text_selectors:
                                text_elem = msg_elem.query_selector(text_selector)
                                if text_elem:
                                    message_text = text_elem.text_content()
                                    if message_text and message_text.strip():
                                        message_text = message_text.strip()
                                        break
                            
                            # Get timestamp
                            timestamp = "Unknown"
                            time_selectors = ['span[data-testid="msg-time"]', 'span[title*=":"]']
                            for time_selector in time_selectors:
                                time_elem = msg_elem.query_selector(time_selector)
                                if time_elem:
                                    time_text = time_elem.get_attribute('title') or time_elem.text_content()
                                    if time_text and ':' in time_text:
                                        timestamp = time_text.strip()
                                        break
                            
                            # Check if outgoing message
                            is_outgoing = False
                            try:
                                if msg_elem.query_selector('div[class*="tail-out"]'):
                                    is_outgoing = True
                            except:
                                pass
                            
                            if message_text and len(message_text) > 0:
                                message_id = f"{{i}}_{{hash(message_text[:50])}}_{{timestamp}}"
                                
                                messages.append({{
                                    'id': message_id,
                                    'text': message_text,
                                    'timestamp': timestamp,
                                    'is_outgoing': is_outgoing
                                }})
                                
                        except Exception as e:
                            continue
                    
                    result = {{'success': True, 'messages': messages, 'total_found': len(message_elements)}}
        
        print(json.dumps(result))

except Exception as e:
    print(json.dumps({{'success': False, 'error': str(e), 'messages': []}}))
'''
        
        return self.run_playwright_script(script)

    def establish_baseline(self):
        """Get initial messages to establish baseline"""
        print("🔍 Establishing baseline - loading existing messages...")
        result = self.get_all_messages()
        
        if result['success']:
            self.baseline_messages = {msg['id'] for msg in result['messages']}
            print(f"✅ Baseline established - {len(self.baseline_messages)} existing messages")
            return True
        else:
            print(f"❌ Failed to establish baseline: {result['error']}")
            return False

    def detect_new_messages(self, current_messages):
        """Detect truly new messages since baseline"""
        if not self.baseline_messages:
            return []
        
        current_ids = {msg['id'] for msg in current_messages}
        new_ids = current_ids - self.baseline_messages
        
        new_messages = [msg for msg in current_messages if msg['id'] in new_ids]
        
        # Update baseline to include new messages
        self.baseline_messages.update(new_ids)
        
        return new_messages

    def start_monitoring(self):
        """Start monitoring for new messages only"""
        print(f"🚀 Starting NEW MESSAGES ONLY monitor for: '{self.group_name}'")
        print(f"⏱️  Check interval: {self.interval} seconds")
        print("=" * 80)
        
        # Establish baseline
        if not self.establish_baseline():
            return
        
        print("\n🎯 Now monitoring for NEW messages only...")
        print("💬 Only messages sent AFTER this point will be displayed")
        print("Press Ctrl+C to stop monitoring")
        print("=" * 80)
        
        self.monitoring = True
        scan_count = 0
        
        try:
            while self.monitoring:
                scan_count += 1
                current_time = datetime.now().strftime('%H:%M:%S')
                
                result = self.get_all_messages()
                
                if result['success']:
                    current_messages = result['messages']
                    new_messages = self.detect_new_messages(current_messages)
                    
                    if new_messages:
                        print(f"\n🚨 NEW MESSAGE(S) at {current_time}")
                        print("=" * 50)
                        
                        for msg in new_messages:
                            direction = "📤 OUT" if msg['is_outgoing'] else "📥 IN"
                            print(f"{direction} [{msg['timestamp']}]: {msg['text']}")
                            
                            # Log to file
                            with open('new_messages.log', 'a', encoding='utf-8') as f:
                                f.write(f"{datetime.now().isoformat()} | {direction} | [{msg['timestamp']}]: {msg['text']}\n")
                        
                        print("=" * 50)
                    else:
                        # Just show a simple status every 10 scans
                        if scan_count % 10 == 0:
                            print(f"⏳ {current_time} - No new messages (scan #{scan_count})")
                else:
                    print(f"❌ {current_time} - Error: {result['error']}")
                
                time.sleep(self.interval)
                
        except KeyboardInterrupt:
            print(f"\n\n🛑 Monitoring stopped")
            print(f"📊 Total scans: {scan_count}")
            print("📝 Log file: new_messages.log")

if __name__ == "__main__":
    monitor = NewMessagesMonitor(group_name="let me test, pls", interval=60)
    monitor.start_monitoring()
