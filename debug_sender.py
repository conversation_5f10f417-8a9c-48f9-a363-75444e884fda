#!/usr/bin/env python3
"""
Debug script to understand sender detection in WhatsApp messages
"""

import subprocess
import sys
import json

def debug_sender_detection():
    """Debug sender detection in WhatsApp messages"""
    script = '''
import json
from playwright.sync_api import sync_playwright
import time

try:
    with sync_playwright() as p:
        browser = p.chromium.connect_over_cdp("http://localhost:9222")
        context = browser.contexts[0]
        pages = context.pages
        
        whatsapp_page = None
        for page in pages:
            try:
                if 'web.whatsapp.com' in page.url:
                    page.query_selector('body')
                    whatsapp_page = page
                    break
            except:
                continue
        
        if not whatsapp_page:
            result = {'success': False, 'error': 'WhatsApp page not found'}
        else:
            # Find and click on "let me test, pls" group
            chat_elements = whatsapp_page.query_selector_all('#pane-side div[role="listitem"]')
            
            target_index = -1
            for i, chat_elem in enumerate(chat_elements):
                try:
                    title_elem = chat_elem.query_selector('span[title]')
                    if title_elem:
                        title = title_elem.get_attribute('title') or title_elem.text_content()
                        if title and title.strip() == "let me test, pls":
                            target_index = i
                            break
                except:
                    continue
            
            if target_index == -1:
                result = {'success': False, 'error': 'Group not found'}
            else:
                chat_elements[target_index].click()
                time.sleep(3)
                
                # Get message elements
                message_elements = whatsapp_page.query_selector_all('div[role="row"]')
                
                debug_info = []
                
                # Analyze first 5 messages for sender detection
                for i, msg_elem in enumerate(message_elements[:5]):
                    try:
                        # Get message text
                        text_elem = msg_elem.query_selector('span.selectable-text')
                        message_text = text_elem.text_content() if text_elem else "No text"
                        
                        # Check various indicators for outgoing messages
                        outgoing_indicators = {
                            'tail-out in class': 'tail-out' in (msg_elem.get_attribute('class') or ''),
                            'tail-out selector': bool(msg_elem.query_selector('div[class*="tail-out"]')),
                            'msg-meta tail-out': bool(msg_elem.query_selector('div[data-testid="msg-meta"][class*="tail-out"]')),
                            'message-out': bool(msg_elem.query_selector('div[class*="message-out"]')),
                            'outgoing meta': bool(msg_elem.query_selector('span[data-testid="msg-meta-outgoing"]'))
                        }
                        
                        # Try to find sender elements
                        sender_attempts = {}
                        sender_selectors = [
                            'span[data-testid="msg-author"]',
                            'div[data-testid="conversation-info-header"] span',
                            'span[dir="auto"][title]',
                            'span[class*="author"]',
                            'div[class*="author"]'
                        ]
                        
                        for selector in sender_selectors:
                            elem = msg_elem.query_selector(selector)
                            if elem:
                                sender_attempts[selector] = elem.get_attribute('title') or elem.text_content()
                        
                        # Get HTML structure for analysis
                        html_sample = msg_elem.inner_html()[:300] if msg_elem else "No HTML"
                        
                        debug_info.append({
                            'index': i,
                            'message_text': message_text[:50],
                            'outgoing_indicators': outgoing_indicators,
                            'sender_attempts': sender_attempts,
                            'html_sample': html_sample
                        })
                        
                    except Exception as e:
                        debug_info.append({
                            'index': i,
                            'error': str(e)
                        })
                
                result = {'success': True, 'debug_info': debug_info}
        
        print(json.dumps(result, indent=2))

except Exception as e:
    print(json.dumps({'success': False, 'error': str(e)}))
'''
    
    try:
        with open('/tmp/debug_sender_script.py', 'w') as f:
            f.write(script)
        
        result = subprocess.run([sys.executable, '/tmp/debug_sender_script.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            return json.loads(result.stdout)
        else:
            return {'success': False, 'error': result.stderr}
    except Exception as e:
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    print("🔍 Debugging sender detection in WhatsApp messages...")
    print("=" * 60)
    
    result = debug_sender_detection()
    
    if result['success']:
        debug_info = result['debug_info']
        
        for msg_info in debug_info:
            if 'error' in msg_info:
                print(f"Message {msg_info['index']}: Error - {msg_info['error']}")
                continue
                
            print(f"\n📝 Message {msg_info['index']}: {msg_info['message_text']}...")
            print("-" * 40)
            
            print("🔍 Outgoing indicators:")
            for indicator, value in msg_info['outgoing_indicators'].items():
                status = "✅" if value else "❌"
                print(f"  {status} {indicator}: {value}")
            
            print("\n👤 Sender attempts:")
            if msg_info['sender_attempts']:
                for selector, value in msg_info['sender_attempts'].items():
                    print(f"  📍 {selector}: '{value}'")
            else:
                print("  ❌ No sender elements found")
            
            print(f"\n🔧 HTML sample:")
            print(f"  {msg_info['html_sample'][:200]}...")
            
    else:
        print(f"❌ Error: {result['error']}")
