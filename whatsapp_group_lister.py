#!/usr/bin/env python3
"""
WhatsApp Group Lister - Production version for monitoring
Lists all WhatsApp groups/chats from the current WhatsApp Web session
"""

from playwright.sync_api import sync_playwright
import time
import json
import asyncio

def get_whatsapp_groups():
    """Get groups from current WhatsApp page"""
    try:
        # Handle asyncio loop issue
        try:
            loop = asyncio.get_running_loop()
            if loop.is_running():
                # We're in an async context, need to handle differently
                import nest_asyncio
                nest_asyncio.apply()
        except RuntimeError:
            # No running loop, we're fine
            pass

        with sync_playwright() as p:
            # Connect to existing Chrome
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            
            # Find existing WhatsApp tab
            context = browser.contexts[0]
            pages = context.pages
            
            whatsapp_page = None
            for page in pages:
                try:
                    if 'web.whatsapp.com' in page.url:
                        whatsapp_page = page
                        break
                except:
                    continue
            
            if not whatsapp_page:
                return {
                    'success': False,
                    'error': 'No WhatsApp tab found',
                    'groups': []
                }
            
            # Wait a bit for page to be ready
            time.sleep(2)
            
            # Check if logged in by looking for multiple indicators
            try:
                login_indicators = [
                    '[data-testid="chat-list"]',
                    '[data-testid="side"]',
                    '#side',
                    'div[role="application"]',
                    '[data-testid="chat-list-search"]'
                ]
                
                logged_in = False
                for selector in login_indicators:
                    element = whatsapp_page.query_selector(selector)
                    if element:
                        logged_in = True
                        break
                
                if not logged_in:
                    # Check for QR code (indicates not logged in)
                    qr_selectors = ['canvas[aria-label*="QR"]', '[data-testid="qr-code"]', 'canvas']
                    for selector in qr_selectors:
                        qr_element = whatsapp_page.query_selector(selector)
                        if qr_element:
                            return {
                                'success': False,
                                'error': 'Not logged in - QR code found',
                                'groups': []
                            }
                    
                    return {
                        'success': False,
                        'error': 'Not logged in or chat list not found',
                        'groups': []
                    }
                    
            except Exception as e:
                return {
                    'success': False,
                    'error': f'Error checking login status: {e}',
                    'groups': []
                }
            
            # Get all chat items - try multiple selectors
            try:
                chat_selectors = [
                    '#pane-side div[role="listitem"]',
                    '[data-testid="chat-list"] div[role="listitem"]',
                    '[data-testid="chat-list"] > div > div',
                    '[data-testid="chat-list"] div[tabindex]'
                ]
                
                chat_elements = []
                used_selector = None
                for selector in chat_selectors:
                    chat_elements = whatsapp_page.query_selector_all(selector)
                    if chat_elements:
                        used_selector = selector
                        break
                
                if not chat_elements:
                    return {
                        'success': False,
                        'error': 'No chat elements found',
                        'groups': []
                    }
                
                groups = []
                for i, chat_elem in enumerate(chat_elements):
                    try:
                        # Try different selectors for chat title
                        title_selectors = [
                            'span[title]',
                            '[data-testid="conversation-info-header-chat-title"]',
                            'span[dir="auto"]'
                        ]
                        
                        title = None
                        for selector in title_selectors:
                            title_elem = chat_elem.query_selector(selector)
                            if title_elem:
                                title = title_elem.get_attribute('title') or title_elem.text_content()
                                if title and title.strip():
                                    title = title.strip()
                                    break
                        
                        if title:
                            groups.append({
                                'index': i,
                                'name': title,
                                'element_index': i
                            })
                            
                    except Exception as e:
                        continue
                
                return {
                    'success': True,
                    'error': None,
                    'groups': groups,
                    'total_count': len(groups),
                    'selector_used': used_selector
                }
                
            except Exception as e:
                return {
                    'success': False,
                    'error': f'Error getting chat elements: {e}',
                    'groups': []
                }
            
    except Exception as e:
        return {
            'success': False,
            'error': f'Error connecting to Chrome: {e}',
            'groups': []
        }

def click_group_by_name(group_name):
    """Click on a specific group by name"""
    try:
        # Handle asyncio loop issue
        try:
            loop = asyncio.get_running_loop()
            if loop.is_running():
                import nest_asyncio
                nest_asyncio.apply()
        except RuntimeError:
            pass

        with sync_playwright() as p:
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            context = browser.contexts[0]
            pages = context.pages
            
            whatsapp_page = None
            for page in pages:
                if 'web.whatsapp.com' in page.url:
                    whatsapp_page = page
                    break
            
            if not whatsapp_page:
                return {'success': False, 'error': 'WhatsApp page not found'}
            
            # Get current groups
            result = get_whatsapp_groups()
            if not result['success']:
                return result
            
            # Find the group
            target_group = None
            for group in result['groups']:
                if group['name'] == group_name:
                    target_group = group
                    break
            
            if not target_group:
                return {'success': False, 'error': f'Group "{group_name}" not found'}
            
            # Get chat elements and click
            chat_elements = whatsapp_page.query_selector_all(result['selector_used'])
            
            if target_group['element_index'] < len(chat_elements):
                target_element = chat_elements[target_group['element_index']]
                target_element.click()
                
                # Wait for conversation to load
                time.sleep(3)
                
                # Verify conversation loaded
                conv_selectors = [
                    '[data-testid="conversation-panel-messages"]',
                    '[data-testid="main"]',
                    '#main'
                ]
                
                for selector in conv_selectors:
                    conv_panel = whatsapp_page.query_selector(selector)
                    if conv_panel:
                        return {'success': True, 'error': None}
                
                return {'success': False, 'error': 'Conversation panel not found after click'}
            else:
                return {'success': False, 'error': 'Group element not found'}
                
    except Exception as e:
        return {'success': False, 'error': f'Error clicking group: {e}'}

if __name__ == "__main__":
    result = get_whatsapp_groups()
    
    if result['success']:
        print(f"✅ Found {result['total_count']} chats/groups:")
        for i, group in enumerate(result['groups'], 1):
            print(f"  {i}. {group['name']}")
    else:
        print(f"❌ Error: {result['error']}")
