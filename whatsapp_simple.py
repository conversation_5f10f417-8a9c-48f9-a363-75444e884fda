#!/usr/bin/env python3
"""
Simple WhatsApp Group Lister - Avoids asyncio issues
"""

import subprocess
import sys
import json

def run_playwright_script(script_content):
    """Run playwright script in a separate process to avoid asyncio issues"""
    try:
        # Write script to temp file
        with open('/tmp/playwright_script.py', 'w') as f:
            f.write(script_content)
        
        # Run in separate process
        result = subprocess.run([sys.executable, '/tmp/playwright_script.py'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            return json.loads(result.stdout)
        else:
            return {'success': False, 'error': result.stderr}
    except Exception as e:
        return {'success': False, 'error': str(e)}

def get_whatsapp_groups():
    """Get groups from current WhatsApp page"""
    script = '''
import json
from playwright.sync_api import sync_playwright
import time

try:
    with sync_playwright() as p:
        browser = p.chromium.connect_over_cdp("http://localhost:9222")
        context = browser.contexts[0]
        pages = context.pages
        
        whatsapp_page = None
        for page in pages:
            try:
                if 'web.whatsapp.com' in page.url:
                    whatsapp_page = page
                    break
            except:
                continue
        
        if not whatsapp_page:
            result = {'success': False, 'error': 'No WhatsApp tab found', 'groups': []}
        else:
            time.sleep(2)
            
            # Check login
            login_indicators = ['[data-testid="side"]', '#side', 'div[role="application"]']
            logged_in = False
            for selector in login_indicators:
                if whatsapp_page.query_selector(selector):
                    logged_in = True
                    break
            
            if not logged_in:
                result = {'success': False, 'error': 'Not logged in', 'groups': []}
            else:
                # Get chats
                chat_selectors = [
                    '#pane-side div[role="listitem"]',
                    '[data-testid="chat-list"] div[role="listitem"]'
                ]
                
                chat_elements = []
                for selector in chat_selectors:
                    chat_elements = whatsapp_page.query_selector_all(selector)
                    if chat_elements:
                        break
                
                groups = []
                for i, chat_elem in enumerate(chat_elements):
                    try:
                        title_selectors = ['span[title]', 'span[dir="auto"]']
                        title = None
                        for selector in title_selectors:
                            title_elem = chat_elem.query_selector(selector)
                            if title_elem:
                                title = title_elem.get_attribute('title') or title_elem.text_content()
                                if title and title.strip():
                                    title = title.strip()
                                    break
                        
                        if title:
                            groups.append({'index': i, 'name': title})
                    except:
                        continue
                
                result = {'success': True, 'error': None, 'groups': groups}
        
        print(json.dumps(result))

except Exception as e:
    print(json.dumps({'success': False, 'error': str(e), 'groups': []}))
'''
    
    return run_playwright_script(script)

def click_group_by_name(group_name):
    """Click on a specific group by name"""
    script = f'''
import json
from playwright.sync_api import sync_playwright
import time

try:
    with sync_playwright() as p:
        browser = p.chromium.connect_over_cdp("http://localhost:9222")
        context = browser.contexts[0]
        pages = context.pages
        
        whatsapp_page = None
        for page in pages:
            if 'web.whatsapp.com' in page.url:
                whatsapp_page = page
                break
        
        if not whatsapp_page:
            result = {{'success': False, 'error': 'WhatsApp page not found'}}
        else:
            # Get chat elements
            chat_elements = whatsapp_page.query_selector_all('#pane-side div[role="listitem"]')
            
            # Find target group
            target_index = -1
            for i, chat_elem in enumerate(chat_elements):
                try:
                    title_elem = chat_elem.query_selector('span[title]')
                    if title_elem:
                        title = title_elem.get_attribute('title') or title_elem.text_content()
                        if title and title.strip() == "{group_name}":
                            target_index = i
                            break
                except:
                    continue
            
            if target_index == -1:
                result = {{'success': False, 'error': 'Group not found'}}
            else:
                # Click the group
                chat_elements[target_index].click()
                time.sleep(3)
                
                # Check if conversation loaded
                conv_panel = whatsapp_page.query_selector('#main')
                if conv_panel:
                    result = {{'success': True, 'error': None}}
                else:
                    result = {{'success': False, 'error': 'Conversation panel not found'}}
        
        print(json.dumps(result))

except Exception as e:
    print(json.dumps({{'success': False, 'error': str(e)}}))
'''
    
    return run_playwright_script(script)

if __name__ == "__main__":
    result = get_whatsapp_groups()
    
    if result['success']:
        print(f"✅ Found {len(result['groups'])} chats/groups:")
        for i, group in enumerate(result['groups'], 1):
            print(f"  {i}. {group['name']}")
    else:
        print(f"❌ Error: {result['error']}")
