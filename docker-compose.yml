services:
    changedetection:
      image: ghcr.io/dgtlmoon/changedetection.io
      container_name: changedetection
      hostname: changedetection
      volumes:
        - changedetection-data:/datastore
# Configurable proxy list support, see https://github.com/dgtlmoon/changedetection.io/wiki/Proxy-configuration#proxy-list-support
#        - ./proxies.json:/datastore/proxies.json

  #    environment:
  #        Default listening port, can also be changed with the -p option (not to be confused with ports: below)
  #      - PORT=5000
  #
  #        Log levels are in descending order. (TRACE is the most detailed one)
  #        Log output levels: TRACE, DEBUG(default), INFO, SUCCESS, WARNING, ERROR, CRITICAL
  #      - LOGGER_LEVEL=TRACE
  #
  #
  #       Uncomment below and the "sockpuppetbrowser" to use a real Chrome browser (It uses the "playwright" protocol)
  #      - PLAYWRIGHT_DRIVER_URL=ws://browser-sockpuppet-chrome:3000
  #
  #
  #       Alternative WebDriver/selenium URL, do not use "'s or 's! (old, deprecated, does not support screenshots very well)
  #      - WEBDRIVER_URL=http://browser-selenium-chrome:4444/wd/hub
  #
  #       WebDriver proxy settings webdriver_proxyType, webdriver_ftpProxy, webdriver_noProxy,
  #                                webdriver_proxyAutoconfigUrl, webdriver_autodetect,
  #                                webdriver_socksProxy, webdriver_socksUsername, webdriver_socksVersion, webdriver_socksPassword
  #
  #             https://selenium-python.readthedocs.io/api.html#module-selenium.webdriver.common.proxy
  #
  #
  #       Playwright proxy settings playwright_proxy_server, playwright_proxy_bypass, playwright_proxy_username, playwright_proxy_password
  #
  #             https://playwright.dev/python/docs/api/class-browsertype#browser-type-launch-option-proxy
  #
  #        Plain requests - proxy support example.
  #      - HTTP_PROXY=socks5h://**********:1080
  #      - HTTPS_PROXY=socks5h://**********:1080
  #
  #        An exclude list (useful for notification URLs above) can be specified by with
  #      - NO_PROXY="localhost,***********/24"
  #
  #        Base URL of your changedetection.io install (Added to the notification alert)
  #      - BASE_URL=https://mysite.com
  #        Respect proxy_pass type settings, `proxy_set_header Host "localhost";` and `proxy_set_header X-Forwarded-Prefix /app;`
  #        More here https://github.com/dgtlmoon/changedetection.io/wiki/Running-changedetection.io-behind-a-reverse-proxy
  #      - USE_X_SETTINGS=1
  #
  #        Hides the `Referer` header so that monitored websites can't see the changedetection.io hostname.
  #      - HIDE_REFERER=true
  #        
  #        Default number of parallel/concurrent fetchers
  #      - FETCH_WORKERS=10
  #
  #        Absolute minimum seconds to recheck, overrides any watch minimum, change to 0 to disable
  #      - MINIMUM_SECONDS_RECHECK_TIME=3
  #
  #        If you want to watch local files file:///path/to/file.txt (careful! security implications!)
  #      - ALLOW_FILE_URI=False
  #
  #        For complete privacy if you don't want to use the 'check version' / telemetry service
  #      - DISABLE_VERSION_CHECK=true
  #
  #        A valid timezone name to run as (for scheduling watch checking) see https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
  #      - TZ=America/Los_Angeles
  #
  #        Maximum height of screenshots, default is 16000 px, screenshots will be clipped to this if exceeded.
  #        RAM usage will be higher if you increase this.
  #      - SCREENSHOT_MAX_HEIGHT=16000
  #
  #        HTTPS SSL Mode for webserver, unset both of these, you may need to volume mount these files also.
  #        ./cert.pem:/app/cert.pem and ./privkey.pem:/app/privkey.pem
  #      - SSL_CERT_FILE=cert.pem
  #      - SSL_PRIVKEY_FILE=privkey.pem
  #
  #        LISTEN_HOST / "host", Same as -h
  #      - LISTEN_HOST=::
  #      - LISTEN_HOST=0.0.0.0

  
      # Comment out ports: when using behind a reverse proxy , enable networks: etc.
      ports:
        - 127.0.0.1:5000:5000
      restart: unless-stopped

     # Used for fetching pages via WebDriver+Chrome where you need Javascript support.
     # Now working on arm64 (needs testing on rPi - tested on Oracle ARM instance)
     # replace image with seleniarm/standalone-chromium:4.0.0-20211213
     
     # If WEBDRIVER or PLAYWRIGHT are enabled, changedetection container depends on that
     # and must wait before starting (substitute "browser-chrome" with "playwright-chrome" if last one is used)
#      depends_on:
#          browser-sockpuppet-chrome:
#              condition: service_started


     # Sockpuppetbrowser is basically chrome wrapped in an API for allowing fast fetching of web-pages.
     # RECOMMENDED FOR FETCHING PAGES WITH CHROME, be sure to enable the "PLAYWRIGHT_DRIVER_URL" env variable in the main changedetection container
#    browser-sockpuppet-chrome:
#        hostname: browser-sockpuppet-chrome
#        image: dgtlmoon/sockpuppetbrowser:latest
#        cap_add:
#            - SYS_ADMIN
## SYS_ADMIN might be too much, but it can be needed on your platform https://github.com/puppeteer/puppeteer/blob/main/docs/troubleshooting.md#running-puppeteer-on-gitlabci
#        restart: unless-stopped
#        environment:
#            - SCREEN_WIDTH=1920
#            - SCREEN_HEIGHT=1024
#            - SCREEN_DEPTH=16
#            - MAX_CONCURRENT_CHROME_PROCESSES=10

     # Used for fetching pages via Playwright+Chrome where you need Javascript support.
     # Note: Works well but is deprecated, does not fetch full page screenshots (doesnt work with Visual Selector)
     #       Does not report status codes (200, 404, 403) and other issues
#    browser-selenium-chrome:
#        hostname: browser-selenium-chrome
#        image: selenium/standalone-chrome:4
#        environment:
#            - VNC_NO_PASSWORD=1
#            - SCREEN_WIDTH=1920
#            - SCREEN_HEIGHT=1080
#            - SCREEN_DEPTH=24
#          CHROME_OPTIONS: |
#            --window-size=1280,1024
#            --headless
#            --disable-gpu
#        volumes:
#            # Workaround to avoid the browser crashing inside a docker container
#            # See https://github.com/SeleniumHQ/docker-selenium#quick-start
#            - /dev/shm:/dev/shm
#        restart: unless-stopped

volumes:
  changedetection-data:

