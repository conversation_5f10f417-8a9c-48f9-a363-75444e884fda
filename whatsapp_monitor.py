#!/usr/bin/env python3
"""
Complete WhatsApp Group Monitor
1. Opens WhatsApp
2. Lists available groups
3. Let user select group to monitor
4. Monitors for new messages
"""

import json
import time
from datetime import datetime
from playwright.sync_api import sync_playwright
from whatsapp_parser import WhatsAppMessageParser

class WhatsAppMonitor:
    def __init__(self):
        self.page = None
        self.browser = None
        self.parser = WhatsAppMessageParser()
        self.monitoring_start_time = None
        self.last_message_count = 0
        
    def connect_to_chrome(self):
        """Connect to existing Chrome with debugging"""
        try:
            playwright = sync_playwright().start()
            self.browser = playwright.chromium.connect_over_cdp("http://localhost:9222")

            # Try to find existing WhatsApp tab first
            context = self.browser.contexts[0]
            existing_pages = context.pages

            whatsapp_page = None
            for page in existing_pages:
                if 'web.whatsapp.com' in page.url:
                    whatsapp_page = page
                    break

            if whatsapp_page:
                print("✅ Found existing WhatsApp tab")
                self.page = whatsapp_page
            else:
                print("📄 Creating new WhatsApp tab")
                self.page = context.new_page()

            print("✅ Connected to Chrome")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to Chrome: {e}")
            print("💡 Make sure Chrome is running with: ./restart_chrome_debug.sh")
            return False
    
    def open_whatsapp(self):
        """Navigate to WhatsApp Web or use existing tab"""
        try:
            # If already on WhatsApp, just check if logged in
            if 'web.whatsapp.com' in self.page.url:
                print("✅ Already on WhatsApp Web")
            else:
                print("🔄 Navigating to WhatsApp Web...")
                self.page.goto("https://web.whatsapp.com/", wait_until="networkidle")

            # Wait for page to load
            time.sleep(3)

            # Check if logged in
            try:
                self.page.wait_for_selector('[data-testid="chat-list"]', timeout=10000)
                print("✅ WhatsApp Web loaded successfully")
                return True
            except:
                print("⚠️  Please scan QR code to login...")
                try:
                    self.page.wait_for_selector('[data-testid="qr-code"]', timeout=5000)
                    print("📱 QR code detected - please scan with your phone")
                    print("⏳ Waiting for login...")
                    self.page.wait_for_selector('[data-testid="chat-list"]', timeout=60000)
                    print("✅ Login successful!")
                    return True
                except:
                    print("❌ Login timeout or failed")
                    return False

        except Exception as e:
            print(f"❌ Error opening WhatsApp: {e}")
            return False
    
    def get_group_list(self):
        """Get list of available groups"""
        try:
            print("📋 Getting list of groups...")
            
            # Wait for chat list to be visible
            self.page.wait_for_selector('[data-testid="chat-list"]', timeout=10000)
            
            # Get all chat items
            chat_elements = self.page.query_selector_all('[data-testid="chat-list"] div[role="listitem"]')
            
            groups = []
            for i, chat_elem in enumerate(chat_elements):
                try:
                    # Get chat title
                    title_elem = chat_elem.query_selector('span[title]')
                    if title_elem:
                        title = title_elem.get_attribute('title')
                        if title:
                            groups.append({
                                'index': i,
                                'name': title,
                                'element': chat_elem
                            })
                except:
                    continue
            
            print(f"✅ Found {len(groups)} chats/groups:")
            for i, group in enumerate(groups, 1):
                print(f"  {i}. {group['name']}")
            
            return groups
            
        except Exception as e:
            print(f"❌ Error getting groups: {e}")
            return []
    
    def select_group(self, groups):
        """Let user select which group to monitor"""
        if not groups:
            print("❌ No groups found")
            return None
        
        print(f"\n📋 Available chats/groups:")
        for i, group in enumerate(groups, 1):
            print(f"  {i}. {group['name']}")
        
        while True:
            try:
                choice = input(f"\n🎯 Select group to monitor (1-{len(groups)}): ").strip()
                index = int(choice) - 1
                
                if 0 <= index < len(groups):
                    selected = groups[index]
                    print(f"✅ Selected: {selected['name']}")
                    return selected
                else:
                    print(f"❌ Please enter a number between 1 and {len(groups)}")
            except ValueError:
                print("❌ Please enter a valid number")
            except KeyboardInterrupt:
                print("\n👋 Cancelled")
                return None
    
    def click_group(self, group):
        """Click on selected group to open it"""
        try:
            print(f"👆 Clicking on group: {group['name']}")
            
            # Click on the group
            group['element'].click()
            
            # Wait for conversation to load
            self.page.wait_for_selector('[data-testid="conversation-panel-messages"]', timeout=10000)
            
            # Scroll to bottom to see latest messages
            self.page.evaluate("""
                const messagesContainer = document.querySelector('[data-testid="conversation-panel-messages"]');
                if (messagesContainer) {
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }
            """)
            
            time.sleep(2)
            print(f"✅ Opened group: {group['name']}")
            
            # Record monitoring start time and initial message count
            self.monitoring_start_time = datetime.now()
            initial_messages = self.get_current_messages()
            self.last_message_count = len(initial_messages) if initial_messages else 0
            
            print(f"🕐 Monitoring started at: {self.monitoring_start_time.strftime('%H:%M:%S')}")
            print(f"📊 Initial message count: {self.last_message_count}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error clicking group: {e}")
            return False
    
    def get_current_messages(self):
        """Get current messages from the conversation"""
        try:
            html_content = self.page.content()
            messages = self.parser.parse_messages(html_content)
            return messages
        except Exception as e:
            print(f"❌ Error getting messages: {e}")
            return []
    
    def get_new_messages(self):
        """Get only NEW messages since monitoring started"""
        try:
            current_messages = self.get_current_messages()
            
            if not current_messages:
                return []
            
            # If we have more messages than before, return the new ones
            if len(current_messages) > self.last_message_count:
                new_messages = current_messages[self.last_message_count:]
                self.last_message_count = len(current_messages)
                return new_messages
            
            return []
            
        except Exception as e:
            print(f"❌ Error getting new messages: {e}")
            return []
    
    def start_monitoring(self, check_interval=5):
        """Start monitoring for new messages"""
        print(f"\n👀 Starting monitoring (checking every {check_interval} seconds)")
        print("Press Ctrl+C to stop monitoring")
        
        try:
            while True:
                new_messages = self.get_new_messages()
                
                if new_messages:
                    timestamp = datetime.now().strftime('%H:%M:%S')
                    print(f"\n🆕 [{timestamp}] Found {len(new_messages)} new messages:")
                    
                    for i, msg in enumerate(new_messages, 1):
                        print(f"  {i}. {msg}")
                    
                    # Save to file
                    self.save_new_messages(new_messages)
                    
                    # Here you could send to your pipeline
                    # self.send_to_pipeline(new_messages)
                
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print(f"\n👋 Monitoring stopped at {datetime.now().strftime('%H:%M:%S')}")
        except Exception as e:
            print(f"❌ Monitoring error: {e}")
    
    def save_new_messages(self, messages):
        """Save new messages to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"new_messages_{timestamp}.json"
        
        data = {
            "timestamp": datetime.now().isoformat(),
            "messages": messages,
            "count": len(messages)
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Saved to {filename}")
    
    def close(self):
        """Clean up resources"""
        if self.page:
            self.page.close()
        if self.browser:
            self.browser.close()

def main():
    monitor = WhatsAppMonitor()
    
    try:
        # Step 1: Connect to Chrome
        if not monitor.connect_to_chrome():
            return
        
        # Step 2: Open WhatsApp
        if not monitor.open_whatsapp():
            return
        
        # Step 3: Get group list
        groups = monitor.get_group_list()
        if not groups:
            return
        
        # Step 4: Let user select group
        selected_group = monitor.select_group(groups)
        if not selected_group:
            return
        
        # Step 5: Click group and start monitoring
        if monitor.click_group(selected_group):
            monitor.start_monitoring()
        
    finally:
        monitor.close()

if __name__ == "__main__":
    print("🚀 WhatsApp Group Monitor")
    print("=" * 30)
    main()
