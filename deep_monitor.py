#!/usr/bin/env python3
"""
Deep Real-time WhatsApp Group Monitor
Monitors "let me test, pls" group with 1-minute intervals
"""

import subprocess
import sys
import json
import time
from datetime import datetime
import os

class WhatsAppMonitor:
    def __init__(self, group_name="let me test, pls", interval=60):
        self.group_name = group_name
        self.interval = interval
        self.previous_messages = []
        self.message_history = []
        self.monitoring = False
        
    def run_playwright_script(self, script_content):
        """Run playwright script in a separate process"""
        try:
            with open('/tmp/monitor_script.py', 'w') as f:
                f.write(script_content)
            
            result = subprocess.run([sys.executable, '/tmp/monitor_script.py'], 
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                return json.loads(result.stdout)
            else:
                return {'success': False, 'error': result.stderr}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def get_all_messages(self):
        """Get ALL messages from the target group"""
        script = f'''
import json
from playwright.sync_api import sync_playwright
import time

try:
    with sync_playwright() as p:
        browser = p.chromium.connect_over_cdp("http://localhost:9222")
        context = browser.contexts[0]
        pages = context.pages
        
        whatsapp_page = None
        for page in pages:
            try:
                if 'web.whatsapp.com' in page.url:
                    # Check if page is still valid
                    page.query_selector('body')  # Test if page is accessible
                    whatsapp_page = page
                    break
            except:
                continue

        if not whatsapp_page:
            result = {{'success': False, 'error': 'WhatsApp page not found or not accessible', 'messages': []}}
        else:
            # Wait for page to be ready and get chat elements
            try:
                whatsapp_page.wait_for_selector('#pane-side', timeout=10000)
                chat_elements = whatsapp_page.query_selector_all('#pane-side div[role="listitem"]')
            except:
                result = {{'success': False, 'error': 'Chat list not found or page not ready', 'messages': []}}
                print(json.dumps(result))
                exit()
            
            # Find target group
            target_index = -1
            for i, chat_elem in enumerate(chat_elements):
                try:
                    title_elem = chat_elem.query_selector('span[title]')
                    if title_elem:
                        title = title_elem.get_attribute('title') or title_elem.text_content()
                        if title and title.strip() == "{self.group_name}":
                            target_index = i
                            break
                except:
                    continue
            
            if target_index == -1:
                result = {{'success': False, 'error': 'Group not found', 'messages': []}}
            else:
                # Click the group
                chat_elements[target_index].click()
                time.sleep(3)
                
                # Check if conversation loaded
                conv_panel = whatsapp_page.query_selector('#main')
                if not conv_panel:
                    result = {{'success': False, 'error': 'Conversation panel not found', 'messages': []}}
                else:
                    # Scroll up to load more messages
                    try:
                        # Scroll up multiple times to load message history
                        for _ in range(5):
                            whatsapp_page.keyboard.press('PageUp')
                            time.sleep(0.5)
                    except:
                        pass
                    
                    # Get ALL message elements
                    message_elements = whatsapp_page.query_selector_all('div[role="row"]')
                    
                    messages = []
                    for i, msg_elem in enumerate(message_elements):
                        try:
                            # Extract message text
                            text_selectors = [
                                'span.selectable-text',
                                '.selectable-text',
                                'span[data-testid="msg-text"]'
                            ]
                            
                            message_text = None
                            for text_selector in text_selectors:
                                text_elem = msg_elem.query_selector(text_selector)
                                if text_elem:
                                    message_text = text_elem.text_content()
                                    if message_text and message_text.strip():
                                        message_text = message_text.strip()
                                        break
                            
                            # Extract timestamp
                            time_selectors = [
                                'span[data-testid="msg-time"]',
                                'div[data-testid="msg-meta"] span',
                                'span[title*=":"]'
                            ]
                            
                            timestamp = "Unknown"
                            for time_selector in time_selectors:
                                time_elem = msg_elem.query_selector(time_selector)
                                if time_elem:
                                    time_text = time_elem.get_attribute('title') or time_elem.text_content()
                                    if time_text and ':' in time_text:
                                        timestamp = time_text.strip()
                                        break
                            
                            # Extract sender info
                            sender_selectors = [
                                'span[data-testid="msg-author"]',
                                'div[data-testid="conversation-info-header"] span',
                                'span[dir="auto"][title]'
                            ]
                            
                            sender = "Unknown"
                            for sender_selector in sender_selectors:
                                sender_elem = msg_elem.query_selector(sender_selector)
                                if sender_elem:
                                    sender_text = sender_elem.get_attribute('title') or sender_elem.text_content()
                                    if sender_text and sender_text.strip() and len(sender_text.strip()) < 100:
                                        sender = sender_text.strip()
                                        break
                            
                            # Check if message is from you
                            is_outgoing = False
                            try:
                                outgoing_indicators = [
                                    'div[data-testid="msg-meta"][class*="tail-out"]',
                                    'div[class*="tail-out"]'
                                ]
                                for indicator in outgoing_indicators:
                                    if msg_elem.query_selector(indicator):
                                        is_outgoing = True
                                        sender = "You"
                                        break
                            except:
                                pass
                            
                            if message_text and len(message_text) > 0:
                                # Create unique message ID based on content and position
                                message_id = f"{{i}}_{{hash(message_text[:50])}}"
                                
                                messages.append({{
                                    'id': message_id,
                                    'index': i,
                                    'sender': sender,
                                    'text': message_text,
                                    'timestamp': timestamp,
                                    'is_outgoing': is_outgoing,
                                    'raw_html': str(msg_elem.inner_html()[:200]) if msg_elem else "No HTML"
                                }})
                                
                        except Exception as e:
                            # Still add error entries to track issues
                            messages.append({{
                                'id': f"error_{{i}}",
                                'index': i,
                                'sender': 'Error',
                                'text': f'Error extracting message: {{str(e)}}',
                                'timestamp': 'Unknown',
                                'is_outgoing': False,
                                'raw_html': 'Error'
                            }})
                    
                    result = {{
                        'success': True, 
                        'error': None, 
                        'messages': messages, 
                        'total_found': len(message_elements),
                        'extracted_count': len([m for m in messages if not m['id'].startswith('error_')])
                    }}
        
        print(json.dumps(result))

except Exception as e:
    print(json.dumps({{'success': False, 'error': str(e), 'messages': []}}))
'''
        
        return self.run_playwright_script(script)

    def detect_new_messages(self, current_messages):
        """Detect new messages by comparing with previous scan"""
        if not self.previous_messages:
            # First run - all messages are "new"
            return current_messages
        
        # Create sets of message IDs for comparison
        previous_ids = {msg['id'] for msg in self.previous_messages}
        current_ids = {msg['id'] for msg in current_messages}
        
        # Find new message IDs
        new_ids = current_ids - previous_ids
        
        # Return new messages
        new_messages = [msg for msg in current_messages if msg['id'] in new_ids]
        return new_messages

    def log_messages(self, messages, is_new=False):
        """Log messages to console and file"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if is_new and messages:
            print(f"\n🚨 NEW MESSAGES DETECTED at {timestamp}")
            print("=" * 60)
            
            for msg in messages:
                status = "📤 OUT" if msg['is_outgoing'] else "📥 IN"
                print(f"{status} [{msg['timestamp']}] {msg['sender']}: {msg['text']}")
                
                # Log to file
                with open('whatsapp_monitor.log', 'a', encoding='utf-8') as f:
                    f.write(f"{timestamp} | NEW | {status} | [{msg['timestamp']}] {msg['sender']}: {msg['text']}\n")
        
        elif not is_new:
            print(f"\n📊 FULL SCAN at {timestamp}")
            print("=" * 60)
            print(f"Total messages: {len(messages)}")
            
            if messages:
                print("Recent messages:")
                # Show last 5 messages
                for msg in messages[-5:]:
                    status = "📤 OUT" if msg['is_outgoing'] else "📥 IN"
                    print(f"  {status} [{msg['timestamp']}] {msg['sender']}: {msg['text'][:100]}...")

    def start_monitoring(self):
        """Start the real-time monitoring loop"""
        print(f"🚀 Starting deep real-time monitoring for group: '{self.group_name}'")
        print(f"⏱️  Monitoring interval: {self.interval} seconds")
        print(f"📝 Log file: whatsapp_monitor.log")
        print("=" * 80)
        print("Press Ctrl+C to stop monitoring")
        print("=" * 80)
        
        self.monitoring = True
        scan_count = 0
        
        try:
            while self.monitoring:
                scan_count += 1
                print(f"\n🔍 SCAN #{scan_count} - {datetime.now().strftime('%H:%M:%S')}")
                
                # Get all messages
                result = self.get_all_messages()
                
                if result['success']:
                    current_messages = result['messages']
                    
                    # Detect new messages
                    new_messages = self.detect_new_messages(current_messages)
                    
                    if new_messages:
                        self.log_messages(new_messages, is_new=True)
                    else:
                        print("✅ No new messages")
                        if scan_count % 5 == 0:  # Show full status every 5 scans
                            self.log_messages(current_messages, is_new=False)
                    
                    # Update previous messages
                    self.previous_messages = current_messages
                    
                    # Add to history
                    self.message_history.append({
                        'timestamp': datetime.now().isoformat(),
                        'total_messages': len(current_messages),
                        'new_messages': len(new_messages)
                    })
                    
                else:
                    print(f"❌ Error: {result['error']}")
                
                # Wait for next scan
                print(f"⏳ Next scan in {self.interval} seconds...")
                time.sleep(self.interval)
                
        except KeyboardInterrupt:
            print(f"\n\n🛑 Monitoring stopped by user")
            self.stop_monitoring()
        except Exception as e:
            print(f"\n\n❌ Monitoring error: {e}")
            self.stop_monitoring()

    def stop_monitoring(self):
        """Stop monitoring and show summary"""
        self.monitoring = False
        
        print("\n" + "=" * 80)
        print("📊 MONITORING SUMMARY")
        print("=" * 80)
        
        if self.message_history:
            total_scans = len(self.message_history)
            total_new_messages = sum(h['new_messages'] for h in self.message_history)
            
            print(f"Total scans performed: {total_scans}")
            print(f"Total new messages detected: {total_new_messages}")
            print(f"Current total messages in group: {self.message_history[-1]['total_messages'] if self.message_history else 0}")
            print(f"Log file: whatsapp_monitor.log")
        
        print("👋 Monitoring session ended")

if __name__ == "__main__":
    # Initialize monitor
    monitor = WhatsAppMonitor(group_name="let me test, pls", interval=60)
    
    # Start monitoring
    monitor.start_monitoring()
