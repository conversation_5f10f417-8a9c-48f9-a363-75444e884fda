#!/usr/bin/env python3
"""
Simple test to get WhatsApp content and parse messages
"""

import json
import requests
from playwright.sync_api import sync_playwright
from bs4 import BeautifulSoup

def simple_whatsapp_test():
    """Simple test to get WhatsApp messages"""
    try:
        with sync_playwright() as p:
            # Connect to existing Chrome
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            
            # Create a new page and navigate to WhatsApp
            context = browser.contexts[0]
            page = context.new_page()
            
            print("🔄 Navigating to WhatsApp Web...")
            page.goto("https://web.whatsapp.com/", wait_until="networkidle")
            
            # Wait for page to load
            print("⏳ Waiting for page to load...")
            page.wait_for_timeout(5000)
            
            # Check if logged in
            try:
                page.wait_for_selector('[data-testid="chat-list"]', timeout=10000)
                print("✅ Logged in successfully")
            except:
                print("⚠️  Not logged in or QR code needed")
                # Try to wait for QR code
                try:
                    page.wait_for_selector('[data-testid="qr-code"]', timeout=5000)
                    print("📱 QR code detected - please scan to login")
                    print("⏳ Waiting 30 seconds for login...")
                    page.wait_for_timeout(30000)
                except:
                    print("❌ Could not detect login state")
            
            # Get page content
            print("📄 Getting page content...")
            html_content = page.content()
            
            # Save for debugging
            with open('whatsapp_page.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print("💾 Saved page content to whatsapp_page.html")
            
            # Parse with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Look for messages with different selectors
            message_selectors = [
                '[data-testid="msg-container"]',
                'div[data-id]',
                '.message-in',
                '.message-out',
                'span[dir="ltr"]'
            ]
            
            all_messages = []
            
            print("\n🔍 Looking for messages...")
            for selector in message_selectors:
                elements = soup.select(selector)
                print(f"  {selector}: {len(elements)} elements")
                
                if elements:
                    for elem in elements[:3]:  # Show first 3
                        text = elem.get_text().strip()
                        if text and len(text) > 5:
                            print(f"    Sample: {text[:100]}")
                            all_messages.append(text)
            
            # Try to find any text that looks like messages
            print("\n📝 Looking for any text content...")
            all_text = soup.get_text()
            lines = [line.strip() for line in all_text.split('\n') if line.strip()]
            
            # Filter for potential messages (lines with reasonable length)
            potential_messages = [line for line in lines if 10 < len(line) < 200]
            
            print(f"Found {len(potential_messages)} potential message lines:")
            for i, msg in enumerate(potential_messages[:10]):
                print(f"  {i+1}. {msg}")
            
            # Create simple output
            if potential_messages:
                output = {
                    "messages": potential_messages[:20],  # First 20 potential messages
                    "total_found": len(potential_messages),
                    "source": "whatsapp_web_simple"
                }
                
                with open('simple_messages.json', 'w', encoding='utf-8') as f:
                    json.dump(output, f, indent=2, ensure_ascii=False)
                
                print(f"\n💾 Saved {len(potential_messages)} potential messages to simple_messages.json")
                return output
            
            page.close()
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🧪 Simple WhatsApp Test")
    print("=" * 30)
    
    result = simple_whatsapp_test()
    
    if result:
        print(f"\n🎉 Found {result['total_found']} potential messages!")
        print("📁 Check simple_messages.json for results")
    else:
        print("\n❌ No messages found")
