"""
Integration code for WhatsApp monitoring in ChangeDetection.io
"""

# Add this to your changedetection.io content fetcher or processor

def whatsapp_message_processor(html_content, watch_uuid, datastore):
    """
    Process WhatsApp Web page content and extract new messages
    This function integrates with ChangeDetection.io's change detection system
    """
    from whatsapp_parser import WhatsAppMessageParser
    import json
    import requests
    
    # Initialize parser (you might want to store this per watch)
    parser = WhatsAppMessageParser()
    
    # Extract new messages
    new_messages = parser.get_new_messages_only(html_content)
    
    if new_messages:
        # Create payload
        payload = parser.create_pipeline_payload(new_messages)
        
        # Send to your pipeline webhook
        webhook_url = "https://your-pipeline.com/api/whatsapp/messages"
        
        try:
            response = requests.post(
                webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"Successfully sent {len(new_messages)} messages to pipeline")
            else:
                print(f"Pipeline webhook failed: {response.status_code}")
                
        except Exception as e:
            print(f"Error sending to pipeline: {e}")
        
        return payload
    
    return None

# Browser Steps for WhatsApp Web (add to your watch configuration)
WHATSAPP_BROWSER_STEPS = """
// Navigate to WhatsApp Web
await page.goto('https://web.whatsapp.com/');

// Wait for QR code or chat list (if already logged in)
try {
    await page.waitForSelector('[data-testid="chat-list"]', { timeout: 10000 });
} catch {
    // If QR code appears, wait for manual scan
    await page.waitForSelector('[data-testid="qr-code"]', { timeout: 5000 });
    console.log('Please scan QR code to login');
    await page.waitForSelector('[data-testid="chat-list"]', { timeout: 60000 });
}

// Click on specific group (replace "Your Group Name" with actual group name)
const groupName = "Supplier Customer Group";
await page.click(`[title="${groupName}"]`);

// Wait for messages to load
await page.waitForSelector('[data-testid="conversation-panel-messages"]');

// Scroll to bottom to get latest messages
await page.evaluate(() => {
    const messagesContainer = document.querySelector('[data-testid="conversation-panel-messages"]');
    if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
});

// Wait a bit for messages to fully load
await page.waitForTimeout(2000);
"""

# CSS Selector for monitoring (add to your watch)
WHATSAPP_CSS_SELECTOR = '[data-testid="conversation-panel-messages"]'

# Example webhook payload that will be sent to your pipeline
EXAMPLE_PAYLOAD = {
    "messages": [
        "John Supplier: I have 100 widgets available at $5 each",
        "Sarah Customer: What's the minimum order?",
        "John Supplier: 50 units minimum",
        "Sarah Customer: I'll take 75 units",
        "John Supplier: Deal! Send me your address"
    ],
    "timestamp": "2024-01-15T15:30:00.000Z",
    "source": "whatsapp_web",
    "count": 5
}
