"""
Chrome Session Hijacking for WhatsApp monitoring
"""

import json
import os
import time
from datetime import datetime
from whatsapp_parser import WhatsAppMessageParser

def save_messages_locally(messages, group_name="whatsapp_group"):
    """Save messages to local JSON file"""
    timestamp = datetime.now().strftime("%Y%m%d")
    filename = f"whatsapp_messages_{group_name}_{timestamp}.json"

    # Load existing messages if file exists
    if os.path.exists(filename):
        with open(filename, 'r', encoding='utf-8') as f:
            existing_data = json.load(f)
    else:
        existing_data = {"messages": [], "last_updated": None}

    # Append new messages
    for msg in messages:
        existing_data["messages"].append({
            "content": msg,
            "timestamp": datetime.now().isoformat()
        })

    existing_data["last_updated"] = datetime.now().isoformat()
    existing_data["total_count"] = len(existing_data["messages"])

    # Save back to file
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(existing_data, f, indent=2, ensure_ascii=False)

    print(f"Saved {len(messages)} new messages to {filename}")
    return filename

def simple_retry(func, max_retries=3, delay=1):
    """Simple retry mechanism"""
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            print(f"Attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                time.sleep(delay)
            else:
                print(f"All {max_retries} attempts failed")
                raise

def whatsapp_message_processor(html_content, watch_uuid=None, datastore=None):
    """
    Process WhatsApp Web page content and save messages locally
    """
    parser = WhatsAppMessageParser()

    def extract_and_save():
        # Extract new messages
        new_messages = parser.get_new_messages_only(html_content)

        if new_messages:
            # Save locally with retry
            filename = save_messages_locally(new_messages)

            # Create payload for potential future use
            payload = parser.create_pipeline_payload(new_messages)

            print(f"Processed {len(new_messages)} new messages")
            return payload

        return None

    # Use simple retry
    return simple_retry(extract_and_save)

# Chrome Session Hijacking Configuration
CHROME_DEBUG_PORT = 9222  # Default Chrome debugging port

def get_chrome_session_config():
    """Configuration for connecting to existing Chrome session"""
    return {
        "browser_connection_url": f"http://localhost:{CHROME_DEBUG_PORT}",
        "use_existing_session": True,
        "headless": False  # Use visible Chrome
    }

# Browser Steps for existing Chrome session
WHATSAPP_BROWSER_STEPS = """
// Connect to existing Chrome session - no need to navigate if already on WhatsApp
// Just ensure we're on the right group

// Check if we're already on WhatsApp Web
if (!window.location.href.includes('web.whatsapp.com')) {
    await page.goto('https://web.whatsapp.com/');
    await page.waitForSelector('[data-testid="chat-list"]', { timeout: 30000 });
}

// Click on specific group if not already selected
const groupName = "Your Group Name Here";  // Replace with actual group name
try {
    const groupElement = await page.$(`[title*="${groupName}"]`);
    if (groupElement) {
        await groupElement.click();
        await page.waitForSelector('[data-testid="conversation-panel-messages"]');
    }
} catch (e) {
    console.log('Group already selected or not found');
}

// Scroll to bottom to get latest messages
await page.evaluate(() => {
    const messagesContainer = document.querySelector('[data-testid="conversation-panel-messages"]');
    if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
});

await page.waitForTimeout(1000);
"""

# CSS Selector for monitoring (add to your watch)
WHATSAPP_CSS_SELECTOR = '[data-testid="conversation-panel-messages"]'

# Example webhook payload that will be sent to your pipeline
EXAMPLE_PAYLOAD = {
    "messages": [
        "John Supplier: I have 100 widgets available at $5 each",
        "Sarah Customer: What's the minimum order?",
        "John Supplier: 50 units minimum",
        "Sarah Customer: I'll take 75 units",
        "John Supplier: Deal! Send me your address"
    ],
    "timestamp": "2024-01-15T15:30:00.000Z",
    "source": "whatsapp_web",
    "count": 5
}
