#!/usr/bin/env python3
"""
Simple script to list WhatsApp groups from current page
"""

from playwright.sync_api import sync_playwright
import time

def get_whatsapp_groups():
    """Get groups from current WhatsApp page"""
    try:
        with sync_playwright() as p:
            # Connect to existing Chrome
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            
            # Find existing WhatsApp tab
            context = browser.contexts[0]
            pages = context.pages
            
            whatsapp_page = None
            for page in pages:
                try:
                    if 'web.whatsapp.com' in page.url:
                        whatsapp_page = page
                        break
                except:
                    continue
            
            if not whatsapp_page:
                print("❌ No WhatsApp tab found")
                print("💡 Please open WhatsApp Web in Chrome first")
                return []
            
            print(f"✅ Found WhatsApp tab: {whatsapp_page.url}")
            
            # Wait a bit for page to be ready
            time.sleep(2)
            
            # Check if logged in by looking for multiple indicators
            try:
                # Try multiple selectors to detect login state
                login_indicators = [
                    '[data-testid="chat-list"]',
                    '[data-testid="side"]',
                    '#side',
                    'div[role="application"]',
                    '[data-testid="chat-list-search"]'
                ]

                logged_in = False
                for selector in login_indicators:
                    element = whatsapp_page.query_selector(selector)
                    if element:
                        print(f"✅ Found login indicator: {selector}")
                        logged_in = True
                        break

                if not logged_in:
                    # Check for QR code (indicates not logged in)
                    qr_selectors = ['canvas[aria-label*="QR"]', '[data-testid="qr-code"]', 'canvas']
                    for selector in qr_selectors:
                        qr_element = whatsapp_page.query_selector(selector)
                        if qr_element:
                            print(f"❌ Found QR code - not logged in")
                            print("💡 Please scan QR code to log into WhatsApp Web")
                            return []

                    print("❌ Not logged in or chat list not found")
                    print("💡 Please log into WhatsApp Web first")
                    return []

            except Exception as e:
                print(f"❌ Error checking login status: {e}")
                return []
            
            print("✅ Chat list found - getting groups...")
            
            # Get all chat items - try multiple selectors
            try:
                chat_selectors = [
                    '[data-testid="chat-list"] div[role="listitem"]',
                    '[data-testid="chat-list"] > div > div',
                    '#pane-side div[role="listitem"]',
                    '[data-testid="chat-list"] div[tabindex]'
                ]

                chat_elements = []
                for selector in chat_selectors:
                    chat_elements = whatsapp_page.query_selector_all(selector)
                    if chat_elements:
                        print(f"✅ Found {len(chat_elements)} chat items using selector: {selector}")
                        break

                if not chat_elements:
                    print("❌ No chat elements found with any selector")
                    return []
                
                groups = []
                for i, chat_elem in enumerate(chat_elements):
                    try:
                        # Try different selectors for chat title
                        title_selectors = [
                            'span[title]',
                            '[data-testid="conversation-info-header-chat-title"]',
                            'span[dir="auto"]'
                        ]
                        
                        title = None
                        for selector in title_selectors:
                            title_elem = chat_elem.query_selector(selector)
                            if title_elem:
                                title = title_elem.get_attribute('title') or title_elem.text_content()
                                if title and title.strip():
                                    title = title.strip()
                                    break
                        
                        if title:
                            groups.append({
                                'index': i,
                                'name': title,
                                'element_index': i  # Store index for clicking later
                            })
                            
                    except Exception as e:
                        print(f"⚠️  Error processing chat {i}: {e}")
                        continue
                
                print(f"✅ Successfully parsed {len(groups)} chats:")
                for i, group in enumerate(groups, 1):
                    print(f"  {i}. {group['name']}")
                
                return groups
                
            except Exception as e:
                print(f"❌ Error getting chat elements: {e}")
                return []
            
    except Exception as e:
        print(f"❌ Error connecting to Chrome: {e}")
        return []

def test_click_group(groups, group_index):
    """Test clicking on a specific group"""
    if not groups or group_index >= len(groups):
        print("❌ Invalid group index")
        return False
    
    selected_group = groups[group_index]
    print(f"🎯 Testing click on: {selected_group['name']}")
    
    try:
        with sync_playwright() as p:
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            context = browser.contexts[0]
            pages = context.pages
            
            whatsapp_page = None
            for page in pages:
                if 'web.whatsapp.com' in page.url:
                    whatsapp_page = page
                    break
            
            if not whatsapp_page:
                print("❌ WhatsApp page not found")
                return False
            
            # Get chat elements again using the same selector that worked for listing
            chat_selectors = [
                '#pane-side div[role="listitem"]',
                '[data-testid="chat-list"] div[role="listitem"]',
                '[data-testid="chat-list"] > div > div',
                '[data-testid="chat-list"] div[tabindex]'
            ]

            chat_elements = []
            for selector in chat_selectors:
                chat_elements = whatsapp_page.query_selector_all(selector)
                if chat_elements:
                    print(f"✅ Found {len(chat_elements)} chat elements for clicking using: {selector}")
                    break
            
            if selected_group['element_index'] < len(chat_elements):
                target_element = chat_elements[selected_group['element_index']]
                
                print(f"👆 Clicking on group...")
                target_element.click()
                
                # Wait for conversation to load
                time.sleep(3)
                
                # Check if conversation panel loaded - try multiple selectors
                try:
                    conv_selectors = [
                        '[data-testid="conversation-panel-messages"]',
                        '[data-testid="main"]',
                        '#main',
                        'div[role="application"] div[role="main"]',
                        '[data-testid="conversation-panel"]'
                    ]

                    conv_panel = None
                    for selector in conv_selectors:
                        conv_panel = whatsapp_page.query_selector(selector)
                        if conv_panel:
                            print(f"✅ Successfully opened group conversation (found: {selector})")
                            return True

                    print("⚠️  Conversation panel not found with any selector")
                    return False
                except Exception as e:
                    print(f"⚠️  Could not verify conversation panel: {e}")
                    return False
            else:
                print("❌ Group element not found")
                return False
                
    except Exception as e:
        print(f"❌ Error clicking group: {e}")
        return False

if __name__ == "__main__":
    print("📋 WhatsApp Group Lister")
    print("=" * 30)
    
    # Get groups
    groups = get_whatsapp_groups()
    
    if groups:
        print(f"\n✅ Found {len(groups)} chats/groups")
        
        # Ask user if they want to test clicking
        try:
            choice = input(f"\n🎯 Test clicking on a group? Enter number (1-{len(groups)}) or 'n' to skip: ").strip()
            
            if choice.lower() != 'n':
                try:
                    group_index = int(choice) - 1
                    if 0 <= group_index < len(groups):
                        success = test_click_group(groups, group_index)
                        if success:
                            print("🎉 Group click test successful!")
                        else:
                            print("❌ Group click test failed")
                    else:
                        print("❌ Invalid group number")
                except ValueError:
                    print("❌ Invalid input")
        except KeyboardInterrupt:
            print("\n👋 Cancelled")
    else:
        print("❌ No groups found")
        print("💡 Make sure WhatsApp Web is open and logged in")
