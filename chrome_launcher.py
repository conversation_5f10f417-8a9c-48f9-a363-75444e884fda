#!/usr/bin/env python3
"""
Chrome Session Launcher for WhatsApp Monitoring
Starts Chrome with debugging enabled so ChangeDetection.io can hijack the session
"""

import subprocess
import sys
import os
import time
import platform

def get_chrome_path():
    """Get Chrome executable path based on OS"""
    system = platform.system()
    
    if system == "Darwin":  # macOS
        paths = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "/Applications/Chromium.app/Contents/MacOS/Chromium"
        ]
    elif system == "Windows":
        paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
        ]
    else:  # Linux
        paths = [
            "/usr/bin/google-chrome",
            "/usr/bin/chromium-browser",
            "/usr/bin/chromium",
            "/snap/bin/chromium"
        ]
    
    for path in paths:
        if os.path.exists(path.replace("%USERNAME%", os.getenv("USERNAME", ""))):
            return path
    
    return None

def launch_chrome_with_debugging(port=9222, user_data_dir=None):
    """Launch Chrome with remote debugging enabled"""
    
    chrome_path = get_chrome_path()
    if not chrome_path:
        print("Chrome not found! Please install Google Chrome.")
        return False
    
    # Create user data directory if not specified
    if not user_data_dir:
        user_data_dir = os.path.join(os.getcwd(), "chrome_session")
        os.makedirs(user_data_dir, exist_ok=True)
    
    # Chrome arguments for debugging
    chrome_args = [
        chrome_path,
        f"--remote-debugging-port={port}",
        f"--user-data-dir={user_data_dir}",
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "https://web.whatsapp.com"
    ]
    
    print(f"Launching Chrome with debugging on port {port}...")
    print(f"User data directory: {user_data_dir}")
    print("Chrome will open WhatsApp Web automatically")
    print("\nIMPORTANT:")
    print("1. Log into WhatsApp Web if not already logged in")
    print("2. Navigate to your target group")
    print("3. Keep this Chrome window open")
    print("4. ChangeDetection.io will connect to this session")
    
    try:
        # Launch Chrome
        process = subprocess.Popen(chrome_args)
        
        # Wait a bit for Chrome to start
        time.sleep(3)
        
        print(f"\n✅ Chrome launched successfully!")
        print(f"🔗 Debugging endpoint: http://localhost:{port}")
        print(f"📱 WhatsApp Web should be loading...")
        print("\nPress Ctrl+C to stop monitoring (but keep Chrome open)")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to launch Chrome: {e}")
        return False

def check_chrome_debugging(port=9222):
    """Check if Chrome debugging is available"""
    import requests
    try:
        response = requests.get(f"http://localhost:{port}/json", timeout=5)
        if response.status_code == 200:
            tabs = response.json()
            whatsapp_tabs = [tab for tab in tabs if 'web.whatsapp.com' in tab.get('url', '')]
            
            print(f"✅ Chrome debugging available on port {port}")
            print(f"📊 Total tabs: {len(tabs)}")
            print(f"💬 WhatsApp tabs: {len(whatsapp_tabs)}")
            
            if whatsapp_tabs:
                for tab in whatsapp_tabs:
                    print(f"   - {tab['title']}: {tab['url']}")
            
            return True
    except:
        pass
    
    print(f"❌ Chrome debugging not available on port {port}")
    return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Launch Chrome for WhatsApp monitoring")
    parser.add_argument("--port", type=int, default=9222, help="Chrome debugging port")
    parser.add_argument("--check", action="store_true", help="Check if Chrome debugging is available")
    parser.add_argument("--user-data-dir", help="Chrome user data directory")
    
    args = parser.parse_args()
    
    if args.check:
        check_chrome_debugging(args.port)
    else:
        success = launch_chrome_with_debugging(args.port, args.user_data_dir)
        
        if success:
            try:
                # Keep script running
                while True:
                    time.sleep(10)
                    # Optional: Check if Chrome is still running
                    if not check_chrome_debugging(args.port):
                        print("⚠️  Chrome debugging connection lost")
                        break
            except KeyboardInterrupt:
                print("\n👋 Monitoring stopped. Chrome session remains open.")
        else:
            sys.exit(1)
