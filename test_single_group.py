#!/usr/bin/env python3
"""
Test single group message extraction
"""

from test_all_groups import get_messages_from_group

if __name__ == "__main__":
    group_name = "Daniel Tan"
    
    print(f"🔍 Testing message extraction for: {group_name}")
    print("=" * 50)
    
    result = get_messages_from_group(group_name)
    
    if result['success']:
        messages = result['messages']
        total_found = result.get('total_found', 0)
        
        print(f"✅ Successfully accessed group ({total_found} total messages found)")
        
        if messages:
            print("📝 First 3 messages:")
            for msg in messages:
                print(f"  {msg['index']}. [{msg['sender']}]: {msg['text']}")
        else:
            print("📝 No messages found or couldn't extract message text")
    else:
        print(f"❌ Error: {result['error']}")
