"""
WhatsApp Web Message Parser for ChangeDetection.io
Extracts messages in simple format: user: text
"""

from bs4 import BeautifulSoup
import json
import re
from datetime import datetime

class WhatsAppMessageParser:
    def __init__(self):
        self.last_message_id = None
        self.seen_messages = set()
    
    def parse_messages(self, html_content):
        """
        Parse WhatsApp Web HTML and extract messages in format:
        ["user: text", "user1: text1", ...]
        """
        messages = []
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Find all message containers
        message_containers = soup.select('[data-testid="msg-container"]')
        
        for container in message_containers:
            message_data = self._extract_message_data(container)
            if message_data and message_data['id'] not in self.seen_messages:
                formatted_message = f"{message_data['sender']}: {message_data['text']}"
                messages.append(formatted_message)
                self.seen_messages.add(message_data['id'])
        
        return messages
    
    def _extract_message_data(self, container):
        """Extract sender, text, and ID from message container"""
        try:
            # Get message ID for deduplication
            msg_id = container.get('data-id') or container.get('id')
            if not msg_id:
                # Fallback: create ID from content hash
                msg_id = hash(str(container))
            
            # Extract sender name
            sender = self._extract_sender(container)
            if not sender:
                return None
            
            # Extract message text
            text = self._extract_text(container)
            if not text:
                return None
            
            return {
                'id': msg_id,
                'sender': sender,
                'text': text
            }
        except Exception as e:
            print(f"Error extracting message: {e}")
            return None
    
    def _extract_sender(self, container):
        """Extract sender name from various possible selectors"""
        # Try different selectors for sender name
        selectors = [
            '[data-testid="msg-meta"] span[dir="auto"]',
            '.message-author',
            '[data-testid="conversation-info-header-chat-title"]',
            'span[title][dir="auto"]',
            '[aria-label*="You"]',  # For own messages
            'span[dir="auto"]:first-child'  # First span in message
        ]

        for selector in selectors:
            sender_elem = container.select_one(selector)
            if sender_elem:
                sender = sender_elem.get_text().strip()
                # Clean up sender name (remove timestamps, etc.)
                sender = re.sub(r'\d{1,2}:\d{2}.*', '', sender).strip()
                if sender and len(sender) > 0:
                    return sender

        # Check if it's an outgoing message (from you)
        if 'message-out' in str(container) or 'tail-out' in str(container):
            return "You"

        # Check if it's an incoming message
        if 'message-in' in str(container) or 'tail-in' in str(container):
            return "Contact"

        return "Unknown"
    
    def _extract_text(self, container):
        """Extract message text content"""
        # Try different selectors for message text
        selectors = [
            '[data-testid="selectable-text"]',
            '.selectable-text',
            '.message-text',
            'span[dir="ltr"]',
            'span[lang]',  # Messages often have lang attribute
            '.copyable-text span'  # Another common structure
        ]

        for selector in selectors:
            text_elem = container.select_one(selector)
            if text_elem:
                text = text_elem.get_text().strip()
                # Clean up text (remove timestamps, status indicators)
                text = re.sub(r'\d{1,2}:\d{2}$', '', text).strip()  # Remove trailing timestamp
                text = re.sub(r'msg-dblcheck$', '', text).strip()  # Remove status indicators
                if text and len(text) > 0:
                    return text

        # Fallback: get all text from container and clean it
        all_text = container.get_text().strip()
        if all_text:
            # Remove common WhatsApp UI elements
            all_text = re.sub(r'\d{1,2}:\d{2}.*$', '', all_text).strip()
            all_text = re.sub(r'msg-dblcheck.*$', '', all_text).strip()
            all_text = re.sub(r'tail-(in|out)', '', all_text).strip()
            if len(all_text) > 3:
                return all_text

        return None
    
    def get_new_messages_only(self, html_content):
        """Get only new messages since last check"""
        all_messages = self.parse_messages(html_content)
        # Since we track seen_messages, parse_messages already returns only new ones
        return all_messages
    
    def create_pipeline_payload(self, messages):
        """Create JSON payload for pipeline"""
        return {
            "messages": messages,
            "timestamp": datetime.now().isoformat(),
            "source": "whatsapp_web",
            "count": len(messages)
        }

# Integration with ChangeDetection.io
def process_whatsapp_changes(html_content, previous_content=None):
    """
    Main function to be called by ChangeDetection.io
    Returns JSON payload for webhook
    """
    parser = WhatsAppMessageParser()
    
    # Get new messages
    new_messages = parser.get_new_messages_only(html_content)
    
    if new_messages:
        # Create payload for pipeline
        payload = parser.create_pipeline_payload(new_messages)
        return json.dumps(payload, indent=2)
    
    return None

# Example usage and testing
if __name__ == "__main__":
    # Test HTML structure (simplified)
    test_html = """
    <div data-testid="msg-container" data-id="msg1">
        <div data-testid="msg-meta">
            <span dir="auto">John Supplier</span>
        </div>
        <div data-testid="selectable-text">I have 100 widgets at $5 each</div>
    </div>
    <div data-testid="msg-container" data-id="msg2">
        <div data-testid="msg-meta">
            <span dir="auto">Sarah Customer</span>
        </div>
        <div data-testid="selectable-text">What's the minimum order?</div>
    </div>
    """
    
    parser = WhatsAppMessageParser()
    messages = parser.parse_messages(test_html)
    payload = parser.create_pipeline_payload(messages)
    
    print("Extracted Messages:")
    print(json.dumps(payload, indent=2))
