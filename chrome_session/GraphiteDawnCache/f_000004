F      
                    #ifdef __clang__
                    #pragma clang diagnostic ignored "-Wall"
                    #endif
                #include <metal_stdlib>
using namespace metal;

template<typename T, size_t N>
struct tint_array {
  const constant T& operator[](size_t i) const constant { return elements[i]; }
  device T& operator[](size_t i) device { return elements[i]; }
  const device T& operator[](size_t i) const device { return elements[i]; }
  thread T& operator[](size_t i) thread { return elements[i]; }
  const thread T& operator[](size_t i) const thread { return elements[i]; }
  threadgroup T& operator[](size_t i) threadgroup { return elements[i]; }
  const threadgroup T& operator[](size_t i) const threadgroup { return elements[i]; }
  T elements[N];
};

struct tint_struct {
  float4 tint_member;
  uint2 tint_member_1;
  float2 tint_member_2;
};

struct tint_struct_1 {
  float4 tint_member_3;
};

struct tint_struct_5 {
  /* 0x0000 */ packed_float3 tint_member_14;
  /* 0x000c */ tint_array<int8_t, 4> tint_pad_2;
};

struct tint_struct_4 {
  /* 0x0000 */ float4 tint_member_6;
  /* 0x0010 */ float2 tint_member_7;
  /* 0x0018 */ tint_array<int8_t, 8> tint_pad;
  /* 0x0020 */ tint_array<float4, 4> tint_member_8;
  /* 0x0060 */ float4 tint_member_9;
  /* 0x0070 */ int tint_member_10;
  /* 0x0074 */ int tint_member_11;
  /* 0x0078 */ int tint_member_12;
  /* 0x007c */ tint_array<int8_t, 4> tint_pad_1;
  /* 0x0080 */ tint_array<tint_struct_5, 3> tint_member_13;
  /* 0x00b0 */ float4 tint_member_15;
  /* 0x00c0 */ float4 tint_member_16;
  /* 0x00d0 */ float4 tint_member_17;
  /* 0x00e0 */ float4 tint_member_18;
  /* 0x00f0 */ float tint_member_19;
  /* 0x00f4 */ tint_array<int8_t, 12> tint_pad_3;
  /* 0x0100 */ float4 tint_member_20;
  /* 0x0110 */ float2 tint_member_21;
  /* 0x0118 */ tint_array<int8_t, 8> tint_pad_4;
  /* 0x0120 */ float4 tint_member_22;
};

struct tint_struct_3 {
  /* 0x0000 */ tint_array<tint_struct_4, 1> tint_member_5;
};

struct tint_struct_6 {
  /* 0x0000 */ float4 tint_member_24;
  /* 0x0010 */ float4 tint_member_25;
};

struct tint_struct_2 {
  const device tint_struct_3* tint_member_4;
  const constant tint_struct_6* tint_member_23;
  thread uint* tint_member_26;
  sampler tint_member_27;
  texture2d<float, access::sample> tint_member_28;
};

struct tint_struct_7 {
  float4 tint_member_29 [[color(0)]];
};

struct tint_struct_8 {
  uint2 tint_member_30 [[user(locn0)]] [[flat]];
  float2 tint_member_31 [[user(locn1)]];
};

float3 v(float3 v_1) {
  float const v_2 = (v_1.x * 0.01745329238474369049f);
  float const v_3 = cos(v_2);
  float const v_4 = (v_1.x * 0.01745329238474369049f);
  float const v_5 = sin(v_4);
  return float3(v_1.z, (v_1.y * v_3), (v_1.y * v_5));
}

float3 v_6(float3 v_7) {
  float3 v_8 = v_7;
  float const v_9 = v_8.x;
  float const v_10 = (v_9 - (360.0f * floor((v_9 / 360.0f))));
  v_8.x = v_10;
  if ((v_8.x < 0.0f)) {
    v_8.x = (v_8.x + 360.0f);
  }
  v_8 = float3(v_8.x, (v_8.yz * 0.00999999977648258209f));
  float3 const v_11 = (float3(0.0f, 8.0f, 4.0f) + (v_8.x * 0.03333333507180213928f));
  float3 const v_12 = (v_11 - (12.0f * floor((v_11 / 12.0f))));
  float3 const v_13 = v_12;
  float const v_14 = min(v_8.z, (1.0f - v_8.z));
  float const v_15 = (v_8.y * v_14);
  float3 const v_16 = min((v_13 - 3.0f), (9.0f - v_13));
  float3 const v_17 = clamp(v_16, float3(-1.0f), float3(1.0f));
  return (v_8.z - (v_15 * v_17));
}

float3 v_18(float3 v_19) {
  float3 v_20 = 0.0f;
  v_20.y = ((v_19.x + 16.0f) * 0.00862068962305784225f);
  v_20.x = ((v_19.y * 0.00200000009499490261f) + v_20.y);
  v_20.z = (v_20.y - (v_19.z * 0.00499999988824129105f));
  float3 const v_21 = powr(v_20, float3(3.0f));
  float3 const v_22 = v_21;
  float v_23 = 0.0f;
  if ((v_22.x > 0.00885645207017660141f)) {
    v_23 = v_22.x;
  } else {
    v_23 = (((116.0f * v_20.x) - 16.0f) * 0.00110705639235675335f);
  }
  float v_24 = 0.0f;
  if ((v_19.x > 8.00000095367431640625f)) {
    v_24 = v_22.y;
  } else {
    v_24 = (v_19.x * 0.00110705639235675335f);
  }
  float v_25 = 0.0f;
  if ((v_22.z > 0.00885645207017660141f)) {
    v_25 = v_22.z;
  } else {
    v_25 = (((116.0f * v_20.z) - 16.0f) * 0.00110705639235675335f);
  }
  float3 const v_26 = float3(v_23, v_24, v_25);
  return (v_26 * float3(0.96429562568664550781f, 1.0f, 0.82510453462600708008f));
}

float3 v_27(float3 v_28) {
  float const v_29 = ((v_28.x + (0.39633777737617492676f * v_28.y)) + (0.21580375730991363525f * v_28.z));
  float const v_30 = ((v_28.x - (0.10556134581565856934f * v_28.y)) - (0.06385417282581329346f * v_28.z));
  float const v_31 = ((v_28.x - (0.08948417752981185913f * v_28.y)) - (1.29148554801940917969f * v_28.z));
  float const v_32 = ((v_29 * v_29) * v_29);
  float const v_33 = ((v_30 * v_30) * v_30);
  float const v_34 = ((v_31 * v_31) * v_31);
  return float3((((4.07674169540405273438f * v_32) - (3.30771160125732421875f * v_33)) + (0.23096993565559387207f * v_34)), (((-1.26843798160552978516f * v_32) + (2.60975742340087890625f * v_33)) - (0.3413193821907043457f * v_34)), (((-0.00419608643278479576f * v_32) - (0.70341861248016357422f * v_33)) + (1.70761466026306152344f * v_34)));
}

float3 v_35(float3 v_36) {
  float3 v_37 = v_36;
  float2 const v_38 = v_37.yz;
  float v_39 = 0.0f;
  float2 v_40 = 0.0f;
  float2 v_41 = 0.0f;
  float const v_42 = dot(v_38, float2(0.40970200300216674805f, -0.91221898794174194336f));
  if ((v_42 < 0.0f)) {
    float const v_43 = dot(v_38, float2(0.46027600765228271484f, 0.88777601718902587891f));
    if ((v_43 < 0.0f)) {
      float const v_44 = dot(v_38, float2(-0.17112199962139129639f, 0.98524999618530273438f));
      if ((v_44 < 0.0f)) {
        v_39 = 0.1020469963550567627f;
        v_40 = float2(-0.01480400003492832184f, -0.16260799765586853027f);
        v_41 = float2(-0.27678599953651428223f, 0.00419300002977252007f);
      } else {
        v_39 = 0.09202899783849716187f;
        v_40 = float2(-0.03853299841284751892f, -0.00164999999105930328f);
        v_41 = float2(-0.23257200419902801514f, -0.09433099627494812012f);
      }
    } else {
      float const v_45 = dot(v_38, float2(0.94792497158050537109f, 0.31849500536918640137f));
      if ((v_45 < 0.0f)) {
        v_39 = 0.08170899748802185059f;
        v_40 = float2(-0.03460099920630455017f, -0.0022150001022964716f);
        v_41 = float2(0.01218499988317489624f, 0.33803099393844604492f);
      } else {
        v_39 = 0.09113200008869171143f;
        v_40 = float2(0.07037000358104705811f, 0.03413899987936019897f);
        v_41 = float2(0.01816999912261962891f, 0.37854999303817749023f);
      }
    }
  } else {
    float const v_46 = dot(v_38, float2(-0.9067999720573425293f, 0.42156198620796203613f));
    if ((v_46 < 0.0f)) {
      float const v_47 = dot(v_38, float2(-0.39791899919509887695f, -0.91742098331451416016f));
      if ((v_47 < 0.0f)) {
        v_39 = 0.11390200257301330566f;
        v_40 = float2(0.09083600342273712158f, 0.03625100106000900269f);
        v_41 = float2(0.22678099572658538818f, 0.01876400038599967957f);
      } else {
        v_39 = 0.1617390066385269165f;
        v_40 = float2(-0.00820199958980083466f, -0.26481899619102478027f);
        v_41 = float2(0.18715600669384002686f, -0.28430399298667907715f);
      }
    } else {
      v_39 = 0.1020469963550567627f;
      v_40 = float2(-0.01480400003492832184f, -0.16260799765586853027f);
      v_41 = float2(-0.27678599953651428223f, 0.00419300002977252007f);
    }
  }
  float v_48 = 1.0f;
  float const v_49 = dot(v_40, v_38);
  float const v_50 = v_49;
  if ((v_50 > 0.0f)) {
    float const v_51 = (1.0f - v_37.x);
    float const v_52 = (v_39 * v_51);
    if ((v_52 < v_50)) {
      float const v_53 = min(v_48, (v_52 / v_50));
      v_48 = v_53;
    }
  }
  float const v_54 = dot(v_41, v_38);
  float const v_55 = v_54;
  if ((v_55 > 0.0f)) {
    float const v_56 = v_37.x;
    float const v_57 = (v_39 * v_56);
    if ((v_57 < v_55)) {
      float const v_58 = min(v_48, (v_57 / v_55));
      v_48 = v_58;
    }
  }
  v_37 = float3(v_37.x, (v_37.yz * v_48));
  float3 const v_59 = v_27(v_37);
  return v_59;
}

float4 v_60(float4 v_61, int v_62, int v_63) {
  float4 v_64 = v_61;
  if (bool(v_63)) {
    switch(v_62) {
      case 2:
      case 3:
      case 4:
      {
        float const v_65 = max(v_64.w, 0.00009999999747378752f);
        v_64 = float4((v_64.xyz / v_65), v_64.w);
        break;
      }
      case 5:
      case 6:
      case 7:
      case 9:
      case 10:
      {
        float const v_66 = max(v_64.w, 0.00009999999747378752f);
        v_64 = float4(v_64.x, (v_64.yz / v_66), v_64.w);
        break;
      }
      default:
      {
        break;
      }
    }
  }
  switch(v_62) {
    case 2:
    {
      float3 const v_67 = v_18(v_64.xyz);
      v_64 = float4(v_67, v_64.w);
      break;
    }
    case 3:
    {
      float3 const v_68 = v_27(v_64.xyz);
      v_64 = float4(v_68, v_64.w);
      break;
    }
    case 4:
    {
      float3 const v_69 = v_35(v_64.xyz);
      v_64 = float4(v_69, v_64.w);
      break;
    }
    case 5:
    {
      float3 const v_70 = v(v_64.xyz);
      float3 const v_71 = v_18(v_70);
      v_64 = float4(v_71, v_64.w);
      break;
    }
    case 6:
    {
      float3 const v_72 = v(v_64.xyz);
      float3 const v_73 = v_27(v_72);
      v_64 = float4(v_73, v_64.w);
      break;
    }
    case 7:
    {
      float3 const v_74 = v(v_64.xyz);
      float3 const v_75 = v_35(v_74);
      v_64 = float4(v_75, v_64.w);
      break;
    }
    case 9:
    {
      float3 const v_76 = v_6(v_64.xyz);
      v_64 = float4(v_76, v_64.w);
      break;
    }
    case 10:
    {
      float3 v_77 = v_64.xyz;
      float3 v_78 = 0.0f;
      v_77 = float3(v_77.x, (v_77.yz * 0.00999999977648258209f));
      if (((v_77.y + v_77.z) >= 1.0f)) {
        v_78 = float3((v_77.y / (v_77.y + v_77.z)));
      } else {
        float3 const v_79 = v_6(float3(v_77.x, 100.0f, 50.0f));
        v_78 = v_79;
        v_78 = (v_78 * ((1.0f - v_77.y) - v_77.z));
        v_78 = (v_78 + v_77.y);
      }
      v_64 = float4(v_78, v_64.w);
      break;
    }
    default:
    {
      break;
    }
  }
  return v_64;
}

float3 v_80(float3 v_81, float4 v_82, float3 v_83) {
  float3 const v_84 = (float(v_82.y) * v_81);
  float3 const v_85 = (v_84 + float(v_82.z));
  float3 const v_86 = powr(v_85, float3(float(v_82.x)));
  float3 const v_87 = (v_86 + float(v_83.y));
  float3 const v_88 = (float(v_82.w) * v_81);
  float3 const v_89 = (v_88 + float(v_83.z));
  float3 const v_90 = select(v_87, v_89, (v_81 < float3(float(v_83.x))));
  return v_90;
}

float2 v_91(int v_92, float2 v_93) {
  float2 v_94 = v_93;
  switch(v_92) {
    case 0:
    {
      float const v_95 = saturate(v_94.x);
      v_94.x = v_95;
      break;
    }
    case 1:
    {
      float const v_96 = fract(v_94.x);
      v_94.x = v_96;
      break;
    }
    case 2:
    {
      float const v_97 = (v_94.x - 1.0f);
      float const v_98 = floor((v_97 * 0.5f));
      v_94.x = ((v_97 - (2.0f * v_98)) - 1.0f);
      if (false) {
        float const v_99 = clamp(v_94.x, -1.0f, 1.0f);
        v_94.x = v_99;
      }
      float const v_100 = abs(v_94.x);
      v_94.x = v_100;
      break;
    }
    case 3:
    {
      bool v_101 = false;
      if ((v_94.x < 0.0f)) {
        v_101 = true;
      } else {
        v_101 = (v_94.x > 1.0f);
      }
      if (v_101) {
        return float2(0.0f, -1.0f);
      }
      break;
    }
    default:
    {
      break;
    }
  }
  return v_94;
}

float4 v_102(tint_array<float4, 4> v_103, float4 v_104, float2 v_105) {
  if ((v_105.y < 0.0f)) {
    return float4(0.0f);
  } else {
    if ((v_105.x <= v_104.x)) {
      return float4(v_103[0]);
    } else {
      if ((v_105.x < v_104.y)) {
        float4 const v_106 = mix(v_103[0], v_103[1], float4(((v_105.x - v_104.x) / (v_104.y - v_104.x))));
        return float4(v_106);
      } else {
        if ((v_105.x < v_104.z)) {
          float4 const v_107 = mix(v_103[1], v_103[2], float4(((v_105.x - v_104.y) / (v_104.z - v_104.y))));
          return float4(v_107);
        } else {
          if ((v_105.x < v_104.w)) {
            float4 const v_108 = mix(v_103[2], v_103[3], float4(((v_105.x - v_104.z) / (v_104.w - v_104.z))));
            return float4(v_108);
          } else {
            return float4(v_103[3]);
          }
        }
      }
    }
  }
  /* unreachable */
  return 0.0f;
}

float4 v_109(float4 v_110) {
  float const v_111 = max(v_110.w, 0.00009999999747378752f);
  return float4((v_110.xyz / v_111), v_110.w);
}

void v_112(tint_struct v_113, thread tint_struct_1* const v_114, tint_struct_2 v_115) {
  (*v_115.tint_member_26) = v_113.tint_member_1.y;
  float2 const v_116 = (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_6.xy;
  float2 const v_117 = (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_6.zw;
  float2 const v_118 = float2(v_116.x, v_116.y);
  float2 const v_119 = (float2x2(v_118, float2(v_117.x, v_117.y)) * v_113.tint_member_2);
  float2 v_120 = float2(((v_119 + (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_7).x + 0.00000999999974737875f), 1.0f);
  float2 const v_121 = v_91((*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_10, v_120);
  v_120 = v_121;
  float4 const v_122 = v_102((*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_8, (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_9, v_120);
  float4 const v_123 = v_122;
  float4 const v_124 = v_60(v_123, (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_11, (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_12);
  float4 v_125 = v_124;
  float4 const v_126 = (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_16;
  float4 const v_127 = (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_18;
  if ((v_126.w < 0.0f)) {
    float4 const v_128 = v_109(v_125);
    v_125 = v_128;
  } else {
    float const v_129 = (1.0f - v_126.w);
    float const v_130 = (v_126.w * v_127.w);
    float const v_131 = (v_126.w - v_130);
    float3 const v_132 = float3(v_125.wx, 1.0f);
    float const v_133 = dot(v_132, float3(v_129, v_131, v_130));
    v_125.w = v_133;
  }
  float3 v_134 = float3(v_125.xyz);
  float3 const v_135 = sign(v_134);
  float3 const v_136 = abs(v_134);
  float3 const v_137 = v_80(v_136, (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_15, v_126.xyz);
  v_134 = (v_135 * v_137);
  tint_array<tint_struct_5, 3> const v_138 = (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_13;
  float3x3 const v_139 = float3x3(float3x3(float3(v_138[0u].tint_member_14), float3(v_138[1u].tint_member_14), float3(v_138[2u].tint_member_14)));
  v_134 = (v_139 * v_134);
  float3 const v_140 = sign(v_134);
  float3 const v_141 = abs(v_134);
  float3 const v_142 = v_80(v_141, (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_17, v_127.xyz);
  v_134 = (v_140 * v_142);
  float const v_143 = v_127.w;
  float const v_144 = max(v_125.w, v_143);
  float3 const v_145 = (float3(v_134) * v_144);
  v_125 = float4(v_145, v_125.w);
  float const v_146 = (v_115.tint_member_28.sample(v_115.tint_member_27, (v_113.tint_member.xy * 0.125f), bias(clamp(-0.47499999403953552246f, -16.0f, 15.9899997711181640625f))).x - 0.5f);
  float3 const v_147 = (v_125.xyz + (v_146 * (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_19));
  float3 const v_148 = clamp(v_147, float3(0.0f), float3(v_125.w));
  float4 const v_149 = float4(v_148, v_125.w);
  float4 v_150 = float4(1.0f);
  float2 const v_151 = (v_113.tint_member.xy - (*v_115.tint_member_23).tint_member_24.xy);
  float4 const v_152 = (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_20;
  float2 const v_153 = (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_21;
  float4 const v_154 = (*v_115.tint_member_4).tint_member_5[(*v_115.tint_member_26)].tint_member_22;
  float const v_155 = abs(v_153.x);
  float2 const v_156 = float2(v_155);
  float2 const v_157 = (float2(v_154.xy) * ((v_152.xy + v_156) - v_151));
  float2 const v_158 = (float2(v_154.zw) * (v_151 - (v_152.zw - v_156)));
  float2 const v_159 = max(v_157, v_158);
  float2 const v_160 = max(v_159, float2(0.0f));
  float2 const v_161 = v_160;
  float const v_162 = length((v_161 * v_153.y));
  float const v_163 = saturate((v_156.x * (1.0f - v_162)));
  float const v_164 = float(v_163);
  float2 const v_165 = float2((v_151 - v_152.xy));
  float4 const v_166 = saturate(float4(v_165, float2((v_152.zw - v_151))));
  float4 v_167 = v_166;
  float4 const v_168 = mix(v_167, float4(1.0f), v_154);
  v_167 = v_168;
  float v_169 = ((((v_164 * v_167.x) * v_167.y) * v_167.z) * v_167.w);
  float v_170 = 0.0f;
  if ((v_153.x < 0.0f)) {
    v_170 = (1.0f - v_169);
  } else {
    v_170 = v_169;
  }
  v_169 = v_170;
  float4 const v_171 = float4(v_169);
  v_150 = (v_150 * v_171.w);
  (*v_114).tint_member_3 = (v_149 * v_150);
}

tint_struct_1 v_172(tint_struct v_173, tint_struct_2 v_174) {
  tint_struct_1 v_175 = {};
  v_112(v_173, (&v_175), v_174);
  return v_175;
}

fragment tint_struct_7 dawn_entry_point(float4 v_177 [[position]], tint_struct_8 v_178 [[stage_in]], const device tint_struct_3* v_179 [[buffer(2)]], const constant tint_struct_6* v_180 [[buffer(0)]], sampler v_181 [[sampler(0)]], texture2d<float, access::sample> v_182 [[texture(0)]]) {
  thread uint v_183 = 0u;
  tint_struct_2 const v_184 = tint_struct_2{.tint_member_4=v_179, .tint_member_23=v_180, .tint_member_26=(&v_183), .tint_member_27=v_181, .tint_member_28=v_182};
  tint_struct_7 v_185 = {};
  v_185.tint_member_29 = v_172(tint_struct{.tint_member=v_177, .tint_member_1=v_178.tint_member_30, .tint_member_2=v_178.tint_member_31}, v_184).tint_member_3;
  return v_185;
}
       dawn_entry_point                      