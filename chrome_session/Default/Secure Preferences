{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "/Applications/Google Chrome.app/Contents/Frameworks/Google Chrome Framework.framework/Versions/138.0.7204.157/Resources/web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "/Applications/Google Chrome.app/Contents/Frameworks/Google Chrome Framework.framework/Versions/138.0.7204.157/Resources/network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "ghbmnnjooekpmoecnnnilnnbdlolhkhi": {"account_extension_type": 0, "ack_external": true, "active_bit": false, "active_permissions": {"api": ["alarms", "storage", "unlimitedStorage", "offscreen"], "explicit_host": ["https://docs.google.com/*", "https://drive.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "allowlist": 1, "commands": {}, "content_settings": [], "creation_flags": 137, "cws-info": {"is-live": true, "is-present": true, "last-updated-time-millis": "*************", "no-privacy-practice": false, "unpublished-long-ago": false, "violation-type": 0}, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": true, "granted_permissions": {"api": ["alarms", "storage", "unlimitedStorage", "offscreen"], "explicit_host": ["https://docs.google.com/*", "https://drive.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 6, "manifest": {"author": {"email": "<EMAIL>"}, "background": {"service_worker": "service_worker_bin_prod.js"}, "content_capabilities": {"matches": ["https://docs.google.com/*", "https://drive.google.com/*", "https://drive-autopush.corp.google.com/*", "https://drive-daily-0.corp.google.com/*", "https://drive-daily-1.corp.google.com/*", "https://drive-daily-2.corp.google.com/*", "https://drive-daily-3.corp.google.com/*", "https://drive-daily-4.corp.google.com/*", "https://drive-daily-5.corp.google.com/*", "https://drive-daily-6.corp.google.com/*", "https://drive-preprod.corp.google.com/*", "https://drive-staging.corp.google.com/*"], "permissions": ["clipboardRead", "clipboardWrite", "unlimitedStorage"]}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "current_locale": "en_GB", "default_locale": "en_US", "description": "Edit, create and view your documents, spreadsheets and presentations – all without Internet access.", "externally_connectable": {"matches": ["https://docs.google.com/*", "https://drive.google.com/*", "https://drive-autopush.corp.google.com/*", "https://drive-daily-0.corp.google.com/*", "https://drive-daily-1.corp.google.com/*", "https://drive-daily-2.corp.google.com/*", "https://drive-daily-3.corp.google.com/*", "https://drive-daily-4.corp.google.com/*", "https://drive-daily-5.corp.google.com/*", "https://drive-daily-6.corp.google.com/*", "https://drive-preprod.corp.google.com/*", "https://drive-staging.corp.google.com/*"]}, "host_permissions": ["https://docs.google.com/*", "https://drive.google.com/*"], "icons": {"128": "128.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnF7RGLAxIon0/XeNZ4MLdP3DMkoORzEAKVg0sb89JpA/W2osTHr91Wqwdc9lW0mFcSpCYS9Y3e7cUMFo/M2ETASIuZncMiUzX2/0rrWtGQ3UuEj3KSe5PdaVZfisyJw/FebvHwirEWrhqcgzVUj9fL9YjE0G45d1zMKcc1umKvLqPyTznNuKBZ9GJREdGLRJCBmUgCkI8iwtwC+QZTUppmaD50/ksnEUXv+QkgGN07/KoNA5oAgo49Jf1XBoMv4QXtVZQlBYZl84zAsI82hb63a6Gu29U/4qMWDdI7+3Ne5TRvo6Zi3EI4M2NQNplJhik105qrz+eTLJJxvf4slrWwIDAQAB", "manifest_version": 3, "minimum_chrome_version": "88", "name": "Google Docs Offline", "permissions": ["alarms", "storage", "unlimitedStorage", "offscreen"], "storage": {"managed_schema": "dasherSettingSchema.json"}, "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.94.1", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["page_embed_script.js"]}]}, "path": "ghbmnnjooekpmoecnnnilnnbdlolhkhi/1.94.1_0", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.94.1"}, "serviceworkerevents": ["alarms.onAlarm", "runtime.onConnectExternal"], "was_installed_by_default": true, "was_installed_by_oem": false, "withholding_permissions": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "/Applications/Google Chrome.app/Contents/Frameworks/Google Chrome Framework.framework/Versions/138.0.7204.157/Resources/pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "/Applications/Google Chrome.app/Contents/Frameworks/Google Chrome Framework.framework/Versions/138.0.7204.157/Resources/hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nmmhkkegccagdldgiimedpiccmgmieda": {"account_extension_type": 0, "ack_external": true, "active_bit": false, "active_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "allowlist": 1, "commands": {}, "content_settings": [], "creation_flags": 137, "cws-info": {"is-live": true, "is-present": true, "last-updated-time-millis": "*************", "no-privacy-practice": false, "unpublished-long-ago": false, "violation-type": 0}, "disable_reasons": [], "events": ["app.runtime.onLaunched", "runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": true, "granted_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 10, "manifest": {"app": {"background": {"scripts": ["craw_background.js"]}}, "current_locale": "en_GB", "default_locale": "en", "description": "Chrome Web Store Payments", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon_128.png", "16": "images/icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrKfMnLqViEyokd1wk57FxJtW2XXpGXzIHBzv9vQI/01UsuP0IV5/lj0wx7zJ/xcibUgDeIxobvv9XD+zO1MdjMWuqJFcKuSS4Suqkje6u+pMrTSGOSHq1bmBVh0kpToN8YoJs/P/yrRd7FEtAXTaFTGxQL4C385MeXSjaQfiRiQIDAQAB", "manifest_version": 2, "minimum_chrome_version": "29", "name": "Chrome Web Store Payments", "oauth2": {"auto_approve": true, "client_id": "203784468217.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/sierra", "https://www.googleapis.com/auth/sierrasandbox", "https://www.googleapis.com/auth/chromewebstore", "https://www.googleapis.com/auth/chromewebstore.readonly"]}, "permissions": ["identity", "webview", "https://www.google.com/", "https://www.googleapis.com/*", "https://payments.google.com/payments/v4/js/integrator.js", "https://sandbox.google.com/payments/v4/js/integrator.js"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.0.0.6"}, "path": "nmmhkkegccagdldgiimedpiccmgmieda/1.0.0.6_0", "preferences": {}, "regular_only_preferences": {}, "running": false, "was_installed_by_default": true, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "8E27D570AEBD2A9D98132C2A7BC37AB9FE2881816154BE3968A6148E59C3408C"}, "extensions": {"ui": {"developer_mode": "F54CD42F408D47D37F828F249DF2E543C6833F3A2536351F9E07E7E36984A8B3"}}, "homepage": "F64915FEFF8251E69A5FC3B9621B93D2CBEDC9A0CCC1D11006690F9749750360", "homepage_is_newtabpage": "C7DF4DF807E36F3332C17BA7C46DEBF4D78EBCF20138241ED64B047B6DEB6405", "session": {"restore_on_startup": "C57E09063DDEA54BAD13A5CF0AC79CCBD8DD0040FC38DC9C155055099C81BCA4", "startup_urls": "93B8641A2A396A88818CF6C9B6F2916428126BEABFB4ECB3161E1879118C1794"}}, "browser": {"show_home_button": "888EC8807EE0F14144CABC5DF6E588273CC613A0FBF8BD65C076908755DC659E"}, "default_search_provider_data": {"template_url_data": "FE3D48F05B3BA25E462B6FBBAC3CFC60772A58B54EB128D61746B77067B2A232"}, "enterprise_signin": {"policy_recovery_token": "DA199E0A1BD1AC8048BCCA61B08E14C7B0C82A939B94CD60DEF53D23F1F172C9"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "BF921E21468F587AC251EFED1032B1EE5DB5DB9D6D7B419E94C3E547A92A885C", "fignfifoniblkonapihmkfakmlgkbkcf": "A4AE3A9574EECE58356013BCD39CF20502CA70CCE564E353BF2AD6E38704A7BE", "ghbmnnjooekpmoecnnnilnnbdlolhkhi": "14E7EA5F621710931C6971AB02AC296EAA34533CFE416AC94A40D5A0189DFB4B", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "F64202CEEEDDACA5CE88710F94F8725847768A1E4FF8D17D99326FE302B3E5F9", "nkeimhogjdpnpccoofpliimaahmaaome": "FC0345B85A07E598F1AE98E93A1FBFB98E09F8EDEEF62F877DE5D6A70DF4BB69", "nmmhkkegccagdldgiimedpiccmgmieda": "3194B838FD0DC834D4471221219A86492E9DA2ACD35C9DCB8B0D9D05E7066BA5"}, "ui": {"developer_mode": "ED893C3F4581C66E70672379C1FBCF811436B321B385EE3B1F6762A09F087D0A"}}, "google": {"services": {"account_id": "9F7DB6CE9246111E402EB5D871E24B28FA88D02F5FC9BB82FA6F6AA06C2F0F66", "last_signed_in_username": "A466140AC02022435AE0E7DF0FC12EADC11870F67676C73C54A399C3D3B0BAD2", "last_username": "B5FFCEFF6C093CCEED9453E0E14E0E6D51397A1E3ED369E3E4BE91BA36F09835"}}, "homepage": "B554BAF590D358FCAF1BC75AF966B3AD4F0CD5F92F57EA91C22AFE82002F4224", "homepage_is_newtabpage": "530C7EB6B12ED530A276E8E6408564C6C52592D3E1A77C5971D08D04DC96E05B", "media": {"storage_id_salt": "44ACFEDA305A5AAD6499ACE69A7FA60E1604F6887D61C75A4D632D4F37FEBEC7"}, "pinned_tabs": "7331CDF041B407940499F4D57694163EF9FD5FBE9BE172D0184D8CC57A2BC24B", "prefs": {"preference_reset_time": "06C01C408BCC9A8BB38921D805780F5F247C960C1DCFC4A51487B8E8E54C62C6"}, "safebrowsing": {"incidents_sent": "12FA6369E31018D032D2A03A2D946F7FA0705A95EA9BAF96D2AB829690DA3F51"}, "search_provider_overrides": "7829B0A7CEA0C2538A37FD348617C5797011802064AB13CB451DC660A8916871", "session": {"restore_on_startup": "E4F0A6C66C4A26478256B5A1969CF488CE2647A023ECF6EC4E3342E2A8B42787", "startup_urls": "F0FA91401DA9F1004439DC4728992AA1354F252AFA85D6FBA3C4366525744411"}}, "super_mac": "5512C33398912C124ECA1698B1138D4B259DCF5AAA783AB9BAA73D8AE32B0777"}}