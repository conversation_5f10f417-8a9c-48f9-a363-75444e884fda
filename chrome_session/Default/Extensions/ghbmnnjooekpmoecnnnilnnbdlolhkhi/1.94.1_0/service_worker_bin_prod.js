'use strict';function aa(){return function(a){return a}}function k(){return function(){}}function n(a){return function(){return this[a]}}function ba(a){return function(){return a}}var q;function ca(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var da=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function ea(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var fa=ea(this);function t(a,b){if(b)a:{var c=fa;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&da(c,a,{configurable:!0,writable:!0,value:b})}}
t("Symbol",function(a){function b(f){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(f||"")+"_"+e++,f)}function c(f,g){this.g=f;da(this,"description",{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=n("g");var d="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",e=0;return b});
t("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=fa[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&da(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ha(ca(this))}})}return a});function ha(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}
var ia=typeof Object.create=="function"?Object.create:function(a){function b(){}b.prototype=a;return new b},ja;if(typeof Object.setPrototypeOf=="function")ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var na=ja;
function u(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(na)na(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.aa=b.prototype}function v(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:ca(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}
function oa(a){if(!(a instanceof Array)){a=v(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}function pa(a){return qa(a,a)}function qa(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a}function ra(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b}t("globalThis",function(a){return a||fa});
t("Promise",function(a){function b(g){this.g=0;this.l=void 0;this.j=[];this.B=!1;var h=this.o();try{g(h.resolve,h.reject)}catch(l){h.reject(l)}}function c(){this.g=null}function d(g){return g instanceof b?g:new b(function(h){h(g)})}if(a)return a;c.prototype.j=function(g){if(this.g==null){this.g=[];var h=this;this.l(function(){h.A()})}this.g.push(g)};var e=fa.setTimeout;c.prototype.l=function(g){e(g,0)};c.prototype.A=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var l=
g[h];g[h]=null;try{l()}catch(m){this.o(m)}}}this.g=null};c.prototype.o=function(g){this.l(function(){throw g;})};b.prototype.o=function(){function g(m){return function(p){l||(l=!0,m.call(h,p))}}var h=this,l=!1;return{resolve:g(this.N),reject:g(this.A)}};b.prototype.N=function(g){if(g===this)this.A(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof b)this.P(g);else{a:switch(typeof g){case "object":var h=g!=null;break a;case "function":h=!0;break a;default:h=!1}h?this.L(g):this.F(g)}};
b.prototype.L=function(g){var h=void 0;try{h=g.then}catch(l){this.A(l);return}typeof h=="function"?this.S(h,g):this.F(g)};b.prototype.A=function(g){this.C(2,g)};b.prototype.F=function(g){this.C(1,g)};b.prototype.C=function(g,h){if(this.g!=0)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.g);this.g=g;this.l=h;this.g===2&&this.W();this.G()};b.prototype.W=function(){var g=this;e(function(){if(g.J()){var h=fa.console;typeof h!=="undefined"&&h.error(g.l)}},1)};b.prototype.J=
function(){if(this.B)return!1;var g=fa.CustomEvent,h=fa.Event,l=fa.dispatchEvent;if(typeof l==="undefined")return!0;typeof g==="function"?g=new g("unhandledrejection",{cancelable:!0}):typeof h==="function"?g=new h("unhandledrejection",{cancelable:!0}):(g=fa.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.l;return l(g)};b.prototype.G=function(){if(this.j!=null){for(var g=0;g<this.j.length;++g)f.j(this.j[g]);this.j=null}};var f=new c;
b.prototype.P=function(g){var h=this.o();g.ua(h.resolve,h.reject)};b.prototype.S=function(g,h){var l=this.o();try{g.call(h,l.resolve,l.reject)}catch(m){l.reject(m)}};b.prototype.then=function(g,h){function l(w,B){return typeof w=="function"?function(x){try{m(w(x))}catch(I){p(I)}}:B}var m,p,r=new b(function(w,B){m=w;p=B});this.ua(l(g,m),l(h,p));return r};b.prototype.catch=function(g){return this.then(void 0,g)};b.prototype.ua=function(g,h){function l(){switch(m.g){case 1:g(m.l);break;case 2:h(m.l);
break;default:throw Error("Unexpected state: "+m.g);}}var m=this;this.j==null?f.j(l):this.j.push(l);this.B=!0};b.resolve=d;b.reject=function(g){return new b(function(h,l){l(g)})};b.race=function(g){return new b(function(h,l){for(var m=v(g),p=m.next();!p.done;p=m.next())d(p.value).ua(h,l)})};b.all=function(g){var h=v(g),l=h.next();return l.done?d([]):new b(function(m,p){function r(x){return function(I){w[x]=I;B--;B==0&&m(w)}}var w=[],B=0;do w.push(void 0),B++,d(l.value).ua(r(w.length-1),p),l=h.next();
while(!l.done)})};return b});function sa(a,b){return Object.prototype.hasOwnProperty.call(a,b)}t("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});t("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});
t("WeakMap",function(a){function b(l){this.g=(h+=Math.random()+1).toString();if(l){l=v(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}}function c(){}function d(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function e(l){if(!sa(l,g)){var m=new c;da(l,g,{value:m})}}function f(l){var m=Object[l];m&&(Object[l]=function(p){if(p instanceof c)return p;Object.isExtensible(p)&&e(p);return m(p)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),
p=new a([[l,2],[m,3]]);if(p.get(l)!=2||p.get(m)!=3)return!1;p.delete(l);p.set(m,4);return!p.has(l)&&p.get(m)==4}catch(r){return!1}}())return a;var g="$jscomp_hidden_"+Math.random();f("freeze");f("preventExtensions");f("seal");var h=0;b.prototype.set=function(l,m){if(!d(l))throw Error("Invalid WeakMap key");e(l);if(!sa(l,g))throw Error("WeakMap key fail: "+l);l[g][this.g]=m;return this};b.prototype.get=function(l){return d(l)&&sa(l,g)?l[g][this.g]:void 0};b.prototype.has=function(l){return d(l)&&sa(l,
g)&&sa(l[g],this.g)};b.prototype.delete=function(l){return d(l)&&sa(l,g)&&sa(l[g],this.g)?delete l[g][this.g]:!1};return b});
t("Map",function(a){function b(){var h={};return h.previous=h.next=h.head=h}function c(h,l){var m=h[1];return ha(function(){if(m){for(;m.head!=h[1];)m=m.previous;for(;m.next!=m.head;)return m=m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})}function d(h,l){var m=l&&typeof l;m=="object"||m=="function"?f.has(l)?m=f.get(l):(m=""+ ++g,f.set(l,m)):m="p_"+l;var p=h[0][m];if(p&&sa(h[0],m))for(h=0;h<p.length;h++){var r=p[h];if(l!==l&&r.key!==r.key||l===r.key)return{id:m,list:p,index:h,entry:r}}return{id:m,
list:p,index:-1,entry:void 0}}function e(h){this[0]={};this[1]=b();this.size=0;if(h){h=v(h);for(var l;!(l=h.next()).done;)l=l.value,this.set(l[0],l[1])}}if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),l=new a(v([[h,"s"]]));if(l.get(h)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),p=m.next();if(p.done||p.value[0]!=h||p.value[1]!="s")return!1;p=m.next();return p.done||p.value[0].x!=
4||p.value[1]!="t"||!m.next().done?!1:!0}catch(r){return!1}}())return a;var f=new WeakMap;e.prototype.set=function(h,l){h=h===0?0:h;var m=d(this,h);m.list||(m.list=this[0][m.id]=[]);m.entry?m.entry.value=l:(m.entry={next:this[1],previous:this[1].previous,head:this[1],key:h,value:l},m.list.push(m.entry),this[1].previous.next=m.entry,this[1].previous=m.entry,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],
h.entry.previous.next=h.entry.next,h.entry.next.previous=h.entry.previous,h.entry.head=null,this.size--,!0):!1};e.prototype.clear=function(){this[0]={};this[1]=this[1].previous=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).entry};e.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=function(){return c(this,
function(h){return h.value})};e.prototype.forEach=function(h,l){for(var m=this.entries(),p;!(p=m.next()).done;)p=p.value,h.call(l,p[1],p[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var g=0;return e});
t("Set",function(a){function b(c){this.g=new Map;if(c){c=v(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size}if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(v([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||
f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;b.prototype.add=function(c){c=c===0?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;b.prototype[Symbol.iterator]=
b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b});t("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)sa(b,d)&&c.push(b[d]);return c}});t("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
t("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
t("String.prototype.includes",function(a){return a?a:function(b,c){if(this==null)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return(this+"").indexOf(b,c||0)!==-1}});
t("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:aa();var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});t("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)sa(b,d)&&c.push([d,b[d]]);return c}});
t("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});t("Number.MAX_SAFE_INTEGER",ba(9007199254740991));t("Number.MIN_SAFE_INTEGER",ba(-9007199254740991));t("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});t("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});
function ta(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}t("Array.prototype.entries",function(a){return a?a:function(){return ta(this,function(b,c){return[b,c]})}});t("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});
t("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});t("Array.prototype.keys",function(a){return a?a:function(){return ta(this,aa())}});t("Array.prototype.values",function(a){return a?a:function(){return ta(this,function(b,c){return c})}});t("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});
t("String.prototype.matchAll",function(a){return a?a:function(b){if(b instanceof RegExp&&!b.global)throw new TypeError("RegExp passed into String.prototype.matchAll() must have global tag.");var c=new RegExp(b,b instanceof RegExp?void 0:"g"),d=this,e=!1,f={next:function(){if(e)return{value:void 0,done:!0};var g=c.exec(d);if(!g)return e=!0,{value:void 0,done:!0};g[0]===""&&(c.lastIndex+=1);return{value:g,done:!1}}};f[Symbol.iterator]=function(){return f};return f}});/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var ua=ua||{},y=this||self;function va(a){a=a.split(".");for(var b=y,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b}function wa(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"}function xa(a){var b=wa(a);return b=="array"||b=="object"&&typeof a.length=="number"}function ya(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}var za="closure_uid_"+(Math.random()*1E9>>>0),Aa=0;function Ba(a,b,c){return a.call.apply(a.bind,arguments)}
function Ca(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function Da(a,b,c){Da=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?Ba:Ca;return Da.apply(null,arguments)}
function Ea(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function Fa(a){(0,eval)(a)}function Ga(a){return a}function z(a,b){function c(){}c.prototype=b.prototype;a.aa=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.sc=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};function Ha(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,Ha);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.g=!0}z(Ha,Error);Ha.prototype.name="CustomError";function Ia(a){y.setTimeout(function(){throw a;},0)};var Ja=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};var Ka,La=va("CLOSURE_FLAGS"),Ma=La&&La[610401301];Ka=Ma!=null?Ma:!1;function Na(){var a=y.navigator;return a&&(a=a.userAgent)?a:""}var Oa,Pa=y.navigator;Oa=Pa?Pa.userAgentData||null:null;function Qa(a){if(!Ka||!Oa)return!1;for(var b=0;b<Oa.brands.length;b++){var c=Oa.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function A(a){return Na().indexOf(a)!=-1};function Ra(){return Ka?!!Oa&&Oa.brands.length>0:!1}function Sa(){return A("Firefox")||A("FxiOS")}function Ta(){return Ra()?Qa("Chromium"):(A("Chrome")||A("CriOS"))&&!(Ra()?0:A("Edge"))||A("Silk")};function Ua(){return Ka?!!Oa&&!!Oa.platform:!1}function Xa(){return A("iPhone")&&!A("iPod")&&!A("iPad")}function Ya(){Xa()||A("iPad")||A("iPod")};function Za(a,b){return Array.prototype.indexOf.call(a,b,void 0)}function $a(a,b){return Array.prototype.some.call(a,b,void 0)}function ab(a,b){b=Za(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c}function bb(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(xa(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var g=0;g<f;g++)a[e+g]=d[g]}else a.push(d)}};A("Mobile");Ua()||A("Macintosh");Ua()||A("Windows");(Ua()?Oa.platform==="Linux":A("Linux"))||Ua()||A("CrOS");Ua()||A("Android");Xa();A("iPad");A("iPod");Ya();Na().toLowerCase().indexOf("kaios");Sa();Xa()||A("iPod");A("iPad");!A("Android")||Ta()||Sa()||(Ra()?0:A("Opera"))||A("Silk");Ta();!A("Safari")||Ta()||(Ra()?0:A("Coast"))||(Ra()?0:A("Opera"))||(Ra()?0:A("Edge"))||(Ra()?Qa("Microsoft Edge"):A("Edg/"))||(Ra()?Qa("Opera"):A("OPR"))||Sa()||A("Silk")||A("Android")||Ya();var cb={},db=null;var eb=typeof Uint8Array!=="undefined",fb=typeof btoa==="function",gb={},hb=typeof structuredClone!="undefined";function ib(a,b){if(b!==gb)throw Error("illegal external caller");this.g=a;if(a!=null&&a.length===0)throw Error("ByteString should be constructed with non-empty values");}function jb(){return kb||(kb=new ib(null,gb))}var kb;function lb(a,b,c){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382[b]=c}function mb(a){return a.__closure__error__context__984382||{}};var nb=void 0;function ob(a,b){if(a!=null){var c;var d=(c=nb)!=null?c:nb={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),lb(a,"severity","incident"),Ia(a))}};var pb=typeof Symbol==="function"&&typeof Symbol()==="symbol";function qb(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b}var rb=qb("jas",void 0,!0),sb=qb(void 0,"0di"),tb=qb(void 0,"1oa"),ub=qb(void 0,Symbol()),vb=qb(void 0,"0ubs"),xb=qb(void 0,"0actk"),yb=qb("m_m","vc",!0);Math.max.apply(Math,oa(Object.values({Wb:1,Ub:2,Rb:4,ec:8,oc:16,ac:32,Gb:64,Pb:128,Nb:256,lc:512,Ob:1024,Qb:2048,bc:4096})));var zb={sb:{value:0,configurable:!0,writable:!0,enumerable:!1}},Ab=Object.defineProperties,C=pb?rb:"sb",Bb,Cb=[];Db(Cb,7);Bb=Object.freeze(Cb);function Eb(a,b){pb||C in a||Ab(a,zb);a[C]|=b}function Db(a,b){pb||C in a||Ab(a,zb);a[C]=b}function Fb(a){Eb(a,34);return a};function Gb(){return typeof BigInt==="function"};var Hb={};function Ib(a,b){return b===void 0?a.g!==Jb&&!!(2&(a.D[C]|0)):!!(2&b)&&a.g!==Jb}var Jb={},Kb=Object.freeze({});function Lb(a){a.uc=!0;return a};var Mb=Lb(function(a){return typeof a==="number"}),Nb=Lb(function(a){return typeof a==="string"}),Ob=Lb(function(a){return typeof a==="boolean"}),Pb=Lb(function(a){return typeof a==="bigint"});var Qb=typeof y.BigInt==="function"&&typeof y.BigInt(0)==="bigint";function Rb(a){var b=a;if(Nb(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(Mb(b)&&!Number.isSafeInteger(b))throw Error(String(b));return Qb?BigInt(a):a=Ob(a)?a?"1":"0":Nb(a)?a.trim()||"0":String(a)}
var Sb=Lb(function(a){return Qb?Pb(a):Nb(a)&&/^(?:-?[1-9]\d*|0)$/.test(a)}),Zb=Lb(function(a){return Qb?a>=Tb&&a<=Vb:a[0]==="-"?Wb(a,Xb):Wb(a,Yb)}),Xb=Number.MIN_SAFE_INTEGER.toString(),Tb=Qb?BigInt(Number.MIN_SAFE_INTEGER):void 0,Yb=Number.MAX_SAFE_INTEGER.toString(),Vb=Qb?BigInt(Number.MAX_SAFE_INTEGER):void 0;function Wb(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};var D=0,$b=0;function ac(a){var b=a>>>0;D=b;$b=(a-b)/4294967296>>>0}function bc(a){if(a<0){ac(0-a);var b=v(cc(D,$b));a=b.next().value;b=b.next().value;D=a>>>0;$b=b>>>0}else ac(a)}function dc(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else Gb()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+ec(c)+ec(a));return c}
function ec(a){a=String(a);return"0000000".slice(a.length)+a}function fc(){var a=D,b=$b;b&2147483648?Gb()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=v(cc(a,b)),a=b.next().value,b=b.next().value,a="-"+dc(a,b)):a=dc(a,b);return a}function cc(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};var hc=typeof BigInt==="function"?BigInt.asIntN:void 0,ic=Number.isSafeInteger,jc=Number.isFinite,kc=Math.trunc;function lc(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function mc(a){return a.displayName||a.name||"unknown type name"}function nc(a){if(typeof a!=="boolean")throw Error("Expected boolean but got "+wa(a)+": "+a);return a}var oc=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;
function pc(a){switch(typeof a){case "bigint":return!0;case "number":return jc(a);case "string":return oc.test(a);default:return!1}}function qc(a){return a==null?a:jc(a)?a|0:void 0}function rc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return jc(a)?a|0:void 0}function sc(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}
function tc(a){a.indexOf(".");if(sc(a))return a;if(a.length<16)bc(Number(a));else if(Gb())a=BigInt(a),D=Number(a&BigInt(4294967295))>>>0,$b=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");$b=D=0;for(var c=a.length,d=0+b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),$b*=1E6,D=D*1E6+d,D>=4294967296&&($b+=Math.trunc(D/4294967296),$b>>>=0,D>>>=0);b&&(b=v(cc(D,$b)),a=b.next().value,b=b.next().value,D=a,$b=b)}return fc()}
function uc(a){pc(a);a=kc(a);if(!ic(a)){bc(a);var b=D,c=$b;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);var d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:dc(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function vc(a){pc(a);a=kc(a);if(ic(a))a=String(a);else{var b=String(a);sc(b)?a=b:(bc(a),a=fc())}return a}function wc(a){return a==null||typeof a==="string"?a:void 0}
function xc(a,b,c,d){if(a!=null&&a[yb]===Hb)return a;if(!Array.isArray(a))return c?d&2?((a=b[sb])||(a=new b,Fb(a.D),a=b[sb]=a),b=a):b=new b:b=void 0,b;c=a[C]|0;d=c|d&32|d&2;d!==c&&Db(a,d);return new b(a)};function yc(a){return a};function zc(){}function Ac(a,b){for(var c in a)!isNaN(c)&&b(a,+c,a[c])}function Bc(a){var b=new zc;Ac(a,function(c,d,e){b[d]=Array.prototype.slice.call(e)});b.g=a.g;return b}function Cc(a,b){b<100||ob(vb,1)};function Dc(a,b,c,d){var e=d!==void 0;d=!!d;var f=Ga(ub),g;!e&&pb&&f&&(g=a[f])&&Ac(g,Cc);f=[];var h=a.length;g=4294967295;var l=!1,m=!!(b&64),p=m?b&128?0:-1:void 0;if(!(b&1)){var r=h&&a[h-1];r!=null&&typeof r==="object"&&r.constructor===Object?(h--,g=h):r=void 0;if(m&&!(b&128)&&!e){l=!0;var w;g=((w=Ec)!=null?w:yc)(g-p,p,a,r)+p}}b=void 0;for(w=0;w<h;w++){var B=a[w];if(B!=null&&(B=c(B,d))!=null)if(m&&w>=g){var x=w-p,I=void 0;((I=b)!=null?I:b={})[x]=B}else f[w]=B}if(r)for(var R in r)h=r[R],h!=null&&
(h=c(h,d))!=null&&(w=+R,B=void 0,m&&!Number.isNaN(w)&&(B=w+p)<g?f[B]=h:(w=void 0,((w=b)!=null?w:b={})[R]=h));b&&(l?f.push(b):f[g]=b);e&&Ga(ub)&&(a=(c=Ga(ub))?a[c]:void 0)&&a instanceof zc&&(f[ub]=Bc(a));return f}
function Fc(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return Zb(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[C]|0;return a.length===0&&b&1?void 0:Dc(a,b,Fc)}if(a!=null&&a[yb]===Hb)return Gc(a);if(a instanceof ib){b=a.g;if(b==null)a="";else if(typeof b==="string")a=b;else{if(fb){for(var c="",d=0,e=b.length-10240;d<e;)c+=String.fromCharCode.apply(null,b.subarray(d,d+=10240));c+=String.fromCharCode.apply(null,d?b.subarray(d):
b);b=btoa(c)}else{c===void 0&&(c=0);if(!db){db={};d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split("");e=["+/=","+/","-_=","-_.","-_"];for(var f=0;f<5;f++){var g=d.concat(e[f].split(""));cb[f]=g;for(var h=0;h<g.length;h++){var l=g[h];db[l]===void 0&&(db[l]=h)}}}c=cb[c];d=Array(Math.floor(b.length/3));e=c[64]||"";for(f=g=0;g<b.length-2;g+=3){var m=b[g],p=b[g+1];l=b[g+2];h=c[m>>2];m=c[(m&3)<<4|p>>4];p=c[(p&15)<<2|l>>6];l=c[l&63];d[f++]=""+h+m+p+l}h=0;l=e;switch(b.length-g){case 2:h=
b[g+1],l=c[(h&15)<<2]||e;case 1:b=b[g],d[f]=""+c[b>>2]+c[(b&3)<<4|h>>4]+l+e}b=d.join("")}a=a.g=b}return a}return}return a}var Hc=hb?structuredClone:function(a){return Dc(a,0,Fc)},Ec;function Gc(a){a=a.D;return Dc(a,a[C]|0,Fc)};function E(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("narr");e=a[C]|0;2048&e&&!(2&e)&&Ic();if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||Db(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1,h=c[g];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("pvtlmt");for(var l in h)f=+l,f<g&&(c[f+b]=h[l],
delete h[l]);e=e&-8380417|(g&1023)<<13;break a}}if(b){l=Math.max(b,f-(e&128?0:-1));if(l>1024)throw Error("spvt");e=e&-8380417|(l&1023)<<13}}}e|=64;d===0&&(e|=2048);Db(a,e);return a}function Ic(){ob(xb,5)};function Jc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[C]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=Kc(a,c,!1,b&&!(c&16)):(Eb(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[yb]===Hb)return b=a.D,c=b[C]|0,Ib(a,c)?a:Lc(a,b,c)?Mc(a,b):Kc(b,c);if(a instanceof ib)return a}function Mc(a,b,c){a=new a.constructor(b);c&&(a.g=Jb);a.l=Jb;return a}function Kc(a,b,c,d){d!=null||(d=!!(34&b));a=Dc(a,b,Jc,d);d=32;c&&(d|=2);b=b&8380609|d;Db(a,b);return a}
function Nc(a){var b=a.D,c=b[C]|0;return Ib(a,c)?Lc(a,b,c)?Mc(a,b,!0):new a.constructor(Kc(b,c,!1)):a}function Pc(a){if(a.g!==Jb)return!1;var b=a.D;b=Kc(b,b[C]|0);Eb(b,2048);a.D=b;a.g=void 0;a.l=void 0;return!0}function Qc(a,b){b===void 0&&(b=a[C]|0);b&32&&!(b&4096)&&Db(a,b|4096)}function Lc(a,b,c){return c&2?!0:c&32&&!(c&4096)?(Db(b,c|2),a.g=Jb,!0):!1};var Rc=Rb(0),Sc={};function F(a,b,c,d,e){Object.isExtensible(a);b=Tc(a.D,b,c,e);if(b!==null||d&&a.l!==Jb)return b}function Tc(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object){c=g[b];var h=!0}else if(e===f)c=g;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}}
function Uc(a,b,c){if(!Pc(a)&&Ib(a,a.D[C]|0))throw Error();var d=a.D;Vc(d,d[C]|0,b,c);return a}function Vc(a,b,c,d){var e=c+-1,f=a.length-1;if(f>=0&&e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;if(d!==void 0){var h;f=((h=b)!=null?h:b=a[C]|0)>>13&1023||536870912;c>=f?d!=null&&(e={},a[f+-1]=(e[c]=d,e)):a[e]=d}return b}
function Wc(a,b,c,d,e,f,g,h){var l=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?Xc(b)||(b|=!a.length||g&&!(4096&b)||32&d&&!(4096&b||16&b)?2:256,b!==l&&Db(a,b),Object.freeze(a)):(f===2&&Xc(b)&&(a=Array.prototype.slice.call(a),l=0,b=Yc(b,d),d=Vc(c,d,e,a)),Xc(b)||(h||(b|=16),b!==l&&Db(a,b)));2&b||!(4096&b||16&b)||Qc(c,d);return a}function Zc(a,b){a=Tc(a,b);return Array.isArray(a)?a:Bb}function $c(a,b){2&b&&(a|=2);return a|1}function Xc(a){return!!(2&a)&&!!(4&a)||!!(256&a)}
function ad(a){return a==null?a:typeof a==="string"?a?new ib(a,gb):jb():a.constructor===ib?a:eb&&a!=null&&a instanceof Uint8Array?a.length?new ib(new Uint8Array(a),gb):jb():void 0}function bd(a,b,c){return cd(a,b)===c?c:-1}
function cd(a,b){a=a.D;if(pb){var c;var d=(c=a[tb])!=null?c:a[tb]=new Map}else tb in a?d=a[tb]:(c=new Map,Object.defineProperty(a,tb,{value:c}),d=c);c=d;d=void 0;var e=c.get(b);if(e==null){for(var f=e=0;f<b.length;f++){var g=b[f];Tc(a,g)!=null&&(e!==0&&(d=Vc(a,d,e)),e=g)}c.set(b,e)}return e}function dd(a,b,c,d){var e=!1;d=Tc(a,d,void 0,function(f){var g=xc(f,c,!1,b);e=g!==f&&g!=null;return g});if(d!=null)return e&&!Ib(d)&&Qc(a,b),d}
function ed(a,b,c){a=a.D;(c=dd(a,a[C]|0,b,c))||(c=b[sb])||(c=new b,Fb(c.D),c=b[sb]=c);return c}function G(a,b,c){var d=a.D,e=d[C]|0;b=dd(d,e,b,c);if(b==null)return b;e=d[C]|0;if(!Ib(a,e)){var f=Nc(b);f!==b&&(Pc(a)&&(d=a.D,e=d[C]|0),b=f,e=Vc(d,e,c,b),Qc(d,e))}return b}
function fd(a,b,c){var d=void 0===Kb?2:4,e=a.D,f=e;e=e[C]|0;var g=Ib(a,e),h=g?1:d;d=h===3;var l=!g;(h===2||l)&&Pc(a)&&(f=a.D,e=f[C]|0);a=Zc(f,c);var m=a===Bb?7:a[C]|0,p=$c(m,e);if(g=!(4&p)){var r=a,w=e,B=!!(2&p);B&&(w|=2);for(var x=!B,I=!0,R=0,Va=0;R<r.length;R++){var Wa=xc(r[R],b,!1,w);if(Wa instanceof b){if(!B){var wb=Ib(Wa);x&&(x=!wb);I&&(I=wb)}r[Va++]=Wa}}Va<R&&(r.length=Va);p|=4;p=I?p&-4097:p|4096;p=x?p|8:p&-9}p!==m&&(Db(a,p),2&p&&Object.freeze(a));if(l&&!(8&p||!a.length&&(h===1||(h!==4?0:2&
p||!(16&p)&&32&e)))){Xc(p)&&(a=Array.prototype.slice.call(a),p=Yc(p,e),e=Vc(f,e,c,a));b=a;l=p;for(m=0;m<b.length;m++)r=b[m],p=Nc(r),r!==p&&(b[m]=p);l|=8;p=l=b.length?l|4096:l&-4097;Db(a,p)}return a=Wc(a,p,f,e,c,h,g,d)}function gd(a,b,c,d){if(d!=null){if(!(d instanceof b))throw Error("Expected instanceof "+mc(b)+" but got "+(d&&mc(d.constructor)));}else d=void 0;Uc(a,c,d);d&&!Ib(d)&&Qc(a.D);return a}function Yc(a,b){return a=(2&b?a|2:a&-3)&-273}
function hd(a,b){var c=c===void 0?!1:c;a=F(a,b);a=a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0;return a!=null?a:c}function id(a,b,c){c=c===void 0?0:c;var d;return(d=rc(F(a,b)))!=null?d:c}function jd(a,b){var c=c===void 0?Rc:c;a=F(a,b);b=typeof a;a!=null&&(b==="bigint"?a=Rb(hc(64,a)):pc(a)?b==="string"?(b=kc(Number(a)),ic(b)?a=Rb(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=Gb()?Rb(hc(64,BigInt(a))):Rb(tc(a)))):a=ic(a)?Rb(uc(a)):Rb(vc(a)):a=void 0);return a!=null?a:c}
function kd(a,b){var c=c===void 0?"":c;var d;return(d=wc(F(a,b)))!=null?d:c}function ld(a,b){var c=c===void 0?0:c;var d;return(d=qc(F(a,b)))!=null?d:c}function md(a,b){return wc(F(a,b,void 0,Sc))}function nd(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return Uc(a,b,c)}function od(a,b){if(b!=null){if(!jc(b))throw a=Error("enum"),lb(a,"severity","warning"),a;b|=0}return Uc(a,1,b)};function H(a,b,c){this.D=E(a,b,c)}H.prototype.toJSON=function(){return Gc(this)};function pd(a,b){if(b==null||b=="")return new a;b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");Eb(b,32);return new a(b)}H.prototype.clone=function(){var a=this.D,b=a[C]|0;return Lc(this,a,b)?Mc(this,a,!0):new this.constructor(Kc(a,b,!1))};H.prototype[yb]=Hb;H.prototype.toString=function(){return this.D.toString()};
function qd(a,b){if(b==null)b=a.constructor,(a=b[sb])||(a=new b,Fb(a.D),a=b[sb]=a),b=a;else{a=a.constructor;if(!Array.isArray(b))throw Error();if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error();b=new a(Fb(b))}return b};function rd(a){return function(b){return pd(a,b)}};function sd(a){this.D=E(a)}u(sd,H);sd.prototype.getTypeName=function(){return kd(this,1).split("/").pop()};var td=function(a){return Lb(function(b){return b instanceof a&&!Ib(b)})}(sd);function ud(){this.key="45681191";this.defaultValue=!1;this.flagNameForDebugging=void 0}ud.prototype.ctor=function(a){return typeof a==="boolean"?a:this.defaultValue};function vd(){var a=wd("[]"),b=xd;this.key="45696263";this.defaultValue=a;this.g=b;this.flagNameForDebugging=void 0}
vd.prototype.ctor=function(a){if(typeof a==="string"&&a)return pd(this.g,a);if(!td(a))return this.defaultValue.clone();var b;try{var c,d=this.g,e=(c=a.getTypeName())!=null?c:"";if(kd(a,1).split("/").pop()!=e)var f=null;else{var g=typeof d==="function"?d:d.constructor,h=a.D,l=h[C]|0,m=Tc(h,2);Pc(a)&&(h=a.D,l=h[C]|0);a=h;if(m!=null&&!(Array.isArray(m)||m!=null&&m[yb]===Hb))throw Error("saw an invalid value of type '"+wa(m)+"' in the Any.value field");var p=xc(m,g,!0,l);if(!(p instanceof g))throw Error("incorrect type in any value: got "+
p.constructor.displayName+", expected "+g.displayName);(g=!!(2&l))||(p=Nc(p));m!==p&&(Vc(a,l,2,p),g||Qc(a));f=p}}catch(r){f=null}return(b=f)!=null?b:this.defaultValue.clone()};function yd(a){this.D=E(a)}u(yd,H);var zd=[1];function Ad(a){this.D=E(a)}u(Ad,H);var Bd=[2,3,4,5,6,8];function Cd(a){this.D=E(a)}u(Cd,H);Cd.prototype.j=function(){var a=F(this,3,void 0,void 0,ad);return a==null?jb():a};function Dd(a){this.D=E(a)}u(Dd,H);var Ed=rd(Dd);function xd(a){this.D=E(a)}u(xd,H);var wd=rd(xd);function Fd(a,b){this.K=a|0;this.I=b|0}function Gd(a){return a.I*4294967296+(a.K>>>0)}q=Fd.prototype;q.isSafeInteger=function(){var a=this.I>>21;return a==0||a==-1&&!(this.K==0&&this.I==-2097152)};
q.toString=function(a){a=a||10;if(a<2||36<a)throw Error("radix out of range: "+a);if(this.isSafeInteger()){var b=Gd(this);return a==10?""+b:b.toString(a)}b=14-(a>>2);var c=Math.pow(a,b),d=J(c,c/4294967296);c=this.div(d);var e=Math,f=e.abs;d=c.multiply(d);d=this.add(Hd(d));e=f.call(e,Gd(d));f=a==10?""+e:e.toString(a);f.length<b&&(f="0000000000000".slice(f.length-b)+f);e=Gd(c);return(a==10?e:e.toString(a))+f};function Id(a){return a.K==0&&a.I==0}q.T=function(){return this.K^this.I};
q.equals=function(a){return this.K==a.K&&this.I==a.I};q.compare=function(a){return this.I==a.I?this.K==a.K?0:this.K>>>0>a.K>>>0?1:-1:this.I>a.I?1:-1};function Hd(a){var b=~a.K+1|0;return J(b,~a.I+!b|0)}q.add=function(a){var b=this.I>>>16,c=this.I&65535,d=this.K>>>16,e=a.I>>>16,f=a.I&65535,g=a.K>>>16;a=(this.K&65535)+(a.K&65535);g=(a>>>16)+(d+g);d=g>>>16;d+=c+f;return J((g&65535)<<16|a&65535,((d>>>16)+(b+e)&65535)<<16|d&65535)};
q.multiply=function(a){if(Id(this))return this;if(Id(a))return a;var b=this.I>>>16,c=this.I&65535,d=this.K>>>16,e=this.K&65535,f=a.I>>>16,g=a.I&65535,h=a.K>>>16;a=a.K&65535;var l=e*a;var m=(l>>>16)+d*a;var p=m>>>16;m=(m&65535)+e*h;p+=m>>>16;p+=c*a;var r=p>>>16;p=(p&65535)+d*h;r+=p>>>16;p=(p&65535)+e*g;r=r+(p>>>16)+(b*a+c*h+d*g+e*f)&65535;return J((m&65535)<<16|l&65535,r<<16|p&65535)};
q.div=function(a){if(Id(a))throw Error("division by zero");if(this.I<0){if(this.equals(Jd)){if(a.equals(Kd)||a.equals(Ld))return Jd;if(a.equals(Jd))return Kd;var b=this.I;b=J(this.K>>>1|b<<31,b>>1);b=b.div(a).shiftLeft(1);if(b.equals(Md))return a.I<0?Kd:Ld;var c=a.multiply(b);c=this.add(Hd(c));return b.add(c.div(a))}return a.I<0?Hd(this).div(Hd(a)):Hd(Hd(this).div(a))}if(Id(this))return Md;if(a.I<0)return a.equals(Jd)?Md:Hd(this.div(Hd(a)));b=Md;for(c=this;c.compare(a)>=0;){var d=Math.max(1,Math.floor(Gd(c)/
Gd(a))),e=Math.ceil(Math.log(d)/Math.LN2);e=e<=48?1:Math.pow(2,e-48);for(var f=Nd(d),g=f.multiply(a);g.I<0||g.compare(c)>0;)d-=e,f=Nd(d),g=f.multiply(a);Id(f)&&(f=Kd);b=b.add(f);c=c.add(Hd(g))}return b};q.and=function(a){return J(this.K&a.K,this.I&a.I)};q.or=function(a){return J(this.K|a.K,this.I|a.I)};q.xor=function(a){return J(this.K^a.K,this.I^a.I)};q.shiftLeft=function(a){a&=63;if(a==0)return this;var b=this.K;return a<32?J(b<<a,this.I<<a|b>>>32-a):J(0,b<<a-32)};
function Nd(a){return a>0?a>=0x7fffffffffffffff?Od:new Fd(a,a/4294967296):a<0?a<=-0x7fffffffffffffff?Jd:Hd(new Fd(-a,-a/4294967296)):Md}function J(a,b){return new Fd(a,b)}var Md=J(0,0),Kd=J(1,0),Ld=J(-1,-1),Od=J(4294967295,2147483647),Jd=J(0,2147483648);function Pd(a,b){b=b===void 0?window:b;b=b===void 0?window:b;return(b=b.WIZ_global_data)&&a in b?b[a]:null};var Qd;
function Rd(){var a=Pd("TSDtV",window);a.indexOf("%.@.");a=Ed("["+a.substring(4));if(a=fd(a,Cd,1)[0])for(var b=v(fd(a,Ad,2)),c=b.next();!c.done;c=b.next()){c=c.value;var d=c.D;if(dd(d,d[C]|0,sd,bd(c,Bd,6))!==void 0)throw Error();}if(a)for(b={},c=v(fd(a,Ad,2)),d=c.next();!d.done;d=c.next()){var e=d.value;d=jd(e,1).toString();switch(cd(e,Bd)){case 3:b[d]=hd(e,bd(e,Bd,3));break;case 2:var f=jd(e,bd(e,Bd,2));Sb(f);Zb(f);f=Zb(f)?Number(f):String(f);b[d]=f;break;case 4:f=void 0;var g=e;var h=bd(e,Bd,4);
e=void 0;e=e===void 0?0:e;g=(f=F(g,h,void 0,void 0,lc))!=null?f:e;b[d]=g;break;case 5:b[d]=kd(e,bd(e,Bd,5));break;case 6:b[d]=G(e,sd,bd(e,Bd,6));break;case 8:f=ed(e,yd,bd(e,Bd,8));switch(cd(f,zd)){case 1:b[d]=kd(f,bd(f,zd,1));break;default:throw Error("case "+cd(f,zd));}break;default:throw Error("case "+cd(e,Bd));}}else b={};this.g=b;this.l=a?a.j():null}function Sd(a){var b=Qd=Qd||new Rd;return a.key in b.g?a.ctor(b.g[a.key]):a.defaultValue}Rd.prototype.j=n("l");function Td(a){this.D=E(a)}u(Td,H);var Ud=new vd;var Vd=new ud;function Wd(a){this.D=E(a)}u(Wd,H);var Xd=function(a){return function(){var b;(b=a[sb])||(b=new a,Fb(b.D),b=a[sb]=b);return b}}(Wd);Object.create(null);function K(){}K.prototype.equals=function(a){return L(this,a)};K.prototype.T=function(){return Yd(this)};K.prototype.toString=function(){return M(Zd($d(ae(this))))+"@"+M((this.T()>>>0).toString(16))};function be(a){return a!=null}K.prototype.v=["java.lang.Object",0];function ce(){}u(ce,K);function de(a,b){a.j=b;ee(a)}function N(a,b){a.g=b;fe(b,a)}function ee(a){ge(a.g)&&(Error.captureStackTrace?Error.captureStackTrace(O(a.g,ge,he)):O(a.g,ge,he).stack=Error().stack)}ce.prototype.toString=function(){var a=Zd($d(ae(this))),b=this.j;return b==null?a:M(a)+": "+M(b)};function ie(a){if(a!=null){var b=a.cb;if(b!=null)return b}a instanceof TypeError?b=je():(b=new ke,ee(b),N(b,Error(b)));b.j=a==null?"null":a.toString();N(b,a);return b}
function le(a){return a instanceof ce}ce.prototype.v=["java.lang.Throwable",0];function me(){}u(me,ce);me.prototype.v=["java.lang.Exception",0];function P(){}u(P,me);P.prototype.v=["java.lang.RuntimeException",0];function ne(){}u(ne,P);function oe(){var a=new ne;ee(a);N(a,Error(a));return a}function pe(a){var b=new ne;de(b,a);N(b,Error(b));return b}ne.prototype.v=["java.lang.IndexOutOfBoundsException",0];function L(a,b){return Object.is(a,b)||a==null&&b==null};var qe;function re(){re=k();for(var a=se(),b=0;b<256;b=b+1|0)te(a,b,ue(b-128|0));qe=a};function ve(){}u(ve,P);ve.prototype.v=["java.lang.ArithmeticException",0];function we(){}u(we,P);we.prototype.v=["java.lang.ArrayStoreException",0];function xe(){}u(xe,P);xe.prototype.v=["java.lang.ClassCastException",0];function ye(){}u(ye,P);function ze(a){var b=new ye;de(b,a);N(b,Error(b));return b}ye.prototype.v=["java.lang.IllegalArgumentException",0];function Ae(){}u(Ae,P);function Be(){var a=new Ae;ee(a);N(a,Error(a));return a}Ae.prototype.v=["java.lang.IllegalStateException",0];function ke(){}u(ke,P);ke.prototype.v=["java.lang.JsException",0];function Ce(){}u(Ce,ke);function je(){var a=new Ce;ee(a);N(a,new TypeError(a));return a}Ce.prototype.v=["java.lang.NullPointerException",0];function De(){}u(De,ne);De.prototype.v=["java.lang.StringIndexOutOfBoundsException",0];function Ee(){}u(Ee,P);function Fe(){var a=new Ee;ee(a);N(a,Error(a));return a}Ee.prototype.v=["java.util.ConcurrentModificationException",0];function Ge(){}u(Ge,P);function He(){var a=new Ge;ee(a);N(a,Error(a));return a}Ge.prototype.v=["java.util.NoSuchElementException",0];function Ie(){}var Je;u(Ie,K);Ie.prototype.v=["java.lang.Number",0];function Ke(){}u(Ke,Ie);Ke.prototype.v=["java.lang.Double",0];function Le(a){return Nd(a)}function Me(a){if(!isFinite(a))throw a=new ve,ee(a),N(a,Error(a)),a.g;return a|0}function Ne(a){return Math.max(Math.min(a,2147483647),-2147483648)|0};function O(a,b,c){a==null||b(a)||(b=M(Zd(Oe(a)))+" cannot be cast to "+M(Zd($d(c))),Pe(b));return a};function ae(a){return a.constructor}function Qe(a,b,c){if(Object.prototype.hasOwnProperty.call(a.prototype,b))return a.prototype[b];c=c();return a.prototype[b]=c};function Re(){}u(Re,K);Re.prototype.v=["java.lang.Boolean",0];function Se(a){switch(Q(typeof a)){case "string":for(var b=0,c=0;c<a.length;c=c+1|0){b=(b<<5)-b;var d=a,e=c;Te(e,d.length);b=b+d.charCodeAt(e)|0}return b;case "number":return a=Q(a),Ne(a);case "boolean":return Q(a)?1231:1237;default:return a==null?0:Yd(a)}}var Ue=0;function Yd(a){return a.Na||(Object.defineProperties(a,{Na:{value:Ue=Ue+1|0,enumerable:!1}}),a.Na)};function Ve(a,b){return a.equals?a.equals(b):Object.is(a,b)}function We(a){return a.T?a.T():Se(a)}function Oe(a){switch(Q(typeof a)){case "number":return $d(Ke);case "boolean":return $d(Re);case "string":return $d(Xe);case "function":return $d(Ye)}if(a instanceof K)a=$d(ae(a));else if(Array.isArray(a))a=(a=a.V)?$d(a.ha,a.ga):$d(K,1);else if(a!=null)a=$d(Ze);else throw new TypeError("null.getClass()");return a};function Ye(){}Ye.prototype.v=["<native function>",1];function Ze(){}u(Ze,K);Ze.prototype.v=["<native object>",0];function $e(){}u($e,P);function S(){var a=new $e;ee(a);N(a,Error(a));return a}$e.prototype.v=["java.lang.UnsupportedOperationException",0];function T(a,b){return L(a,b)||a!=null&&Ve(a,b)}function af(a){return a!=null?We(a):0}function bf(a){if(a==null)throw je().g;};function cf(){this.g=0}u(cf,Ie);function df(a){a>-129&&a<128?(re(),a=qe[a+128|0]):a=ue(a);return a}function ue(a){var b=new cf;b.g=a;return b}cf.prototype.equals=function(a){return ef(a)&&O(a,ef,cf).g==this.g};cf.prototype.T=n("g");cf.prototype.toString=function(){return""+this.g};function ef(a){return a instanceof cf}cf.prototype.v=["java.lang.Integer",0];function ff(){}u(ff,K);q=ff.prototype;q.add=function(){throw S().g;};q.Aa=function(a){Q(a);var b=!1;for(a=a.H();a.g();){var c=a.j();b=!!(+b|+this.add(c))}};q.clear=function(){for(var a=this.H();a.g();)a.j(),a.l()};q.contains=function(a){return gf(this,a,!1)};q.Da=function(a){Q(a);for(a=a.H();a.g();){var b=a.j();if(!this.contains(b))return!1}return!0};q.remove=function(a){return gf(this,a,!0)};q.removeAll=function(a){Q(a);for(var b=!1,c=this.H();c.g();){var d=c.j();a.contains(d)&&(c.l(),b=!0)}return b};
q.fa=function(){return hf(this,Array(this.size()))};q.ma=function(a){return hf(this,a)};q.toString=function(){for(var a=jf("[","]"),b=this.H();b.g();){var c=b.j();kf(a,L(c,this)?"(this Collection)":M(c))}return a.toString()};function gf(a,b,c){for(a=a.H();a.g();){var d=a.j();if(T(b,d))return c&&a.l(),!0}return!1}q.Ua=function(){return this.fa()};q.v=["java.util.AbstractCollection",0];function lf(){}function mf(){var a=new nf;a.j=1;a.g=1;return U(a,of)}function pf(a){return qf(a.slice(0,a.length))}function U(){return qf(ra.apply(0,arguments))}function rf(a){return a!=null&&!!a.oa}lf.prototype.oa=!0;lf.prototype.v=["java.util.List",1];function sf(){}u(sf,ff);q=sf.prototype;q.add=function(a){this.sa(this.size(),a);return!0};q.sa=function(){throw S().g;};q.Ca=function(a,b){Q(b);for(b=b.H();b.g();){var c=b.j(),d=void 0;this.sa((d=a,a=a+1|0,d),c)}};q.clear=function(){this.Sa(0,this.size())};q.equals=function(a){if(L(a,this))return!0;if(!rf(a))return!1;a=O(a,rf,lf);if(this.size()!=a.size())return!1;a=a.H();for(var b=this.H();b.g();){var c=b.j(),d=a.j();if(!T(c,d))return!1}return!0};
q.T=function(){tf();for(var a=1,b=this.H();b.g();){var c=b.j();a=Math.imul(31,a)+af(c)|0}return a};q.indexOf=function(a){for(var b=0,c=this.size();b<c;b=b+1|0)if(T(a,this.Y(b)))return b;return-1};q.H=function(){var a=new uf;a.F=this;a.o=0;a.A=-1;return a};q.lastIndexOf=function(a){for(var b=this.size()-1|0;b>-1;b=b-1|0)if(T(a,this.Y(b)))return b;return-1};q.Ia=function(a){var b=new vf;b.F=this;b.o=0;b.A=-1;wf(a,this.size());b.o=a;return b};q.Ka=function(){throw S().g;};
q.Sa=function(a,b){for(var c=this.Ia(a);a<b;a=a+1|0)c.j(),c.l()};q.oa=!0;q.v=["java.util.AbstractList",0];function xf(){}u(xf,sf);q=xf.prototype;q.Aa=function(a){this.Ca(this.g.length,a)};q.contains=function(a){return this.indexOf(a)!=-1};q.Y=function(a){yf(a,this.g.length);return this.g[a]};q.indexOf=function(a){a:{for(var b=0,c=this.g.length;b<c;b=b+1|0)if(T(a,this.g[b])){a=b;break a}a=-1}return a};q.H=function(){var a=new zf;a.F=this;a.o=0;a.A=-1;return a};q.lastIndexOf=function(a){a:{for(var b=this.g.length-1|0;b>=0;b=b-1|0)if(T(a,this.g[b])){a=b;break a}a=-1}return a};
q.Ka=function(a){this.Y(a);this.g.splice(a,1)};q.remove=function(a){a=this.indexOf(a);if(a==-1)return!1;this.g.splice(a,1);return!0};q.size=function(){return this.g.length};q.ma=function(a){var b=this.g.length;a.length<b&&(a=Af(Array(b),a));for(var c=0;c<b;c=c+1|0)te(a,c,this.g[c]);a.length>b&&te(a,b,null);return a};q.oa=!0;q.v=["java.util.ArrayListBase",0];function Bf(){}u(Bf,xf);function Cf(){var a=new Bf;a.g=[];return a}q=Bf.prototype;q.add=function(a){this.g.push(a);return!0};q.sa=function(a,b){wf(a,this.g.length);this.g.splice(a,0,b)};q.Ca=function(a,b){wf(a,this.g.length);b=b.fa();var c=b.length;if(c!=0){var d=this.g.length+c|0;this.g.length=d;var e=a+c|0;Df(this.g,a,this.g,e,d-e|0);Df(b,0,this.g,a,c)}};q.fa=function(){var a=this.g,b=a.slice();b.V=a.V;b==null||Ef(b,K,be,1)||(a=$d(K,1),a=Zd(Oe(b))+" cannot be cast to "+Zd(a),Pe(a));return b};
q.Sa=function(a,b){var c=this.g.length;if(a<0||b>c)throw pe("fromIndex: "+a+", toIndex: "+b+", size: "+c).g;if(a>b)throw ze("fromIndex: "+a+" > toIndex: "+b).g;this.g.splice(a,b-a|0)};q.v=["java.util.ArrayList",0];function zf(){this.A=this.o=0}u(zf,K);zf.prototype.g=function(){return this.o<this.F.g.length};zf.prototype.j=function(){Ff(this.g());var a;this.A=(a=this.o,this.o=this.o+1|0,a);return this.F.g[this.A]};zf.prototype.l=function(){Gf(this.A!=-1);var a=this.F,b=this.o=this.A;a.g.splice(b,1);this.A=-1};zf.prototype.v=["java.util.ArrayListBase$1",0];function Hf(){}u(Hf,sf);q=Hf.prototype;q.contains=ba(!1);q.Y=function(a){yf(a,0);return null};q.H=function(){return If()};q.size=ba(0);q.v=["java.util.Collections$EmptyList",0];function Jf(){}var Kf;u(Jf,K);Jf.prototype.g=ba(!1);Jf.prototype.j=function(){throw He().g;};Jf.prototype.l=function(){throw Be().g;};function Lf(){Lf=k();Kf=new Jf}Jf.prototype.v=["java.util.Collections$EmptyListIterator",0];function Mf(){}u(Mf,K);Mf.prototype.g=function(){return this.o.g()};Mf.prototype.j=function(){return O(this.o.j(),V,W).O()};Mf.prototype.l=function(){this.o.l()};Mf.prototype.v=["java.util.AbstractMap$1$1",0];function W(){}function V(a){return a!=null&&!!a.ya}W.prototype.ya=!0;W.prototype.v=["java.util.Map$Entry",1];function Nf(){}function Of(){var a=ra.apply(0,arguments);tf();if(a.length==0)a=Pf(Qf);else{var b=new Rf;b.g=Sf();for(var c=0;c<a.length;c=c+1|0)if(!b.add(Q(a[c])))throw ze("Duplicate element").g;a=Pf(b)}return a}function Tf(a){return a!=null&&!!a.pa}Nf.prototype.pa=!0;Nf.prototype.v=["java.util.Set",1];function Uf(){}u(Uf,ff);q=Uf.prototype;q.equals=function(a){if(L(a,this))return!0;if(!Tf(a))return!1;a=O(a,Tf,Nf);return a.size()!=this.size()?!1:this.Da(a)};q.T=function(){return Vf(this)};q.removeAll=function(a){Q(a);var b=this.size();if(b<a.size())for(var c=this.H();c.g();){var d=c.j();a.contains(d)&&c.l()}else for(a=a.H();a.g();)c=a.j(),this.remove(c);return b!=this.size()};q.pa=!0;q.v=["java.util.AbstractSet",0];function Wf(){}u(Wf,Uf);q=Wf.prototype;q.clear=function(){this.g.clear()};q.contains=function(a){return this.g.ia(a)};q.H=function(){var a=this.g.Z().H(),b=new Mf;b.o=a;return b};q.remove=function(a){return this.g.ia(a)?(this.g.remove(a),!0):!1};q.size=function(){return this.g.size()};q.v=["java.util.AbstractMap$1",0];function Xf(){}u(Xf,K);Xf.prototype.g=function(){return this.o.g()};Xf.prototype.j=function(){return O(this.o.j(),V,W).R()};Xf.prototype.l=function(){this.o.l()};Xf.prototype.v=["java.util.AbstractMap$2$1",0];function Yf(){}u(Yf,ff);q=Yf.prototype;q.clear=function(){this.g.clear()};q.contains=function(a){return this.g.Pa(a)};q.H=function(){var a=this.g.Z().H(),b=new Xf;b.o=a;return b};q.size=function(){return this.g.size()};q.v=["java.util.AbstractMap$2",0];function Zf(){}u(Zf,K);q=Zf.prototype;q.O=n("j");q.R=n("g");q.Oa=function(a){var b=this.g;this.g=a;return b};q.equals=function(a){if(!V(a))return!1;a=O(a,V,W);return T(this.j,a.O())&&T(this.g,a.R())};q.T=function(){return af(this.j)^af(this.g)};q.toString=function(){return M(this.j)+"="+M(this.g)};q.ya=!0;q.v=["java.util.AbstractMap$AbstractEntry",0];function $f(){}u($f,Zf);function ag(a,b){var c=new $f;c.j=a;c.g=b;return c}$f.prototype.v=["java.util.AbstractMap$SimpleEntry",0];function bg(){}function cg(a){return a!=null&&!!a.Ma}bg.prototype.Ma=!0;bg.prototype.v=["java.util.Map",1];function dg(){}u(dg,K);q=dg.prototype;q.clear=function(){this.Z().clear()};q.ia=function(a){return eg(this,a,!1)!=null};q.Pa=function(a){for(var b=this.Z().H();b.g();){var c=O(b.j(),V,W).R();if(T(a,c))return!0}return!1};function fg(a,b){var c=b.O();b=b.R();var d=a.get(c);return!T(b,d)||d==null&&!a.ia(c)?!1:!0}q.equals=function(a){if(L(a,this))return!0;if(!cg(a))return!1;a=O(a,cg,bg);if(this.size()!=a.size())return!1;for(a=a.Z().H();a.g();){var b=O(a.j(),V,W);if(!fg(this,b))return!1}return!0};
q.get=function(a){return gg(eg(this,a,!1))};q.T=function(){return Vf(this.Z())};q.Ra=function(){var a=new Wf;a.g=this;return a};q.X=function(){throw S().g;};q.remove=function(a){return gg(eg(this,a,!0))};q.size=function(){return this.Z().size()};q.toString=function(){for(var a=jf("{","}"),b=this.Z().H();b.g();){var c=O(b.j(),V,W);c=M(hg(this,c.O()))+"="+M(hg(this,c.R()));kf(a,c)}return a.toString()};function hg(a,b){return L(b,a)?"(this Map)":M(b)}q.values=function(){var a=new Yf;a.g=this;return a};
function gg(a){return a==null?null:a.R()}function eg(a,b,c){for(a=a.Z().H();a.g();){var d=O(a.j(),V,W);if(T(b,d.O()))return c&&(d=ag(d.O(),d.R()),a.l()),d}return null}q.Ma=!0;q.v=["java.util.AbstractMap",0];function ig(){}u(ig,K);ig.prototype.toString=n("g");ig.prototype.v=["java.lang.AbstractStringBuilder",0];function jg(){}u(jg,ig);jg.prototype.v=["java.lang.StringBuilder",0];function kg(){}u(kg,K);function jf(a,b){var c=new kg;c.o=", ".toString();c.l=a.toString();c.j=b.toString();c.A=M(c.l)+M(c.j);return c}function kf(a,b){if(a.g==null){var c=new jg,d=O(Q(a.l),lg,Xe);c.g=d;a.g=c}else c=a.g,c.g=M(c.g)+M(a.o);a=a.g;a.g=M(a.g)+M(b)}kg.prototype.toString=function(){return this.g==null?this.A:this.j.length==0?this.g.toString():M(this.g.toString())+M(this.j)};kg.prototype.v=["java.util.StringJoiner",0];function mg(){}u(mg,Uf);mg.prototype.contains=ba(!1);mg.prototype.H=function(){return If()};mg.prototype.size=ba(0);mg.prototype.v=["java.util.Collections$EmptySet",0];function ng(){}u(ng,K);q=ng.prototype;q.add=function(){throw S().g;};q.Aa=function(){throw S().g;};q.clear=function(){throw S().g;};q.contains=function(a){return this.g.contains(a)};q.Da=function(a){return this.g.Da(a)};q.H=function(){var a=this.g.H(),b=new og;b.o=a;return b};q.remove=function(){throw S().g;};q.removeAll=function(){throw S().g;};q.size=function(){return this.g.size()};q.fa=function(){return this.g.fa()};q.ma=function(a){return this.g.ma(a)};q.toString=function(){return this.g.toString()};
q.Ua=function(){return this.fa()};q.v=["java.util.Collections$UnmodifiableCollection",0];function og(){}u(og,K);og.prototype.g=function(){return this.o.g()};og.prototype.j=function(){return this.o.j()};og.prototype.l=function(){throw S().g;};og.prototype.v=["java.util.Collections$UnmodifiableCollectionIterator",0];function pg(){}u(pg,ng);q=pg.prototype;q.sa=function(){throw S().g;};q.Ca=function(){throw S().g;};q.equals=function(a){return Ve(this.j,a)};q.Y=function(a){return this.j.Y(a)};q.T=function(){return We(this.j)};q.indexOf=function(a){return this.j.indexOf(a)};q.lastIndexOf=function(a){return this.j.lastIndexOf(a)};q.Ia=function(a){a=this.j.Ia(a);var b=new qg;b.o=a;return b};q.Ka=function(){throw S().g;};q.oa=!0;q.v=["java.util.Collections$UnmodifiableList",0];function qg(){}u(qg,og);qg.prototype.v=["java.util.Collections$UnmodifiableListIterator",0];function rg(){}u(rg,ng);rg.prototype.equals=function(a){return Ve(this.g,a)};rg.prototype.T=function(){return We(this.g)};rg.prototype.pa=!0;rg.prototype.v=["java.util.Collections$UnmodifiableSet",0];function sg(){}u(sg,pg);sg.prototype.v=["java.util.Collections$UnmodifiableRandomAccessList",0];var tg,Qf;function If(){tf();return Lf(),Kf}function qf(a){tf();for(var b=0;b<a.length;b=b+1|0)Q(a[b]);a.length==0?b=tg:(b=new ug,Q(a),b.g=a);a=new sg;a.g=b;a.j=b;return a}function Pf(a){tf();var b=new rg;b.g=a;return b}function Vf(a){tf();var b=0;for(a=a.H();a.g();){var c=a.j();b=b+af(c)|0}return b}function tf(){tf=k();tg=new Hf;Qf=new mg};function vg(){}u(vg,Uf);q=vg.prototype;q.clear=function(){this.g.clear()};q.contains=function(a){return V(a)?fg(this.g,O(a,V,W)):!1};q.H=function(){var a=new wg;a.o=this.g;a.G=a.o.l.H();a.A=a.G;a.F=xg(a);a.B=a.o.j;return a};q.remove=function(a){return this.contains(a)?(a=O(a,V,W).O(),this.g.remove(a),!0):!1};q.size=function(){return this.g.size()};q.v=["java.util.AbstractHashMap$EntrySet",0];function wg(){this.F=!1;this.B=0}u(wg,K);wg.prototype.g=n("F");function xg(a){if(a.A.g())return!0;if(!L(a.A,a.G))return!1;a.A=a.o.g.H();return a.A.g()}wg.prototype.l=function(){Gf(this.C!=null);if(this.o.j!=this.B)throw Fe().g;this.C.l();this.C=null;this.F=xg(this);this.B=this.o.j};wg.prototype.j=function(){if(this.o.j!=this.B)throw Fe().g;Ff(this.g());this.C=this.A;var a=O(this.A.j(),V,W);this.F=xg(this);return a};wg.prototype.v=["java.util.AbstractHashMap$EntrySetIterator",0];function yg(){this.j=0}u(yg,dg);q=yg.prototype;q.clear=function(){zg(this)};function zg(a){var b=new Ag;b.j=new Map;b.l=a;a.g=b;b=new Bg;b.g=new Map;b.o=a;a.l=b;Cg(a)}function Cg(a){a.j=a.j+1|0}q.ia=function(a){return lg(a)?this.l.g.has(a):Dg(a,Eg(this.g,a==null?0:We(a)))!=null};q.Pa=function(a){return Fg(a,this.l)||Fg(a,this.g)};function Fg(a,b){for(b=b.H();b.g();){var c=O(b.j(),V,W),d=a;c=c.R();if(T(d,c))return!0}return!1}q.Z=function(){var a=new vg;a.g=this;return a};
q.get=function(a){return lg(a)?this.l.g.get(a):gg(Dg(a,Eg(this.g,a==null?0:We(a))))};q.X=function(a,b){if(lg(a))a=Gg(this.l,a,b);else a:{var c=this.g,d=a==null?0:We(a),e=Eg(c,d);if(e.length==0)c.j.set(d,e);else if(d=Dg(a,e),d!=null){a=d.Oa(b);break a}te(e,e.length,ag(a,b));c.g=c.g+1|0;Cg(c.l);a=null}return a};q.remove=function(a){return lg(a)?Hg(this.l,a):Ig(this.g,a)};q.size=function(){return this.g.g+this.l.l|0};q.v=["java.util.AbstractHashMap",0];function Jg(){this.o=0}u(Jg,K);Jg.prototype.g=function(){if(this.o<this.A.length)return!0;var a=this.C.next();return a.done?!1:(this.A=a.value[1],this.o=0,!0)};Jg.prototype.l=function(){Ig(this.B,this.F.O());this.o!=0&&(this.o=this.o-1|0)};Jg.prototype.j=function(){var a;return this.F=this.A[a=this.o,this.o=this.o+1|0,a]};Jg.prototype.v=["java.util.InternalHashCodeMap$1",0];function Ag(){this.g=0}u(Ag,K);function Ig(a,b){for(var c=b==null?0:We(b),d=Eg(a,c),e=0;e<d.length;e=e+1|0){var f=d[e];if(T(b,f.O()))return d.length==1?(d.length=0,a.j.delete(c)):d.splice(e,1),a.g=a.g-1|0,Cg(a.l),f.R()}return null}function Dg(a,b){for(var c=0;c<b.length;c++){var d=b[c];if(T(a,d.O()))return d}return null}Ag.prototype.H=function(){var a=new Jg;a.B=this;a.C=a.B.j.entries();a.o=0;a.A=[];a.F=null;return a};function Eg(a,b){a=a.j.get(b);return a==null?[]:a}
Ag.prototype.v=["java.util.InternalHashCodeMap",0];function Kg(){}u(Kg,K);Kg.prototype.g=function(){return!this.A.done};Kg.prototype.l=function(){Hg(this.o,this.B.value[0])};Kg.prototype.j=function(){this.B=this.A;this.A=this.F.next();var a=new Lg,b=this.B,c=this.o.j;a.j=this.o;a.g=b;a.l=c;return a};Kg.prototype.v=["java.util.InternalStringMap$1",0];function Mg(){}u(Mg,K);q=Mg.prototype;q.equals=function(a){if(!V(a))return!1;a=O(a,V,W);return T(this.O(),a.O())&&T(this.R(),a.R())};q.T=function(){return af(this.O())^af(this.R())};q.toString=function(){return M(this.O())+"="+M(this.R())};q.ya=!0;q.v=["java.util.AbstractMapEntry",0];function Lg(){this.l=0}u(Lg,Mg);Lg.prototype.O=function(){return this.g.value[0]};Lg.prototype.R=function(){return this.j.j!=this.l?this.j.g.get(this.g.value[0]):this.g.value[1]};Lg.prototype.Oa=function(a){return Gg(this.j,this.g.value[0],a)};Lg.prototype.v=["java.util.InternalStringMap$2",0];function Bg(){this.j=this.l=0}u(Bg,K);function Gg(a,b,c){var d=a.g.get(b);a.g.set(b,c===void 0?null:c);d===void 0?(a.l=a.l+1|0,Cg(a.o)):a.j=a.j+1|0;return d}function Hg(a,b){var c=a.g.get(b);c===void 0?a.j=a.j+1|0:(a.g.delete(b),a.l=a.l-1|0,Cg(a.o));return c}Bg.prototype.H=function(){var a=new Kg;a.o=this;a.F=a.o.g.entries();a.A=a.F.next();return a};Bg.prototype.v=["java.util.InternalStringMap",0];function Ng(){this.j=0}u(Ng,yg);function Sf(){var a=new Ng;zg(a);return a}Ng.prototype.v=["java.util.HashMap",0];function Rf(){}u(Rf,Uf);q=Rf.prototype;q.add=function(a){return this.g.X(a,this)==null};q.clear=function(){this.g.clear()};q.contains=function(a){return this.g.ia(a)};q.H=function(){return this.g.Ra().H()};q.remove=function(a){return this.g.remove(a)!=null};q.size=function(){return this.g.size()};q.pa=!0;q.v=["java.util.HashSet",0];function Og(){}var Pg;u(Og,K);function Qg(a){var b=new Og;b.g=a;return b}Og.prototype.equals=function(a){if(L(a,this))return!0;if(!Rg(a))return!1;a=O(a,Rg,Og);return T(this.g,a.g)};Og.prototype.T=function(){return af(this.g)};Og.prototype.toString=function(){return this.g!=null?"Optional.of("+M(M(this.g))+")":"Optional.empty()"};function Sg(){Sg=k();Pg=Qg(null)}function Rg(a){return a instanceof Og}Og.prototype.v=["java.util.Optional",0];function hf(a,b){var c=a.size();b.length<c&&(b=Af(Array(c),b));var d=b;a=a.H();for(var e=0;e<c;e=e+1|0)te(d,e,a.j());b.length>c&&te(b,c,null);return b};function uf(){this.A=this.o=0}u(uf,K);uf.prototype.g=function(){return this.o<this.F.size()};uf.prototype.j=function(){Ff(this.g());var a;return this.F.Y(this.A=(a=this.o,this.o=this.o+1|0,a))};uf.prototype.l=function(){Gf(this.A!=-1);this.F.Ka(this.A);this.o=this.A;this.A=-1};uf.prototype.v=["java.util.AbstractList$IteratorImpl",0];function vf(){uf.call(this)}u(vf,uf);vf.prototype.v=["java.util.AbstractList$ListIteratorImpl",0];function ug(){}u(ug,sf);q=ug.prototype;q.contains=function(a){return this.indexOf(a)!=-1};q.Y=function(a){var b=this.size();yf(a,b);return this.g[a]};q.size=function(){return this.g.length};q.fa=function(){return this.ma(Array(this.g.length))};q.H=function(){var a=new Tg;a.A=this.g;return a};q.ma=function(a){var b=this.g.length;a.length<b&&(a=Af(Array(b),a));for(var c=0;c<b;c=c+1|0)te(a,c,this.g[c]);a.length>b&&te(a,b,null);return a};q.v=["java.util.Arrays$ArrayList",0];function Tg(){this.o=0}u(Tg,K);Tg.prototype.g=function(){return this.o<this.A.length};Tg.prototype.j=function(){Ff(this.g());var a;return this.A[a=this.o,this.o=this.o+1|0,a]};Tg.prototype.l=function(){throw S().g;};Tg.prototype.v=["javaemul.internal.ArrayIterator",0];function Ug(a,b){if(L(a,b))return!0;if(a==null||b==null||a.length!=b.length)return!1;for(var c=0;c<a.length;c=c+1|0)if(!T(a[c],b[c]))return!1;return!0};function Vg(){}u(Vg,ye);Vg.prototype.v=["java.lang.NumberFormatException",0];function Df(a,b,c,d,e){var f=a.length,g=c.length;if(b<0||d<0||e<0||(b+e|0)>f||(d+e|0)>g)throw oe().g;if(e!=0)if(L(a,c)&&b<d)for(b=b+e|0,e=d+e|0;e>d;)te(c,e=e-1|0,a[b=b-1|0]);else for(e=d+e|0;d<e;)g=f=void 0,te(c,(f=d,d=d+1|0,f),a[g=b,b=b+1|0,g])};function Af(a,b){a.V=b.V;return a};function Pe(a){var b=new xe;de(b,a);N(b,Error(b));throw b.g;}function Ff(a){if(!a)throw He().g;}function Gf(a){if(!a)throw Be().g;}function Q(a){Wg(a);return a}function Wg(a){if(a==null)throw je().g;return a}function yf(a,b){if(a<0||a>=b)throw pe("Index: "+a+", Size: "+b).g;}function Te(a,b){if(a<0||a>=b){var c=new De;de(c,"Index: "+a+", Size: "+b);N(c,Error(c));throw c.g;}}function wf(a,b){if(a<0||a>b)throw pe("Index: "+a+", Size: "+b).g;};function se(){var a=[256];return Xg(a,Yg(cf,ef,a.length))}function Xg(a,b){var c=a[0];if(c==null)return null;var d=new globalThis.Array(c);b&&(d.V=b);if(a.length>1){a=a.slice(1);b=b&&Yg(b.ha,b.Ha,b.ga-1);for(var e=0;e<c;e++)d[e]=Xg(a,b)}else if(b&&(a=b.ha.Db,a!==void 0))for(b=0;b<c;b++)d[b]=a;return d}function Zg(a){a.V=Yg(Xe,lg,1);return a}
function te(a,b,c){var d;if(!(d=c==null))a:{if(d=a.V)if(d.ga>1){if(!Ef(c,d.ha,d.Ha,d.ga-1)){d=!1;break a}}else if(c!=null&&!d.Ha(c)){d=!1;break a}d=!0}if(!d)throw a=new we,ee(a),N(a,Error(a)),a.g;a[b]=c}function Ef(a,b,c,d){if(a==null||!Array.isArray(a))return!1;a=a.V||{ha:K,ga:1};var e=a.ga;return e==d?(d=a.ha,d===b?!0:b&&b.prototype.Va||d&&d.prototype.Va?!1:c(d.prototype)):e>d?K==b:!1}function Yg(a,b,c){return{ha:a,Ha:b,ga:c}};function Xe(){}u(Xe,K);function M(a){return a==null?"null":a.toString()}function $g(a,b){Te(b,a.length+1|0);return a.substr(b)}function lg(a){return"string"===typeof a}Xe.prototype.v=["java.lang.String",0];function ah(){}var bh,ch;u(ah,K);function dh(){dh=k();ch=new eh;bh=new fh}ah.prototype.v=["java.util.Locale",0];function eh(){}u(eh,ah);eh.prototype.toString=ba("");eh.prototype.v=["java.util.Locale$1",0];function fh(){}u(fh,ah);fh.prototype.toString=ba("unknown");fh.prototype.v=["java.util.Locale$4",0];function gh(a,b){this.g=a;this.j=b}u(gh,K);function $d(a,b){var c=b||0;return Qe(a,"$$class/"+c,function(){return new gh(a,c)})}function Zd(a){return a.j!=0?M(hh("[",a.j))+M(a.g.prototype.v[1]==3?a.g.prototype.v[2]:"L"+M(a.g.prototype.v[0])+";"):a.g.prototype.v[0]}function ih(a){return M(a.g.prototype.v[0])+M(hh("[]",a.j))}function jh(a,b){return $g(a,a.lastIndexOf(b)+1|0)}
gh.prototype.toString=function(){return String(this.j==0&&this.g.prototype.v[1]==1?"interface ":this.j==0&&this.g.prototype.v[1]==3?"":"class ")+M(Zd(this))};function hh(a,b){for(var c="",d=0;d<b;d=d+1|0)c=M(c)+M(a);return c}gh.prototype.v=["java.lang.Class",0];function he(){}function ge(a){return a instanceof Error}he.prototype.v=["Error",0];function fe(a,b){if(a instanceof Object)try{a.cb=b,Object.defineProperties(a,{cause:{get:function(){return b.l&&b.l.g}}})}catch(c){}};function mh(a,b){this.l=b;this.j=a;ee(this);N(this,Error(this))}u(mh,P);mh.prototype.getMessage=n("j");fa.Object.defineProperties(mh.prototype,{error:{configurable:!0,enumerable:!0,get:function(){var a=Error(),b=this.g;a.fileName=b.fileName;a.lineNumber=b.lineNumber;a.columnNumber=b.columnNumber;a.message=b.message;a.name=b.name;a.stack=b.stack;a.toSource=b.toSource;a.cause=b.cause;for(var c in b)c.indexOf("__java$")!=0&&(a[c]=b[c]);return a}}});
mh.prototype.v=["com.google.apps.docs.xplat.base.XplatException",0];function nh(){}function oh(a){return a instanceof Error}nh.prototype.v=["Error",0];function ph(){var a=a==null?function(c){return Ne(Math.floor(Math.random()*c))}:a;var b=(a(2147483647)>>>0).toString(16);b=M(qh("0",Math.max(0,8-b.length|0)))+M(b);a=(a(2147483647)>>>0).toString(16);return M(a)+M(b)};function rh(){}function sh(a){return a instanceof Array}rh.prototype.v=["Array",0];function th(){}function uh(a){return a instanceof Object}th.prototype.v=["Object",0];var qh=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};function vh(){}function wh(a){return new RegExp(a,"")}function xh(a){return a instanceof RegExp}vh.prototype.v=["RegExp",0];function yh(){}u(yh,K);function zh(a,b){var c=new yh;if(b==null)throw je().g;c.j=O(b,lg,Xe);b="g";a.multiline&&(b=M(b)+"m");a.ignoreCase&&(b=M(b)+"i");c.l=new RegExp(a.source,b);return c}function Ah(a){a.g=a.l.exec(a.j);return a.g!=null}yh.prototype.v=["com.google.apps.xplat.regex.RegExpMatcher",0];function Bh(){}function Ch(a){return a instanceof Object}Bh.prototype.v=["Object",0];var Dh={Xb:"build-label",Eb:"buildLabel",Fb:"clientLog",Jb:"docId",Zb:"mobile-app-version",kc:"severity",qc:"severity-unprefixed",Sb:"isArrayPrototypeIntact",Tb:"isEditorElementAttached",Mb:"documentCharacterSet",Vb:"isModuleLoadFailure",hc:"reportName",Yb:"locale",Hb:"createdOnServer",dc:"numUnsavedCommands",Ib:"cspViolationContext",fc:"relatedToBrowserExtension",rc:"workerError",Kb:"docosPostLimitExceeded",Lb:"docosPostLimitType",jc:"saveTakingTooLongOnClient",mc:"truncatedCommentNotificationsCount",
nc:"truncatedCommentNotificationsFromPayload",cc:"nonfatalReason"};function Eh(){this.g=!1}u(Eh,K);q=Eh.prototype;q.dispose=function(){this.g||(this.g=!0,this.za(),jh(jh(ih($d(ae(this))),"."),"$"))};q.Ga=n("g");q.za=function(){if(this.o!=null){for(var a=this.o,b=0;b<a.length;b++)a[b].dispose();this.o.length=0}};q.toString=function(){return K.prototype.toString.call(this)||""};q.v=["com.google.apps.xplat.disposable.Disposable",0];/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Fh=pa([""]),Gh=qa(["\x00"],["\\0"]),Hh=qa(["\n"],["\\n"]),Ih=qa(["\x00"],["\\u0000"]);function Jh(a){return a.toString().indexOf("`")===-1}Jh(function(a){return a(Fh)})||Jh(function(a){return a(Gh)})||Jh(function(a){return a(Hh)})||Jh(function(a){return a(Ih)});function Kh(a){var b=y.onerror;y.onerror=function(c,d,e,f,g){b&&b(c,d,e,f,g);a({message:c,fileName:d,line:e,lineNumber:e,tc:f,error:g});return!0}}
function Lh(a){var b=va("window.location.href");a==null&&(a='Unknown Error of type "null/undefined"');if(typeof a==="string")return{message:a,name:"Unknown error",lineNumber:"Not available",fileName:b,stack:"Not available"};var c=!1;try{var d=a.lineNumber||a.line||"Not available"}catch(f){d="Not available",c=!0}try{var e=a.fileName||a.filename||a.sourceURL||y.$googDebugFname||b}catch(f){e="Not available",c=!0}b=Mh(a);return!c&&a.lineNumber&&a.fileName&&a.stack&&a.message&&a.name?{message:a.message,
name:a.name,lineNumber:a.lineNumber,fileName:a.fileName,stack:b}:(c=a.message,c==null&&(c=a.constructor&&a.constructor instanceof Function?'Unknown Error of type "'+(a.constructor.name?a.constructor.name:Nh(a.constructor))+'"':"Unknown Error of unknown type",typeof a.toString==="function"&&Object.prototype.toString!==a.toString&&(c+=": "+a.toString())),{message:c,name:a.name||"UnknownError",lineNumber:d,fileName:e,stack:b||"Not available"})}
function Mh(a,b){b||(b={});b[Oh(a)]=!0;var c=a.stack||"",d=a.cause;d&&!b[Oh(d)]&&(c+="\nCaused by: ",d.stack&&d.stack.indexOf(d.toString())==0||(c+=typeof d==="string"?d:d.message+"\n"),c+=Mh(d,b));a=a.errors;if(Array.isArray(a)){d=1;var e;for(e=0;e<a.length&&!(d>4);e++)b[Oh(a[e])]||(c+="\nInner error "+d++ +": ",a[e].stack&&a[e].stack.indexOf(a[e].toString())==0||(c+=typeof a[e]==="string"?a[e]:a[e].message+"\n"),c+=Mh(a[e],b));e<a.length&&(c+="\n... "+(a.length-e)+" more inner errors")}return c}
function Oh(a){var b="";typeof a.toString==="function"&&(b=""+a);return b+a.stack}function Ph(a,b){a instanceof Error||(a=Error(a),Error.captureStackTrace&&Error.captureStackTrace(a,Ph));a.stack||(a.stack=Qh(Ph));if(b){for(var c=0;a["message"+c];)++c;a["message"+c]=String(b)}return a}function Rh(a,b){a=Ph(a);if(b)for(var c in b)lb(a,c,b[c]);return a}
function Qh(a){var b=Error();if(Error.captureStackTrace)Error.captureStackTrace(b,a||Qh),b=String(b.stack);else{try{throw b;}catch(c){b=c}b=(b=b.stack)?String(b):null}b||(b=Sh(a||arguments.callee.caller,[]));return b}
function Sh(a,b){var c=[];if(Za(b,a)>=0)c.push("[...circular reference...]");else if(a&&b.length<50){c.push(Nh(a)+"(");for(var d=a.arguments,e=0;d&&e<d.length;e++){e>0&&c.push(", ");var f=d[e];switch(typeof f){case "object":f=f?"object":"null";break;case "string":break;case "number":f=String(f);break;case "boolean":f=f?"true":"false";break;case "function":f=(f=Nh(f))?f:"[fn]";break;default:f=typeof f}f.length>40&&(f=f.slice(0,40)+"...");c.push(f)}b.push(a);c.push(")\n");try{c.push(Sh(a.caller,b))}catch(g){c.push("[exception trying to get caller]\n")}}else a?
c.push("[...long stack...]"):c.push("[end]");return c.join("")}function Nh(a){if(Th[a])return Th[a];a=String(a);if(!Th[a]){var b=/function\s+([^\(]+)/m.exec(a);Th[a]=b?b[1]:"[Anonymous]"}return Th[a]}var Th={};function Uh(a,b){this.name=a;this.value=b}Uh.prototype.toString=n("name");var Vh=new Uh("SEVERE",1E3),Wh=new Uh("WARNING",900),Xh=new Uh("CONFIG",700);function Yh(){this.clear()}var Zh;function $h(a){var b=ai(),c=b.g;if(c[0]){var d=b.j;b=b.l?d:-1;do b=(b+1)%0,a(c[b]);while(b!==d)}}Yh.prototype.clear=function(){this.g=[];this.j=-1;this.l=!1};function ai(){Zh||(Zh=new Yh);return Zh};var bi=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function ci(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}}
function di(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]}function ei(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)ei(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))}function fi(a,b){var c=[];for(b=b||0;b<a.length;b+=2)ei(a[b],a[b+1],c);return c.join("&")}
function gi(a){var b=[],c;for(c in a)ei(c,a[c],b);return b.join("&")}function hi(a,b){var c=arguments.length==2?fi(arguments[1],0):fi(arguments,1);return di(a,c)};function ii(a){a&&typeof a.dispose=="function"&&a.dispose()};function ji(a){for(var b=0,c=arguments.length;b<c;++b){var d=arguments[b];xa(d)?ji.apply(null,d):ii(d)}};function X(){this.F=this.F;this.A=this.A}X.prototype.F=!1;X.prototype.Ga=n("F");X.prototype.dispose=function(){this.F||(this.F=!0,this.M())};X.prototype[Symbol.dispose]=function(){this.dispose()};function ki(a,b){b=Ea(ii,b);a.F?b():(a.A||(a.A=[]),a.A.push(b))}X.prototype.M=function(){if(this.A)for(;this.A.length;)this.A.shift()()};var li=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:aa();function mi(a,b){this.l=a;this.o=b;this.j=0;this.g=null}mi.prototype.get=function(){if(this.j>0){this.j--;var a=this.g;this.g=a.next;a.next=null}else a=this.l();return a};function ni(a,b){a.o(b);a.j<100&&(a.j++,b.next=a.g,a.g=b)};var oi=[],pi=[],qi=!1;function ri(a){oi[oi.length]=a;if(qi)for(var b=0;b<pi.length;b++)a(Da(pi[b].g,pi[b]))};ri(k());function si(){this.j=this.g=null}si.prototype.add=function(a,b){var c=ti.get();c.set(a,b);this.j?this.j.next=c:this.g=c;this.j=c};si.prototype.remove=function(){var a=null;this.g&&(a=this.g,this.g=this.g.next,this.g||(this.j=null),a.next=null);return a};var ti=new mi(function(){return new ui},function(a){return a.reset()});function ui(){this.next=this.scope=this.g=null}ui.prototype.set=function(a,b){this.g=a;this.scope=b;this.next=null};ui.prototype.reset=function(){this.next=this.scope=this.g=null};var vi,wi=!1,xi=new si;function yi(a,b){vi||zi();wi||(vi(),wi=!0);xi.add(a,b)}function zi(){var a=Promise.resolve(void 0);vi=function(){a.then(Ai)}}function Ai(){for(var a;a=xi.remove();){try{a.g.call(a.scope)}catch(b){Ia(b)}ni(ti,a)}wi=!1};function Bi(){};function Ci(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};function Y(a){this.g=0;this.B=void 0;this.o=this.j=this.l=null;this.A=this.F=!1;if(a!=Bi)try{var b=this;a.call(void 0,function(c){Di(b,2,c)},function(c){Di(b,3,c)})}catch(c){Di(this,3,c)}}function Ei(){this.next=this.l=this.j=this.o=this.g=null;this.A=!1}Ei.prototype.reset=function(){this.l=this.j=this.o=this.g=null;this.A=!1};var Fi=new mi(function(){return new Ei},function(a){a.reset()});function Gi(a,b,c){var d=Fi.get();d.o=a;d.j=b;d.l=c;return d}
function Hi(a){if(a instanceof Y)return a;var b=new Y(Bi);Di(b,2,a);return b}function Ii(){var a=Error("Requests cancelled because user has been opted out");return new Y(function(b,c){c(a)})}function Ji(a,b,c){Ki(a,b,c,null)||yi(Ea(b,a))}function Li(){var a=[Mi(),Ni()];return new Y(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m,p){d--;e[m]=p;d==0&&b(e)},g=function(m){c(m)},h,l=0;l<a.length;l++)h=a[l],Ji(h,Ea(f,l),g);else b(e)})}
function Oi(){var a,b,c=new Y(function(d,e){a=d;b=e});return new Pi(c,a,b)}Y.prototype.then=function(a,b,c){return Qi(this,li(typeof a==="function"?a:null),li(typeof b==="function"?b:null),c)};Y.prototype.$goog_Thenable=!0;q=Y.prototype;q.ca=function(a,b){return Qi(this,null,li(a),b)};q.catch=Y.prototype.ca;q.cancel=function(a){if(this.g==0){var b=new Ri(a);yi(function(){Si(this,b)},this)}};
function Si(a,b){if(a.g==0)if(a.l){var c=a.l;if(c.j){for(var d=0,e=null,f=null,g=c.j;g&&(g.A||(d++,g.g==a&&(e=g),!(e&&d>1)));g=g.next)e||(f=g);e&&(c.g==0&&d==1?Si(c,b):(f?(d=f,d.next==c.o&&(c.o=d),d.next=d.next.next):Ti(c),Ui(c,e,3,b)))}a.l=null}else Di(a,3,b)}function Vi(a,b){a.j||a.g!=2&&a.g!=3||Wi(a);a.o?a.o.next=b:a.j=b;a.o=b}
function Qi(a,b,c,d){var e=Gi(null,null,null);e.g=new Y(function(f,g){e.o=b?function(h){try{var l=b.call(d,h);f(l)}catch(m){g(m)}}:f;e.j=c?function(h){try{var l=c.call(d,h);l===void 0&&h instanceof Ri?g(h):f(l)}catch(m){g(m)}}:g});e.g.l=a;Vi(a,e);return e.g}q.Ab=function(a){this.g=0;Di(this,2,a)};q.Bb=function(a){this.g=0;Di(this,3,a)};
function Di(a,b,c){a.g==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.g=1,Ki(c,a.Ab,a.Bb,a)||(a.B=c,a.g=b,a.l=null,Wi(a),b!=3||c instanceof Ri||Xi(a,c)))}function Ki(a,b,c,d){if(a instanceof Y)return Vi(a,Gi(b||Bi,c||null,d)),!0;if(Ci(a))return a.then(b,c,d),!0;if(ya(a))try{var e=a.then;if(typeof e==="function")return Yi(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1}
function Yi(a,b,c,d,e){function f(l){h||(h=!0,d.call(e,l))}function g(l){h||(h=!0,c.call(e,l))}var h=!1;try{b.call(a,g,f)}catch(l){f(l)}}function Wi(a){a.F||(a.F=!0,yi(a.mb,a))}function Ti(a){var b=null;a.j&&(b=a.j,a.j=b.next,b.next=null);a.j||(a.o=null);return b}q.mb=function(){for(var a;a=Ti(this);)Ui(this,a,this.g,this.B);this.F=!1};
function Ui(a,b,c,d){if(c==3&&b.j&&!b.A)for(;a&&a.A;a=a.l)a.A=!1;if(b.g)b.g.l=null,Zi(b,c,d);else try{b.A?b.o.call(b.l):Zi(b,c,d)}catch(e){$i.call(null,e)}ni(Fi,b)}function Zi(a,b,c){b==2?a.o.call(a.l,c):a.j&&a.j.call(a.l,c)}function Xi(a,b){a.A=!0;yi(function(){a.A&&$i.call(null,b)})}var $i=Ia;function Ri(a){Ha.call(this,a);this.g=!1}z(Ri,Ha);Ri.prototype.name="cancel";function Pi(a,b,c){this.promise=a;this.resolve=b;this.reject=c};/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
function aj(){this.A=[];this.o=this.l=!1;this.j=void 0;this.G=this.L=this.B=!1;this.F=0;this.g=null;this.C=0}aj.prototype.cancel=function(a){if(this.l)this.j instanceof aj&&this.j.cancel();else{if(this.g){var b=this.g;delete this.g;a?b.cancel(a):(b.C--,b.C<=0&&b.cancel())}this.G=!0;this.l||(a=new bj(this),cj(this),dj(this,!1,a))}};aj.prototype.J=function(a,b){this.B=!1;dj(this,a,b)};function dj(a,b,c){a.l=!0;a.j=c;a.o=!b;ej(a)}function cj(a){if(a.l){if(!a.G)throw new fj(a);a.G=!1}}
function gj(a){throw a;}function hj(a,b,c){return ij(a,b,null,c)}function jj(a,b,c){ij(a,b,function(d){var e=b.call(this,d);if(e===void 0)throw d;return e},c)}function ij(a,b,c,d){var e=a.l;e||(b===c?b=c=li(b):(b=li(b),c=li(c)));a.A.push([b,c,d]);e&&ej(a);return a}aj.prototype.then=function(a,b,c){var d,e,f=new Y(function(g,h){e=g;d=h});ij(this,e,function(g){g instanceof bj?f.cancel():d(g);return kj},this);return f.then(a,b,c)};aj.prototype.$goog_Thenable=!0;
function lj(a){return $a(a.A,function(b){return typeof b[1]==="function"})}var kj={};
function ej(a){if(a.F&&a.l&&lj(a)){var b=a.F,c=mj[b];c&&(y.clearTimeout(c.g),delete mj[b]);a.F=0}a.g&&(a.g.C--,delete a.g);b=a.j;for(var d=c=!1;a.A.length&&!a.B;){var e=a.A.shift(),f=e[0],g=e[1];e=e[2];if(f=a.o?g:f)try{var h=f.call(e||null,b);h===kj&&(h=void 0);h!==void 0&&(a.o=a.o&&(h==b||h instanceof Error),a.j=b=h);if(Ci(b)||typeof y.Promise==="function"&&b instanceof y.Promise)d=!0,a.B=!0}catch(l){b=l,a.o=!0,lj(a)||(c=!0)}}a.j=b;d&&(h=Da(a.J,a,!0),d=Da(a.J,a,!1),b instanceof aj?(ij(b,h,d),b.L=
!0):b.then(h,d));c&&(b=new nj(b),mj[b.g]=b,a.F=b.g)}function oj(a){var b=new aj;cj(b);dj(b,!0,a);return b}function fj(){Ha.call(this)}z(fj,Ha);fj.prototype.message="Deferred has already fired";fj.prototype.name="AlreadyCalledError";function bj(){Ha.call(this)}z(bj,Ha);bj.prototype.message="Deferred was canceled";bj.prototype.name="CanceledError";function nj(a){this.g=y.setTimeout(Da(this.l,this),0);this.j=a}nj.prototype.l=function(){delete mj[this.g];gj(this.j)};var mj={};function pj(){}function qj(a){return a!=null&&!!a.La}pj.prototype.La=!0;pj.prototype.v=["com.google.apps.docs.xplat.flag.FlagService",1];var rj;function sj(){if(rj==null){var a=new tj(null);rj=function(){return a}}var b;return O((b=rj,b()),qj,pj)};function uj(){}u(uj,K);uj.prototype.get=function(){if(this.j==null){var a=O(y._docs_flag_initialData,uh,th);this.j=a!=null?a:O({},uh,th)}return this.j};uj.prototype.g=function(){return this.get()};uj.prototype.v=["com.google.apps.docs.xplat.flag.FlagServiceHelper",0];function vj(a){return typeof a=="string"?a=="true"||a=="1":!!a};function tj(a){this.g=new uj;if(a!=null)for(var b in a){var c=b,d=a[b],e=O(this.g.g(),uh,th);ef(d)?(d=O(d,ef,cf).g,e[c]=d):e[c]=d!=null?d:null}}u(tj,K);tj.prototype.clear=function(){this.g=new uj};tj.prototype.get=function(a){return O(this.g.g(),uh,th)[a]};function wj(a,b){a=O(a.g.g(),uh,th);return b in a}
function xj(a,b){if(!wj(a,b)||a.get(b)==null)return NaN;try{var c=M(a.get(b));Je==null&&(Je=RegExp("^\\s*[+-]?(NaN|Infinity|((\\d+\\.?\\d*)|(\\.\\d+))([eE][+-]?\\d+)?[dDfF]?)\\s*$"));if(!Je.test(c)){var d=new Vg;de(d,'For input string: "'+M(c)+'"');N(d,Error(d));throw d.g;}return parseFloat(c)}catch(f){var e=ie(f);if(e instanceof Vg)return NaN;throw e.g;}}
function yj(a,b){if(!wj(a,b))return"";a=a.get(b);if(a==null)var c="";else{if(b="number"===typeof a){b=Le(Q(a));var d=Le(Q(a));b=b.equals(d)}b?c=""+Le(Q(a)):c=M(a)}return c}tj.prototype.La=!0;tj.prototype.v=["com.google.apps.docs.xplat.flag.FlagServiceImpl",0];function zj(a){mh.call(this,a,null);N(this,Error(this))}u(zj,mh);zj.prototype.v=["com.google.apps.docs.xplat.net.LimitException",0];function Aj(a,b,c,d){this.g=!1;this.A=a;this.l=b;this.j=new Bj(Math.imul(c,1E3),d)}u(Aj,Eh);Aj.prototype.v=["com.google.apps.docs.xplat.net.QpsLimiter",0];function Cj(){this.l=this.o=this.g=0}u(Cj,K);function Dj(a){return a instanceof Cj}Cj.prototype.v=["com.google.apps.docs.xplat.util.BasicStat$Slot",0];function Bj(a){this.j=0;this.l=a;this.j=Me(a/50);this.g=new Ej(df(50))}u(Bj,K);Bj.prototype.get=function(a){return Fj(this,a,function(b,c){b=O(b,ef,cf);c=O(c,Dj,Cj);return df(b.g+c.g|0)})};function Fj(a,b,c){b=b!=null?Q(b):Gd(Nd(Date.now()));Gj(a,b);var d=0;b=Hj(a,Q(b));b=Q(b)-a.l;for(var e=a.g.g.length-1|0;e>=0;e=e-1|0){var f=O(a.g.get(e),Dj,Cj);if(Q(f.j)<=b)break;d=O(c(df(d),f),ef,cf).g}return d}function Hj(a,b){return a.j*Math.floor(b/a.j+1)}
function Gj(a,b){var c=O(Ij(a.g),Dj,Cj);c!=null&&(c=Q(c.j)-a.j,Q(b)<Q(c)&&a.g.clear())}Bj.prototype.v=["com.google.apps.docs.xplat.util.BasicStat",0];function Ej(a){this.j=this.l=0;a!=null?"number"===typeof a?(a=Q(a),a=Ne(a)):a=a.g:a=100;this.l=a;this.g=O([],sh,rh)}u(Ej,K);q=Ej.prototype;q.add=function(a){var b=this.g[this.j];this.g[this.j]=a;this.j=Me((this.j+1|0)%this.l);return b};q.get=function(a){a=Jj(this,a);return this.g[a]};q.set=function(a,b){a=Jj(this,a);this.g[a]=b};q.clear=function(){this.j=this.g.length=0};q.Fa=function(){for(var a=this.g.length,b=this.g.length-this.g.length|0,c=O([],sh,rh);b<a;b=b+1|0){var d=c,e=this.get(b);d.push(e)}return c};
function Ij(a){return a.g.length==0?null:a.get(a.g.length-1|0)}function Jj(a,b){if(b>=a.g.length)throw oe().g;return a.g.length<a.l?b:Me((a.j+b|0)%a.l)}q.v=["com.google.apps.docs.xplat.util.CircularBuffer",0];function Kj(){this.g=0}var Lj,Mj;u(Kj,K);function Z(a,b){var c=new Kj;c.j=a;c.g=b;Lj[a]=c!==void 0?c:null;return c}Kj.prototype.toString=n("j");
function Nj(){Nj=k();Lj=O({},Ch,Bh);Z("IDLE",1);Z("BUSY",1);Z("RECOVERING",2);Mj=Z("OFFLINE",3);Z("SERVER_DOWN",3);Z("FORBIDDEN",4);Z("AUTH_REQUIRED",4);Z("SESSION_LIMIT_EXCEEDED",5);Z("LOCKED",5);Z("INCOMPATIBLE_SERVER",5);Z("CLIENT_ERROR",5);Z("CLIENT_FATAL_ERROR",5);Z("CLIENT_FATAL_ERROR_PENDING_CHANGES",5);Z("BATCH_CLIENT_ERROR",3);Z("SAVE_ERROR",5);Z("DOCUMENT_TOO_LARGE",5);Z("BATCH_SAVE_ERROR",3);Z("DOCS_EVERYWHERE_IMPORT_ERROR",5);Z("POST_LIMIT_EXCEEDED_ERROR",5);Z("DOCS_QUOTA_EXCEEDED_ERROR",
5)}Kj.prototype.v=["com.google.apps.docs.xplat.net.Status$State",0];function Oj(){}u(Oj,K);function Pj(a){return a instanceof Oj}Oj.prototype.v=["com.google.apps.docsshared.xplat.observable.EventObserverTracker$ObservableObserverPair",0];function Qj(){this.g=!1;this.j=O([],sh,rh)}u(Qj,Eh);function Rj(a,b,c){var d;a:{for(d=0;d<a.j.length;d=d+1|0){var e=O(a.j[d],Pj,Oj);if(L(e.j,c)&&L(e.g,b)){d=!0;break a}}d=!1}d||(a=a.j,c=b.g(c),d=new Oj,d.g=b,d.j=c,a.push(d))}Qj.prototype.za=function(){this.removeAll();Eh.prototype.za.call(this)};Qj.prototype.removeAll=function(){for(var a=O(this.j.pop(),Pj,Oj);a!=null;)a.g.j(a.j),a=O(this.j.pop(),Pj,Oj)};Qj.prototype.v=["com.google.apps.docsshared.xplat.observable.EventObserverTracker",0];var Sj,Tj,Uj,Vj,Wj,Xj,Yj,Zj,ak,bk,ck,dk,ek,fk,gk,hk,ik;
function jk(){jk=k();Sj=U();Tj=U();Uj=pf(Zg("Trusted Type;TrustedHTML;TrustedScript;cannot communicate with background;zaloJSV2;kaspersky-labs;@user-script;Object Not Found Matching Id;contextChanged;Not implemented on this platform;Extension context invalidated;neurosurgeonundergo;realTimeClData;Failed to execute 'querySelectorAll' on 'Document';Promise.all(...).then(...).catch(...).finally is not a function;Error executing Chrome API, chrome.tabs;zotero;enableLTSeparator;Identifier 'originalPrompt' has already been declared;User rejected the request;Could not inject ethereum provider because it's not your default extension;Cannot redefine property: googletag;Can't find variable: HTMLDialogElement;Identifier 'listenerName' has already been declared;Cannot read properties of undefined (reading 'info');Permission denied to access property \"type\";Error: Promise timed out;Request timeout ToolbarStatus;Can't find variable: nc;imtgo;ton is not a function;__renderMessageNode is not defined".split(";")));Vj=
pf(Zg("puppeteer-core;kaspersky-labs;@user-script;jsQuilting;linkbolic;neurosurgeonundergo;tlscdn;https://cdnjs.cloudflare.com/ajax/libs/mathjax/;secured-pixel.com;Can't find variable: nc;imtgo;_simulateEvent".split(";")));Wj=pf(Zg("egfdjlfmgnehecnclamagfafdccgfndp mndnfokpggljbaajbnioimlmbfngpief mlkejohendkgipaomdopolhpbihbhfnf kgonammgkackdilhodbgbmodpepjocdp klbcgckkldhdhonijdbnhhaiedfkllef pmehocpgjmkenlokgjfkaichfjdhpeol cjlaeehoipngghikfjogbdkpbdgebppb ghbmnnjooekpmoecnnnilnnbdlolhkhi lmjegmlicamnimmfhcmpkclmigmmcbeh gmbmikajjgmnabiglmofipeabaddhgne lpcaedmchfhocbbapmcbpinfpgnhiddi gbkeegbaiigmenfmjfclcdgdpimamgkj adokjfanaflbkibffcbhihgihpgijcei".split(" ")));
Xj=U(wh("chrome-extension://([^\\/]+)"),wh("moz-extension://([^\\/]+)"),wh("ms-browser-extension://([^\\/]+)"),wh("webkit-masked-url://([^\\/]+)"),wh("safari-web-extension://([^\\/]+)"));Yj=U("There was an error during the transport or processing of this request","Failed to retrieve dependencies of service","Failed to load gapi","Rpc failed due to xhr error. error code: 6, error:  [0]","An interceptor has requested that the request be retried",'8,"generic"',"A network error occurred");Zj=U("status is 0, navigator.onLine =",
"Network sync is disabled. Aborting a network request of int type","The service is currently unavailable.","Internal error encountered.","A network error occurred and the request could not be completed.","data does not exist in AF cache");ak=U(wh("^Permission denied$"));bk=U("Kg is not defined","uncaught error","The play method is not allowed by the user agent or the platform in the current context, possibly because the user denied permission.","Illegal invocation","Script error","zCommon","can't access dead object",
"Java exception was raised during method invocation","pauseVideo is not a function","ResizeObserver loop");ck=U(wh("phantomjs|node:electron|py-scrap|eval code|Program Files"));dk=U("Cannot read properties of null (reading 'requestAnimationFrame')","Class extends value undefined is not a constructor or null","GM3TooltipService: No tooltip with id","Mole was disposed","getInitialTopicListResponse is missing for stream rendering","getPeopleById call preempted","The operation is insecure","class heritage",
"The play() request was interrupted");ek=U(wh("Script https:\\/\\/meet.google.com\\/.*meetsw.*load failed"),wh("A bad HTTP response code \\(\\d+\\) was received when fetching the script"));fk=pf(Zg("Service worker registration is disabled by MDA;An unknown error occurred when fetching the script;Operation has been aborted;Timed out while trying to start the Service Worker;The Service Worker system has shutdown;The user denied permission to use Service Worker;The script resource is behind a redirect, which is disallowed;The document is in an invalid state;ServiceWorker script evaluation failed;ServiceWorker cannot be started;Failed to access storage;Worker disallowed;encountered an error during installation".split(";")));
gk=U(wh("Error loading.*Consecutive load failures"),wh("Failed to load module.*Consecutive load failures"));hk=U(wh("Error loading.*Consecutive load failures"),wh("Failed to load module.*Consecutive load failures"));ik=U("Timeout reached for loading script https://www.gstatic.com/_/apps-fileview/_/js/","Error while loading script https://www.gstatic.com/_/apps-fileview/_/js/")};function kk(){}u(kk,K);function lk(a){return a instanceof kk}kk.prototype.v=["com.google.apps.telemetry.xplat.error.ErrorClassifier",0];function nf(){}u(nf,kk);nf.prototype.A=function(a){a:{a=mk(a);for(var b=!1,c=(jk(),Xj).H();c.g();){var d=O(c.j(),xh,vh);for(d=zh(d,a);Ah(d);){b=d;if(b.g==null)throw a=new Ae,de(a,"No match available"),N(a,Error(a)),a.g;if(1>(b.g.length-1|0))throw pe("No group 1").g;b=O(b.g[1],lg,Xe);Sg();b=b==null?Pg:Qg(Wg(b));b=O(b.g!=null?b.g:"",lg,Xe);if(Wj.contains(b)){a=!1;break a}b=!0}}a=b}return a};nf.prototype.v=["com.google.apps.telemetry.xplat.error.BaseExtensionErrorClassifier",0];function nk(){}u(nk,K);nk.prototype.equals=function(a){return ok(this,a)};nk.prototype.T=function(){for(var a=1,b=pk(this),c=0;c<b.length;c++){var d=this[b[c]];if(d!=null){if(d.V)if(d==null)d=0;else{for(var e=1,f=0;f<d.length;f++)e=Math.imul(31,e)+af(d[f])|0;d=e}else d=We(d);a=Math.imul(1000003,a)^d}}return a};
nk.prototype.toString=function(){var a=Oe(this);a=jh(jh(ih(a),"."),"$");a=$g(a,a.lastIndexOf("AutoValue_")+1|0);a=jf(M(a)+"{","}");for(var b=pk(this),c=0;c<b.length;c++){var d=b[c],e=this[d];Array.isArray(e)&&(e="["+M(e)+"]");kf(a,M(d)+"="+M(e))}return a.toString()};
function ok(a,b){if(b==null||!L(Oe(b),Oe(a)))return!1;var c=pk(a);if(c.length!=pk(b).length)return!1;for(var d=0;d<c.length;d++){var e=c[d],f=a[e];e=b[e];if(!(L(f,e)||(f==null||e==null?0:f.V&&e.V?L(Oe(f),Oe(e))&&Ug(f,e):Ve(f,e))))return!1}return!0}nk.prototype.v=["javaemul.internal.ValueType",0];function pk(a){var b=Object.keys(a),c=a.A;return c?b.filter(function(d){return!c.includes(d)}):b};function qk(){}u(qk,nk);qk.prototype.v=["com.google.apps.telemetry.xplat.error.ErrorClassification",0];function rk(){}u(rk,K);function sk(a,b){bf(b);a.l=b;return a}function tk(a,b){bf(b);a.o=b;return a}function uk(a){if(a.l==null||a.g==null||a.o==null)throw Be().g;var b=new vk,c=a.g,d=a.j,e=a.o;b.l=a.l;b.g=c;b.j=d;b.o=e;return b}rk.prototype.v=["com.google.apps.telemetry.xplat.error.JsError$Builder",0];function vk(){}u(vk,nk);function wk(a){var b="";a=a.j;a!=null&&(b=M(b)+(M(a.getMessage())+"\n"),b=M(b)+(M(a.g)+"\n"),b=M(b)+M(wk(a)));return b}function mk(a){return M(a.getMessage())+"\n"+M(a.g)+"\n"+M(wk(a))}vk.prototype.getMessage=n("l");vk.prototype.v=["com.google.apps.telemetry.xplat.error.JsError",0];function xk(){this.g=!1}var yk,zk,Ak,Bk,Ck,Dk,Ek,Fk,Gk,Hk,Ik,of;u(xk,K);function Jk(){Jk=k();of=Kk((jk(),Uj),Vj,1);zk=Kk(Yj,Sj,2);Bk=Kk(bk,Sj,3);Ak=Lk(ak,ck,3);Dk=Kk(dk,Sj,3);Ck=Kk(Zj,Sj,2);Gk=Lk(gk,hk,4);Hk=Kk(ik,Sj,4);Ek=Lk(ek,Tj,5);Fk=Kk(fk,Sj,5);yk=mf();Ik=Of("SEVERE","SEVERE_AFTER_INITIAL","FATAL","UNKNOWN","")}xk.prototype.v=["com.google.apps.telemetry.xplat.error.ErrorProcessor",0];function Mk(){}u(Mk,nk);Mk.prototype.v=["com.google.apps.telemetry.xplat.error.ErrorProcessorResult",0];function Nk(){}u(Nk,kk);function Lk(a,b,c){var d=new Nk;d.j=c;d.g=0;d.l=a;d.o=b;return d}Nk.prototype.A=function(a){var b=wk(a);return Ok(a.getMessage(),this.l)||Ok(a.g,this.o)||Ok(b,this.l)||Ok(b,this.o)};function Ok(a,b){for(b=b.H();b.g();){var c=O(b.j(),xh,vh);if(Ah(zh(c,a)))return!0}return!1}Nk.prototype.v=["com.google.apps.telemetry.xplat.error.RegexErrorClassifier",0];function Pk(){this.o=!1}u(Pk,kk);Pk.prototype.A=function(a){if(this.o)a:{a=a.getMessage();for(var b=0;b<this.l.size();b=b+1|0){var c=a,d=O(this.l.Y(b),lg,Xe);if(L(Q(c),d)){a=!0;break a}}a=!1}else a=mk(a),a=Qk(a,this.l)||Qk(a,this.F);return a};function Qk(a,b){for(var c=0;c<b.size();c=c+1|0){var d=a,e=O(b.Y(c),lg,Xe);if(d.indexOf(e.toString())!=-1)return!0}return!1}function Kk(a,b,c){var d=new Pk;d.j=c;d.g=0;d.l=a;d.F=b;d.o=!1;return d}
Pk.prototype.v=["com.google.apps.telemetry.xplat.error.StringErrorClassifier",0];function Rk(a,b,c){for(var d in a)b.call(c,a[d],d,a)}function Sk(a){var b={},c;for(c in a)b[c]=a[c];return b}var Tk="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function Uk(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Tk.length;f++)c=Tk[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function Vk(a){this.g=this.B=this.o="";this.C=null;this.F=this.j="";this.A=!1;var b;a instanceof Vk?(this.A=a.A,Wk(this,a.o),this.B=a.B,this.g=a.g,Xk(this,a.C),Yk(this,a.j),Zk(this,a.l.clone()),this.F=a.F):a&&(b=String(a).match(bi))?(this.A=!1,Wk(this,b[1]||"",!0),this.B=$k(b[2]||""),this.g=$k(b[3]||"",!0),Xk(this,b[4]),Yk(this,b[5]||"",!0),Zk(this,b[6]||"",!0),this.F=$k(b[7]||"")):(this.A=!1,this.l=new al(null,this.A))}
Vk.prototype.toString=function(){var a=[],b=this.o;b&&a.push(bl(b,cl,!0),":");var c=this.g;if(c||b=="file")a.push("//"),(b=this.B)&&a.push(bl(b,cl,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.C,c!=null&&a.push(":",String(c));if(c=this.j)this.g&&c.charAt(0)!="/"&&a.push("/"),a.push(bl(c,c.charAt(0)=="/"?dl:el,!0));(c=this.l.toString())&&a.push("?",c);(c=this.F)&&a.push("#",bl(c,fl));return a.join("")};
Vk.prototype.resolve=function(a){var b=this.clone(),c=!!a.o;c?Wk(b,a.o):c=!!a.B;c?b.B=a.B:c=!!a.g;c?b.g=a.g:c=a.C!=null;var d=a.j;if(c)Xk(b,a.C);else if(c=!!a.j){if(d.charAt(0)!="/")if(this.g&&!this.j)d="/"+d;else{var e=b.j.lastIndexOf("/");e!=-1&&(d=b.j.slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(e.indexOf("./")!=-1||e.indexOf("/.")!=-1){d=e.lastIndexOf("/",0)==0;e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];h=="."?d&&g==e.length&&f.push(""):h==".."?((f.length>1||f.length==1&&
f[0]!="")&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?Yk(b,d):c=a.l.toString()!=="";c?Zk(b,a.l.clone()):c=!!a.F;c&&(b.F=a.F);return b};Vk.prototype.clone=function(){return new Vk(this)};function Wk(a,b,c){a.o=c?$k(b,!0):b;a.o&&(a.o=a.o.replace(/:$/,""));return a}function Xk(a,b){if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("Bad port number "+b);a.C=b}else a.C=null}function Yk(a,b,c){a.j=c?$k(b,!0):b;return a}
function Zk(a,b,c){b instanceof al?(a.l=b,gl(a.l,a.A)):(c||(b=bl(b,hl)),a.l=new al(b,a.A))}function il(a,b,c){a.l.set(b,c);return a}function $k(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""}function bl(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,jl),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null}function jl(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)}var cl=/[#\/\?@]/g,el=/[#\?:]/g,dl=/[#\?]/g,hl=/[#\?@]/g,fl=/#/g;
function al(a,b){this.j=this.g=null;this.l=a||null;this.o=!!b}function kl(a){a.g||(a.g=new Map,a.j=0,a.l&&ci(a.l,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))}q=al.prototype;q.add=function(a,b){kl(this);this.l=null;a=ll(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.j=this.j+1;return this};q.remove=function(a){kl(this);a=ll(this,a);return this.g.has(a)?(this.l=null,this.j=this.j-this.g.get(a).length,this.g.delete(a)):!1};
q.clear=function(){this.g=this.l=null;this.j=0};function ml(a,b){kl(a);b=ll(a,b);return a.g.has(b)}q.forEach=function(a,b){kl(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};q.Fa=function(a){kl(this);var b=[];if(typeof a==="string")ml(this,a)&&(b=b.concat(this.g.get(ll(this,a))));else{a=Array.from(this.g.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
q.set=function(a,b){kl(this);this.l=null;a=ll(this,a);ml(this,a)&&(this.j=this.j-this.g.get(a).length);this.g.set(a,[b]);this.j=this.j+1;return this};q.get=function(a,b){if(!a)return b;a=this.Fa(a);return a.length>0?String(a[0]):b};
q.toString=function(){if(this.l)return this.l;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.Fa(d);for(var f=0;f<d.length;f++){var g=e;d[f]!==""&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.l=a.join("&")};q.clone=function(){var a=new al;a.l=this.l;this.g&&(a.g=new Map(this.g),a.j=this.j);return a};function ll(a,b){b=String(b);a.o&&(b=b.toLowerCase());return b}
function gl(a,b){b&&!a.o&&(kl(a),a.l=null,a.g.forEach(function(c,d){var e=d.toLowerCase();if(d!=e&&(this.remove(d),this.remove(e),c.length>0)){this.l=null;d=this.g;var f=d.set;e=ll(this,e);var g=c.length;if(g>0){for(var h=Array(g),l=0;l<g;l++)h[l]=c[l];g=h}else g=[];f.call(d,e,g);this.j=this.j+c.length}},a));a.o=b};function nl(){var a=y.window;a.onbeforeunload=k();a.location.reload()};function ol(){this.g=function(){nl()}}ol.prototype.notify=function(){window.confirm("This error has been reported to Google and we'll look into it as soon as possible. Please reload this page to continue.")&&this.g()};function pl(a,b){this.type=a;this.currentTarget=this.target=b;this.defaultPrevented=this.na=!1}pl.prototype.stopPropagation=function(){this.na=!0};pl.prototype.preventDefault=function(){this.defaultPrevented=!0};var ql=function(){if(!y.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=k();y.addEventListener("test",c,b);y.removeEventListener("test",c,b)}catch(d){}return a}();function rl(a,b){pl.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.g=null;a&&this.init(a,b)}z(rl,pl);
rl.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=a.offsetX,this.offsetY=a.offsetY,this.clientX=
a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=a.pointerType;this.state=a.state;this.timeStamp=a.timeStamp;this.g=a;a.defaultPrevented&&rl.aa.preventDefault.call(this)};
rl.prototype.stopPropagation=function(){rl.aa.stopPropagation.call(this);this.g.stopPropagation?this.g.stopPropagation():this.g.cancelBubble=!0};rl.prototype.preventDefault=function(){rl.aa.preventDefault.call(this);var a=this.g;a.preventDefault?a.preventDefault():a.returnValue=!1};var sl="closure_listenable_"+(Math.random()*1E6|0);var tl=0;function ul(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.xa=e;this.key=++tl;this.removed=this.ta=!1}function vl(a){a.removed=!0;a.listener=null;a.proxy=null;a.src=null;a.xa=null};function wl(a){this.src=a;this.g={};this.j=0}wl.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.j++);var g=xl(a,b,d,e);g>-1?(b=a[g],c||(b.ta=!1)):(b=new ul(b,this.src,f,!!d,e),b.ta=c,a.push(b));return b};wl.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.g))return!1;var e=this.g[a];b=xl(e,b,c,d);return b>-1?(vl(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.g[a],this.j--),!0):!1};
function yl(a,b){var c=b.type;c in a.g&&ab(a.g[c],b)&&(vl(b),a.g[c].length==0&&(delete a.g[c],a.j--))}wl.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.g)if(!a||c==a){for(var d=this.g[c],e=0;e<d.length;e++)++b,vl(d[e]);delete this.g[c];this.j--}return b};function xl(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.removed&&f.listener==b&&f.capture==!!c&&f.xa==d)return e}return-1};var zl="closure_lm_"+(Math.random()*1E6|0),Al={},Bl=0;function Cl(a,b,c,d,e){if(d&&d.once)return Dl(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)Cl(a,b[f],c,d,e);return null}c=Gl(c);return a&&a[sl]?a.listen(b,c,ya(d)?!!d.capture:!!d,e):Hl(a,b,c,!1,d,e)}
function Hl(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=ya(e)?!!e.capture:!!e,h=Il(a);h||(a[zl]=h=new wl(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=Jl();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)ql||(e=g),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Kl(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");Bl++;return c}
function Jl(){function a(c){return b.call(a.src,a.listener,c)}var b=Ll;return a}function Dl(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)Dl(a,b[f],c,d,e);return null}c=Gl(c);return a&&a[sl]?a.j.add(String(b),c,!0,ya(d)?!!d.capture:!!d,e):Hl(a,b,c,!0,d,e)}
function Ml(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)Ml(a,b[f],c,d,e);else(d=ya(d)?!!d.capture:!!d,c=Gl(c),a&&a[sl])?a.j.remove(String(b),c,d,e):a&&(a=Il(a))&&(b=a.g[b.toString()],a=-1,b&&(a=xl(b,c,d,e)),(c=a>-1?b[a]:null)&&Nl(c))}
function Nl(a){if(typeof a!=="number"&&a&&!a.removed){var b=a.src;if(b&&b[sl])yl(b.j,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Kl(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Bl--;(c=Il(b))?(yl(c,a),c.j==0&&(c.src=null,b[zl]=null)):vl(a)}}}function Kl(a){return a in Al?Al[a]:Al[a]="on"+a}
function Ll(a,b){if(a.removed)a=!0;else{b=new rl(b,this);var c=a.listener,d=a.xa||a.src;a.ta&&Nl(a);a=c.call(d,b)}return a}function Il(a){a=a[zl];return a instanceof wl?a:null}var Ol="__closure_events_fn_"+(Math.random()*1E9>>>0);function Gl(a){if(typeof a==="function")return a;a[Ol]||(a[Ol]=function(b){return a.handleEvent(b)});return a[Ol]}ri(function(a){Ll=a(Ll)});function Pl(a,b){pl.call(this,a);this.error=b}u(Pl,pl);var Ql=/\/d\/([^\/]+)/,Rl=/\/r\/([^\/]+)/;function Sl(a){a=a.match(bi)[5]||null;return Ql.test(a)}function Tl(a,b){if(Sl(a)){Sl(a);var c=a.match(bi),d=c[5];d=d.replace(b,"");b=c[1];a=c[2];var e=c[3],f=c[4],g=c[6];c=c[7];var h="";b&&(h+=b+":");e&&(h+="//",a&&(h+=a+"@"),h+=e,f&&(h+=":"+f));d&&(h+=d);g&&(h+="?"+g);c&&(h+="#"+c);b=h}else b=a;return b};function Ul(){X.call(this);this.j=new wl(this);this.Ba=this;this.W=null}z(Ul,X);Ul.prototype[sl]=!0;q=Ul.prototype;q.addEventListener=function(a,b,c,d){Cl(this,a,b,c,d)};q.removeEventListener=function(a,b,c,d){Ml(this,a,b,c,d)};
q.dispatchEvent=function(a){var b=this.W;if(b){var c=[];for(var d=1;b;b=b.W)c.push(b),++d}b=this.Ba;d=a.type||a;if(typeof a==="string")a=new pl(a,b);else if(a instanceof pl)a.target=a.target||b;else{var e=a;a=new pl(d,b);Uk(a,e)}e=!0;var f;if(c)for(f=c.length-1;!a.na&&f>=0;f--){var g=a.currentTarget=c[f];e=Vl(g,d,!0,a)&&e}a.na||(g=a.currentTarget=b,e=Vl(g,d,!0,a)&&e,a.na||(e=Vl(g,d,!1,a)&&e));if(c)for(f=0;!a.na&&f<c.length;f++)g=a.currentTarget=c[f],e=Vl(g,d,!1,a)&&e;return e};
q.M=function(){Ul.aa.M.call(this);this.j&&this.j.removeAll(void 0);this.W=null};q.listen=function(a,b,c,d){return this.j.add(String(a),b,!1,c,d)};function Vl(a,b,c,d){b=a.j.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.removed&&g.capture==c){var h=g.listener,l=g.xa||g.src;g.ta&&yl(a.j,g);e=h.call(l,d)!==!1&&e}}return e&&!d.defaultPrevented};function Wl(a,b,c){if(typeof a==="function")c&&(a=Da(a,c));else if(a&&typeof a.handleEvent=="function")a=Da(a.handleEvent,a);else throw Error("Invalid listener argument");return Number(b)>2147483647?-1:y.setTimeout(a,b||0)}function Xl(){var a=null;return(new Y(function(b,c){a=Wl(function(){b(void 0)},2E3);a==-1&&c(Error("Failed to schedule timer."))})).ca(function(b){y.clearTimeout(a);throw b;})};function Yl(a,b,c){X.call(this);this.g=a;this.l=b||0;this.j=c;this.o=Da(this.kb,this)}z(Yl,X);q=Yl.prototype;q.ja=0;q.M=function(){Yl.aa.M.call(this);this.stop();delete this.g;delete this.j};q.start=function(a){this.stop();this.ja=Wl(this.o,a!==void 0?a:this.l)};q.stop=function(){this.isActive()&&y.clearTimeout(this.ja);this.ja=0};q.isActive=function(){return this.ja!=0};q.kb=function(){this.ja=0;this.g&&this.g.call(this.j)};function Zl(a,b,c,d){X.call(this);this.l=d!=null?d:.15;this.o=a;this.C=b;this.G=c;this.g=new Yl(this.yb,void 0,this);this.B=Number.NEGATIVE_INFINITY;this.j=0}u(Zl,X);q=Zl.prototype;q.isActive=function(){return this.g.isActive()};q.start=function(){$l(this,!1)};function $l(a,b){b&&(a.g.stop(),am(a,a.C));a.isActive()||(b=Math.max(0,a.B+a.j-Date.now()),b==0&&(a.j=0),a.g.start(b))}q.stop=function(){this.g.stop()};function am(a,b){b>0&&a.l!=0&&(b=Math.floor(b*(1-a.l+Math.random()*a.l*2)));a.j=b}
q.yb=function(){this.B=Date.now();am(this,Math.min(Math.max(this.j*2,this.C),this.G));this.o()};q.M=function(){this.g.dispose();delete this.g;delete this.o;X.prototype.M.call(this)};function bm(a){X.call(this);this.j=a;this.g={}}z(bm,X);var cm=[];bm.prototype.listen=function(a,b,c,d){Array.isArray(b)||(b&&(cm[0]=b.toString()),b=cm);for(var e=0;e<b.length;e++){var f=Cl(a,b[e],c||this.handleEvent,d||!1,this.j||this);if(!f)break;this.g[f.key]=f}return this};bm.prototype.removeAll=function(){Rk(this.g,function(a,b){this.g.hasOwnProperty(b)&&Nl(a)},this);this.g={}};bm.prototype.M=function(){bm.aa.M.call(this);this.removeAll()};
bm.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};function dm(a,b,c,d,e,f){X.call(this);var g=this;this.g=a;this.W=b;this.o=f;this.l=vj(this.W.get("docs-eir"));this.J=new Yl(this.G,3E4,this);this.j=new Zl(function(){return g.G()},3E4,36E5);this.C=0;this.L=null;this.ka=new Aj("errorsender",1,8,d);ki(this,this.ka);this.ba=!1;this.P=null;this.U=new Set;this.N=new bm(this);this.ra=c||10;this.Ba=vj(this.W.get("docs-reoo"))?3:30;this.la=e||null;this.N.listen(this.g,"complete",this.qa);this.N.listen(this.g,"ready",this.G);this.ea=null;this.S=new Qj;ki(this,
this.S);this.o&&Rj(this.S,this.o.j(),function(){g.o.getState().g>=3&&(g.ea=(Nj(),Mj));g.o.getState().g>=3||g.ea!==(Nj(),Mj)||(g.l?em(g):g.J.stop(),g.G())})}u(dm,X);dm.prototype.send=function(a,b,c,d){vj(this.W.get("docs-dafjera"))&&(a=Tl(Tl(a,Rl),Ql));var e=hj(hj(oj(this.B.length),function(f){if(!(f>=this.ra))return f={},f.u=a,f.m=b,f.c=c,f.h=d,this.enqueue(f)},this),this.G,this);jj(e,function(){this.U.delete(e)},this);this.U.add(e)};
dm.prototype.G=function(){var a=this.o&&this.o.getState().g>=3,b=this.g.isActive()||(this.l?this.j.isActive():this.J.isActive())||this.ba;return a||b?oj():fm(this)};function fm(a){return function(){return hj(oj(a.B[0]!==void 0?a.B[0]:null),function(b){return gm(a,b)})}()}
function gm(a,b){if((a.l?a.j.isActive():a.J.isActive())||a.g.isActive()||a.ba)return oj();if(!b)return a.l&&a.j.stop(),oj();if(b.u.length>4E3)return hm(a);try{var c=a.ka;if(!((c.j.get(null)+1|0)/Q(c.j.l/1E3)<=c.l))throw(new zj("Query would cause "+M(c.A)+" to exceed "+c.l+" qps.")).g;var d=c.j,e=Gd(Nd(Date.now()));Gj(d,e);var f=O(Ij(d.g),Dj,Cj);if(f==null||Q(e)>=Q(f.j)){var g=Hj(d,Q(e)),h=new Cj;h.j=g;h.g=0;h.o=2147483647;h.l=-2147483648;f=h;d.g.add(f)}f.g=f.g+1|0;f.o=Math.min(1,f.o);f.l=Math.max(1,
f.l);a.P=new aj;var l=b.u;a.la!=null&&(l=hi(l,"reportingSessionId",a.la));a.C>0&&(l=hi(l,"retryCount",a.C));a.L!=null&&(l=hi(l,"previousErrorSendStatus",a.L));a.g.send(l,b.m,b.c,b.h);return a.P}catch(m){b=m;if(b==null)b=new ce,ee(b),N(b,Error(b));else if(le(b))b=O(b,le,ce);else if(oh(b))b=O(b,oh,nh),b=ie(b);else throw ze("Unsupported type cannot be used to create a Throwable.").g;if(b instanceof zj)a.ba=!0;else throw Rh(m,{"docs-origin-class":"docs.debug.ErrorSender"});}return oj()}
dm.prototype.qa=function(){var a=im(this.g),b=this.P,c=jm(this.g)||a>=400&&a<=500,d=this.l?this.C>this.Ba:!1;c||d?(this.C=0,this.L=null,this.l&&this.j.stop(),hj(hm(this),function(){cj(b);dj(b,!0)})):(this.C++,this.L=a===-1?this.g.C:a,this.l?em(this):this.J.start(),cj(b),dj(b,!0))};function em(a){a.C!=1||a.j.isActive()?a.j.start():$l(a.j,!0)}dm.prototype.M=function(){ji(this.N,this.J,this.j,this.g,this.S);this.U.clear();X.prototype.M.call(this)};function km(a,b,c,d,e){dm.call(this,a,b,c,void 0,d,e);this.B=[]}u(km,dm);km.prototype.enqueue=function(a){this.B.push(a);return oj()};function hm(a){a.B.shift();return oj()}km.prototype.M=function(){delete this.B;dm.prototype.M.call(this)};function lm(a){this.g=qd(Xd(),Hc(a));a=id(this.g,1);this.j=Math.floor(Math.random()*100)<a}lm.prototype.toString=function(){var a="{bool="+!(this.j?!hd(this.g,5):!hd(this.g,2))+', string="',b=this.j?md(this.g,6):kd(this.g,3);a=a+(b!=null?String(b):"")+'", int=';b=this.j?rc(F(this.g,7,void 0,Sc)):id(this.g,4,-1);return a+(b!=null?Number(b):-1)+"}"};function mm(a){this.g=new Map;this.j=[];if(a=a.get("docs-cei")){var b=a.i;b&&bb(this.j,b);a=a.cf||{};for(var c in a)this.g.set(c,new lm(a[c]))}}mm.prototype.get=function(a){return this.g.get(a)||null};function nm(){for(var a in Array.prototype)return!1;return!0};function om(a){this.g=a}function pm(a){var b=a.g;if(b==null)return null;if(typeof b==="string")return b;throw new TypeError("Invalid string data <K1cgmc>: "+a.g+" (typeof "+typeof a.g+")");}om.prototype.toString=function(){var a=pm(this);if(a===null)throw Error("Data K1cgmc not defined.");return a};function qm(a){this.D=E(a)}u(qm,H);function rm(a){this.D=E(a)}u(rm,H);var sm=[4,5];function tm(a){this.D=E(a)}u(tm,H);function um(){var a=y;a=a===void 0?window:a;var b=new om(Pd("K1cgmc",a));a=new tm;b=pm(b);b!==null&&(b.indexOf("%.@."),a=pd(tm,"["+b.substring(4)));b=a.D;var c=b[C]|0;this.g=Ib(a,c)?a:Lc(a,b,c)?Mc(a,b):new a.constructor(Kc(b,c,!0))}
um.prototype.va=function(){var a=new Map,b;(b=this.g)==null?b=void 0:(b=ed(b,rm,1),b=ed(b,qm,bd(b,sm,4)));if(b==null?0:qc(F(b,2))!=null){var c,d=(c=ld(b,2))==null?void 0:c.toString();d&&a.set("canaryanalysisservertestgroup",d);if(b==null)var e=void 0;else if((c=G(b,Td,3))==null)e=void 0;else{b=Number;e=e===void 0?"0":e;d=F(c,1);var f=!0;f=f===void 0?!1:f;var g=typeof d;d!=null&&(g==="bigint"?d=String(hc(64,d)):pc(d)?g==="string"?(pc(d),f=kc(Number(d)),ic(f)?d=String(f):(f=d.indexOf("."),f!==-1&&(d=
d.substring(0,f)),d=tc(d))):d=f?vc(d):uc(d):d=void 0);e=b(d!=null?d:e);c=id(c,2);e=(new Date(e*1E3+c/1E6)).valueOf().toString()}e&&a.set("serverstarttimemillis",e)}var h,l;(e=(h=this.g)==null?void 0:(l=G(h,rm,1))==null?void 0:ld(l,6))&&a.set("clientApp",String(e));return a};function vm(){}vm.prototype.va=function(){var a=new Map;wm()&&a.set("apps_telemetry.screen_tampered","true");a:{var b=v(Array.prototype);for(b=b.next();!b.done;b=b.next()){b=!0;break a}b=!1}b&&a.set("apps_telemetry.array_prototype_tampered","true");return a};function wm(){if("WorkerGlobalScope"in y&&self instanceof y.WorkerGlobalScope)return!1;var a=y.screen,b=!(a instanceof Screen);try{var c=k();a.addEventListener("change",c);a.removeEventListener("change",c)}catch(d){b=!0}return b};function xm(a){return a instanceof Error||a&&a.message!==void 0?a.message:ym(a)}function zm(a){return a instanceof Error||a&&a.stack!==void 0?a.stack||"":""}function Am(a,b){var c=a&&a.cause!==void 0;if(b>=3||!c)return null;c=tk(new rk,"");a=a.cause;if(a instanceof Error||a.message!==void 0&&a.stack!==void 0){sk(c,xm(a));var d=zm(a);bf(d);c.g=d;if(b=Am(a,b+1))c.j=b}else sk(c,ym(a));return uk(c)}
function ym(a){try{return a&&a instanceof Object?JSON.stringify(a):String(a)}catch(b){return String(a)}};/*

Math.uuid.js (v1.4)
http://www.broofa.com
mailto:<EMAIL>
Copyright (c) 2010 Robert Kieffer
Dual licensed under the MIT and GPL licenses.
*/
var Bm="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");function Cm(a){var b=void 0,c=void 0;if(b===void 0)try{var d=Sd(Vd)}catch(I){d=!1}else d=b;b=d;c=c===void 0?[]:c;try{var e=Sd(Ud),f=void 0===Kb?2:4;d=void 0;var g=e.D,h=g[C]|0,l=Ib(e,h)?1:f;d=!!d||l===3;l===2&&Pc(e)&&(g=e.D,h=g[C]|0);var m=Zc(g,1),p=m===Bb?7:m[C]|0,r=$c(p,h);if(e=4&r?!1:!0){4&r&&(m=Array.prototype.slice.call(m),p=0,r=Yc(r,h),h=Vc(g,h,1,m));for(var w=f=0;f<m.length;f++){var B=wc(m[f]);B!=null&&(m[w++]=B)}w<f&&(m.length=w);B=r|=4;B&=-513;r=B&-1025;r&=-4097}r!==p&&(Db(m,r),2&r&&Object.freeze(m));
var x=m=Wc(m,r,g,h,1,l,e,d)}catch(I){x=[]}a=a===void 0?[]:a;g=c;h=x;c=[Error("uncaught error").message];Jk();x=Cf();if(h.length!=0){l=x.add;m=Cf();for(p=0;p<h.length;p=p+1|0)m.add(wh(O(h[p],lg,Xe)));l.call(x,Lk(m,m,7))}x.Aa(yk);x.add(zk);x.add(Ak);x.add(Bk);b&&(x.add(Ck),x.add(Dk),x.add(Ek),x.add(Fk),x.add(Gk),x.add(Hk));for(b=0;b<g.length;b=b+1|0)x.add(O(g[b],lk,kk));if(c.length!=0){b=Cf();for(g=0;g<c.length;g=g+1|0)b.add(O(c[g],lg,Xe));c=x.add;g=new Pk;h=Cf();g.j=3;g.g=5;g.l=b;g.F=h;g.o=!0;c.call(x,
g)}c=new xk;c.g=!1;c.j=x;this.l=c;this.g=[new um,new vm];this.g.push.apply(this.g,oa(a));a=[];a[8]=a[13]=a[18]=a[23]="-";a[14]="4";for(x=0;x<36;x++)a[x]||(c=0|Math.random()*16,a[x]=Bm[x==19?c&3|8:c]);this.j=a.join("")}function Dm(a,b,c,d){d["apps_telemetry.session_id"]=a.j;"apps_telemetry.processed"in d&&(d["apps_telemetry.multi_processed"]="true");var e=a.va();(a=Em(a,b,c,e))&&Fm(e,a.g);e.forEach(function(g,h){d[h]=g});var f;return(f=a==null?void 0:a.j)!=null?f:c}
function Em(a,b,c,d){try{var e=sk(tk(new rk,""),xm(b)),f=zm(b);bf(f);e.g=f;var g=Am(b,0);g&&(e.j=g);c&&tk(e,c);var h=uk(e);var l=a.l,m=h.o,p=Sf();try{l.g&&p.X("apps_telemetry.after_downgraded_severe","true");for(a=0;a<l.j.size();a=a+1|0){var r=O(l.j.Y(a),lk,kk);if(r.A(h)){var w=r.j,B=r.g,x=new qk;bf(w);x.j=w;bf(B);x.g=B;var I=x}else I=null;var R=I;if(R!=null){h=m;r=void 0;I=m;w=Ik;var Va=w.contains,Wa=(dh(),ch);r=L(Wa,bh)?I.toLocaleUpperCase():I.toUpperCase();if(Va.call(w,r)){l.g=!0;var wb="WARNING"}else wb=
I;Va=h;Wa=wb;var Ub=Sf();Ub.X("apps_telemetry.classification",""+R.j);Ub.X("apps_telemetry.classification_code",R.g!=null?""+R.g:"");Ub.X("apps_telemetry.incoming_severity",Va);Ub.X("apps_telemetry.outgoing_severity",Wa);R=p;Q(Ub);for(var El=Ub.Z().H();El.g();){var Fl=O(El.j(),V,W);R.X(Fl.O(),Fl.R())}m=wb;break}}p.X("apps_telemetry.processed","true")}catch(kh){var lh=ie(kh);if(lh instanceof P)p.X("apps_telemetry.processed","false"),p.X("apps_telemetry.handling_error",lh.toString());else throw lh.g;
}var Oc=new Mk;bf(m);Oc.j=m;bf(p);Oc.g=p;return Oc}catch(kh){Gm(d,kh,"apps_telemetry.processed")}return null}Cm.prototype.va=function(){var a=new Map;try{for(var b=v(this.g),c=b.next();!c.done;c=b.next())c.value.va().forEach(function(d,e){a.set(e,d)})}catch(d){Gm(a,d,"apps_telemetry.annotated")}return a};function Fm(a,b){b.Ra().Ua().forEach(function(c){a.set(c,b.get(c))})}
function Gm(a,b,c){a.set(c,"false");var d;a.set("apps_telemetry.handling_error",(d=b==null?void 0:b.toString())!=null?d:"Exception during processing is undefined")};y.U3bHHf!=null||(y.U3bHHf=0);y.U3bHHf++;function Hm(a,b){var c=a.__wiz;c||(c=a.__wiz={});return c[b.toString()]};/*

 Copyright 2024 Google, Inc
 SPDX-License-Identifier: MIT
*/
var Im={};var Jm={};function Km(a){var b=document.body,c=Ja(b.getAttribute("jsaction")||"");var d=["u0pjoe"];for(var e=v(d),f=e.next();!f.done;f=e.next()){f=f.value;var g;if(g=c){var h=Im[g];h?g=!!h[f.toString()]:(h=Jm[f.toString()],h||(h=new RegExp("(^\\s*"+f+"\\s*:|[\\s;]"+f+"\\s*:)"),Jm[f.toString()]=h),g=h.test(g))}else g=!1;g||(c&&!/;$/.test(c)&&(c+=";"),c+=f+":.CLIENT",Lm(b,c));(g=Hm(b,f))?g.push(a):b.__wiz[f.toString()]=[a]}return{et:d,hb:a,el:b}}
function Lm(a,b){a.setAttribute("jsaction",b);"__jsaction"in a&&delete a.__jsaction};function Mm(a){X.call(this);this.j=a}z(Mm,X);Mm.prototype.g=function(a){return Nm(this,a)};function Om(a,b){a=Object.prototype.hasOwnProperty.call(a,za)&&a[za]||(a[za]=++Aa);return(b?"__wrapper_":"__protected_")+a+"__"}function Nm(a,b){var c=Om(a,!0);b[c]||((b[c]=Pm(a,b))[Om(a,!1)]=b);return b[c]}function Pm(a,b){function c(){if(a.Ga())return b.apply(this,arguments);try{return b.apply(this,arguments)}catch(d){Qm(a,d)}}c[Om(a,!1)]=b;return c}
function Qm(a,b){if(!(b&&typeof b==="object"&&typeof b.message==="string"&&b.message.indexOf("Error in protected function: ")==0||typeof b==="string"&&b.indexOf("Error in protected function: ")==0))throw a.j(b),new Rm(b);}function Sm(a){var b=b||y.window||y.globalThis;"onunhandledrejection"in b&&(b.onunhandledrejection=function(c){Qm(a,c&&c.reason?c.reason:Error("uncaught error"))})}
function Tm(a,b){var c=y.window||y.globalThis,d=c[b];if(!d)throw Error(b+" not on global?");c[b]=function(e,f){typeof e==="string"&&(e=Ea(Fa,e));e&&(arguments[0]=e=Nm(a,e));if(d.apply)return d.apply(this,arguments);var g=e;if(arguments.length>2){var h=Array.prototype.slice.call(arguments,2);g=function(){e.apply(this,h)}}return d(g,f)};c[b][Om(a,!1)]=d}
Mm.prototype.M=function(){var a=y.window||y.globalThis;var b=a.setTimeout;b=b[Om(this,!1)]||b;a.setTimeout=b;b=a.setInterval;b=b[Om(this,!1)]||b;a.setInterval=b;Mm.aa.M.call(this)};function Rm(a){Ha.call(this,"Error in protected function: "+(a&&a.message?String(a.message):String(a)),a);(a=a&&a.stack)&&typeof a==="string"&&(this.stack=a)}z(Rm,Ha);function Um(){};var Vm;function Wm(){}z(Wm,Um);Wm.prototype.j=function(){return new XMLHttpRequest};Vm=new Wm;function Xm(a){Ul.call(this);this.headers=new Map;this.U=a||null;this.l=!1;this.g=null;this.N="";this.C=0;this.o=this.L=this.G=this.J=!1;this.B=null;this.P="";this.S=!1}z(Xm,Ul);var Ym=/^https?$/i,Zm=["POST","PUT"],$m=[];q=Xm.prototype;q.ib=function(){this.dispose();ab($m,this)};
q.send=function(a,b,c,d){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.N+"; newUri="+a);b=b?b.toUpperCase():"GET";this.N=a;this.C=0;this.J=!1;this.l=!0;this.g=this.U?this.U.j():Vm.j();this.g.onreadystatechange=li(Da(this.Ta,this));try{this.L=!0,this.g.open(b,String(a),!0),this.L=!1}catch(g){an(this);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&typeof d.get===
"function"){e=v(d.keys());for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("Unknown input type for opt_headers: "+String(d));d=Array.from(c.keys()).find(function(g){return"content-type"==g.toLowerCase()});e=y.FormData&&a instanceof y.FormData;!(Za(Zm,b)>=0)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=v(c);for(d=b.next();!d.done;d=b.next())c=v(d.value),d=c.next().value,c=c.next().value,this.g.setRequestHeader(d,c);this.P&&(this.g.responseType=
this.P);"withCredentials"in this.g&&this.g.withCredentials!==this.S&&(this.g.withCredentials=this.S);try{this.B&&(clearTimeout(this.B),this.B=null),this.G=!0,this.g.send(a),this.G=!1}catch(g){an(this)}};function an(a){a.l=!1;a.g&&(a.o=!0,a.g.abort(),a.o=!1);a.C=5;bn(a);cn(a)}function bn(a){a.J||(a.J=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))}
q.abort=function(a){this.g&&this.l&&(this.l=!1,this.o=!0,this.g.abort(),this.o=!1,this.C=a||7,this.dispatchEvent("complete"),this.dispatchEvent("abort"),cn(this))};q.M=function(){this.g&&(this.l&&(this.l=!1,this.o=!0,this.g.abort(),this.o=!1),cn(this,!0));Xm.aa.M.call(this)};q.Ta=function(){this.Ga()||(this.L||this.G||this.o?dn(this):this.Ja())};q.Ja=function(){dn(this)};
function dn(a){if(a.l&&typeof ua!="undefined")if(a.G&&(a.g?a.g.readyState:0)==4)setTimeout(a.Ta.bind(a),0);else if(a.dispatchEvent("readystatechange"),(a.g?a.g.readyState:0)==4){a.l=!1;try{jm(a)?(a.dispatchEvent("complete"),a.dispatchEvent("success")):(a.C=6,bn(a))}finally{cn(a)}}}function cn(a,b){if(a.g){a.B&&(clearTimeout(a.B),a.B=null);var c=a.g;a.g=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=null}catch(d){}}}q.isActive=function(){return!!this.g};
function jm(a){var b=im(a);a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}if(!c){if(b=b===0)a=String(a.N).match(bi)[1]||null,!a&&y.self&&y.self.location&&(a=y.self.location.protocol.slice(0,-1)),b=!Ym.test(a?a.toLowerCase():"");c=b}return c}function im(a){try{return(a.g?a.g.readyState:0)>2?a.g.status:-1}catch(b){return-1}}ri(function(a){Xm.prototype.Ja=a(Xm.prototype.Ja)});function en(a,b,c){Ul.call(this);this.B=b||null;this.o={};this.C=fn;this.J=a;if(!c){this.g=null;this.g=new Mm(Da(this.l,this));Tm(this.g,"setTimeout");Tm(this.g,"setInterval");a=this.g;b=y.window||y.globalThis;c=["requestAnimationFrame","mozRequestAnimationFrame","webkitAnimationFrame","msRequestAnimationFrame"];for(var d=0;d<c.length;d++){var e=c[d];c[d]in b&&Tm(a,e)}a=this.g;qi=!0;b=Da(a.g,a);for(c=0;c<oi.length;c++)oi[c](b);pi.push(a)}}z(en,Ul);
function gn(a,b){pl.call(this,"c");this.error=a;this.da=b}z(gn,pl);function hn(a,b){return new en(a,b,void 0)}function fn(a,b,c,d){if(d instanceof Map){var e={};d=v(d);for(var f=d.next();!f.done;f=d.next()){var g=v(f.value);f=g.next().value;g=g.next().value;e[f]=g}}else e=d;d=new Xm;$m.push(d);d.j.add("ready",d.ib,!0,void 0,void 0);d.send(a,b,c,e)}function jn(a,b){a.C=b}
en.prototype.l=function(a,b){a=a.error||a;b=b?Sk(b):{};a instanceof Error&&Uk(b,mb(a));var c=Lh(a);if(this.B)try{this.B(c,b)}catch(w){}var d=c.message.substring(0,1900);if(!(a instanceof Ha)||a.g){var e=c.fileName,f=c.lineNumber;a=c.stack;try{var g=hi(this.J,"script",e,"error",d,"line",f);a:{for(var h in this.o){var l=!1;break a}l=!0}if(!l){l=g;var m=gi(this.o);g=di(l,m)}m={};m.trace=a;if(b)for(var p in b)m["context."+p]=b[p];var r=gi(m);this.C(g,"POST",r,this.G)}catch(w){}}try{this.dispatchEvent(new gn(c,
b))}catch(w){}};en.prototype.M=function(){ii(this.g);en.aa.M.call(this)};function kn(){this.g=Date.now()}var ln=null;kn.prototype.set=function(a){this.g=a};kn.prototype.reset=function(){this.set(Date.now())};kn.prototype.get=n("g");function mn(a){this.o=a||"";ln||(ln=new kn);this.A=ln}mn.prototype.g=!0;mn.prototype.j=!0;mn.prototype.l=!1;function nn(a){return a<10?"0"+a:String(a)}function on(a){mn.call(this,a)}z(on,mn);
function pn(a,b){var c=[];c.push(a.o," ");if(a.j){var d=c.push,e=new Date(b.l());d.call(c,"[",nn(e.getFullYear()-2E3)+nn(e.getMonth()+1)+nn(e.getDate())+" "+nn(e.getHours())+":"+nn(e.getMinutes())+":"+nn(e.getSeconds())+"."+nn(Math.floor(e.getMilliseconds()/10)),"] ")}d=c.push;e=a.A.get();e=(b.l()-e)/1E3;var f=e.toFixed(3),g=0;if(e<1)g=2;else for(;e<100;)g++,e*=10;for(;g-- >0;)f=" "+f;d.call(c,"[",f,"s] ");c.push("[",b.j(),"] ");c.push(b.getMessage());a.l&&(b=b.g(),b!==void 0&&c.push("\n",b instanceof
Error?b.message:String(b)));a.g&&c.push("\n");return c.join("")};function qn(a){a=a===void 0?new rn:a;Ul.call(this);var b=this;this.S={};this.g=null;this.l={};this.N=new bm(this);this.lb=a.A;this.U=a.J;this.Za=a.C;this.jb=a.o;this.ab=a.L;var c=a.j;this.Xa=new Cm(a.G);this.gb=a.N;this.ba=new ol;var d=new Xm;sn(this,c);this.G=new km(d,c,void 0,void 0,void 0);ki(this,this.G);this.o=a.g?a.g:yj(c,"docs-sup")+yj(c,"docs-jepp")+"/jserror";if(d=yj(c,"jobset"))this.o=hi(this.o,"jobset",d);if(d=yj(c,"docs-ci"))this.o=hi(this.o,"id",d);d=yj(c,"docs-pid");vj(c.get("docs-eaotx"))&&
d&&(this.o=hi(this.o,"ouid",d));this.la=xj(c,"docs-srmoe")||0;this.eb=vj(c.get("docs-oesf"));this.qa=xj(c,"docs-srmour")||0;this.fb=vj(c.get("docs-oursf"));d=this.qa>0&&Math.random()<this.qa;this.bb=vj(c.get("docs-wesf"));this.ra=xj(c,"docs-srmwe")||0;tn(this);$i=function(g){return un(b,g,"promise rejection")};var e=xj(c,"docs-srmdue")||0;if(e>0&&Math.random()<e){var f=vj(c.get("docs-duesf"));gj=function(g){un(b,g,"deferred error",f,"isDeferredUnhandledErrback")}}else gj=k();xj(c,"docs-srmxue");c.get("docs-xduesf");
d&&(d=new Mm(function(g){var h={};h=(h.isUnhandledRejection="true",h);b.fb?vn(b,g,h):b.info(g,h)}),Sm(d),ki(this,d));this.L=null;this.ra>0&&Math.random()<this.ra&&document&&document.body&&(this.L=Km(function(g){var h={};h=(h.isWizError="true",h);g=v(g.data.errors);for(var l=g.next();!l.done;l=g.next())l=l.value.error,b.bb?vn(b,l,h):b.info(l,h)}));this.P=a.l;this.C=!1;this.J=!0;this.B=!1;this.ka=yj(c,"docs-jern");this.Ya=a.B;this.Wa=a.F.concat(Object.values(Dh))}u(qn,Ul);
function tn(a){var b=b===void 0?!1:b;if(wn){if(xn!=null)throw Error('ErrorReporter already installed. at "'+xn.stack+'"');throw Error("ErrorReporter already installed.");}wn=!0;xn=Error();a.g=hn(a.o,function(e,f){return yn(a,e,f)});var c={};a.Za&&(c["X-No-Abort"]="1");a.g.G=c;jn(a.g,function(e,f,g,h){a.J&&a.G.send(e,f,g,h)});if(a.la>0&&Math.random()<a.la){c={};var d=(c.isWindowOnError="true",c);a.eb?Kh(function(e){vn(a,e.error instanceof Error?e.error:Error(e.message),d)}):Kh(function(e){a.log(e.error instanceof
Error?e.error:Error(e.message),d)})}a.N.listen(a.g,"c",function(e){e.da.severity=e.da["severity-unprefixed"]||e.da.severity;var f=e.da.severity;(f=f=="fatal"||f=="postmortem")&&!a.jb&&(!a.lb||(b===void 0?0:b)?a.ba.notify(void 0,e.da):a.ba.notify(e,e.da));a.dispatchEvent(new Pl(f?"a":"b",e.error,e.da))})}function sn(a,b){b=new mm(b);var c=b.g,d;for(d in c){var e=c[d];e&&(a.l["expflag-"+d]=e.toString())}a.l.experimentIds=b.j.join(",")}
function vn(a,b,c){a.B=!1;zn(b,"fatal");if(!a.g){if(b instanceof mh)throw b.g;throw Rh(b);}a.g.l(b,An(a,b,c));if(a.ab){c=An(a,b,c);c.is_forceFatal=1;var d=b instanceof mh?b.g:b;yn(a,d,c);b=Rh(d);a=", context:"+JSON.stringify(An(a,d,c));b.message+=a;throw b;}}function Bn(a,b,c){a.B=!1;zn(b,"warning");a.g&&a.g.l(b,An(a,b,c))}qn.prototype.info=function(a,b,c){this.B=c||!1;zn(a,"incident");this.g&&this.g.l(a,An(this,a,b))};
qn.prototype.log=function(a,b,c){this.B=!!c;zn(a,"incident");this.g&&this.g.l(a,An(this,a,b))};
function un(a,b,c,d,e){d=d===void 0?!0:d;if(b&&typeof b==="object"&&b.type==="error"){var f=b.error;b=JSON.stringify({error:f&&f.message?f.message:"Missing error cause.",stack:f&&f.stack?f.stack:"Missing error cause.",message:b.message,filename:b.filename,lineno:b.lineno,colno:b.colno,type:b.type});c=Error("Unhandled "+c+" with ErrorEvent: "+b)}else c=typeof b==="string"?Error("Unhandled "+c+" with: "+b):b==null?Error("Unhandled "+c+' with "null/undefined"'):b;b={};e&&(b[e]="true");d?Ia(c):a.info(c,
b)}function Cn(a,b,c){return function(){a:{var d=ra.apply(0,arguments);if(a.g){try{var e=b.apply(c,d);break a}catch(f){vn(a,f)}e=void 0}else e=b.apply(c,d)}return e}}function Dn(a,b){a.g&&b.then(void 0,function(c){vn(a,c instanceof Error?c:Error(c))});return b}function An(a,b,c){b instanceof mh&&(b=b.g);c=c?Sk(c):{};c.severity=mb(b).severity;a.U&&(c.errorGroupId=a.U);return c}
function yn(a,b,c){var d=a.C;try{a.ea(b,c)}catch(f){throw d&&!a.P&&(a.J=!1),a.C=!0,c.provideLogDataError=f.message,c.severity||(c.severity="fatal"),Rh(f);}finally{if(c["severity-unprefixed"]=c.severity||"fatal",c.severity=""+c["severity-unprefixed"],!a.Ya)for(var e in c)typeof c[e]==="number"||c[e]instanceof Number||typeof c[e]==="boolean"||c[e]instanceof Boolean||a.Wa.includes(e)||e in c&&delete c[e]}}
qn.prototype.ea=function(a,b){for(var c in this.S)try{b[c]=this.S[c](a)}catch(g){}Uk(b,this.l);if((ai(),0)>0){var d=new on,e="";$h(function(g){e+=pn(d,g)});b.clientLog=e}c=b.severity||"fatal";this.gb||(c=Dm(this.Xa,a,c,b));this.ka&&(b.reportName=this.ka+"_"+c);b.isArrayPrototypeIntact=nm().toString();if(!("WorkerGlobalScope"in y&&self instanceof y.WorkerGlobalScope)){try{var f=!!document.getElementById("docs-editor")}catch(g){f=!1}b.isEditorElementAttached=f.toString()}b.documentCharacterSet=document.characterSet;
f=a.stack||"";if(f.trim().length==0||f=="Not available")b["stacklessError-reportingStack"]=Qh(qn.prototype.ea),[a.message].concat(oa(Object.keys(b)),oa(Object.values(b))).some(function(g){return g&&g.includes("<eye3")})||(b.eye3Hint="<eye3-stackless title='Stackless JS Error - "+a.name+"'/>");this.C&&!this.P?(this.J=this.B,c=="fatal"?c="postmortem":c=="incident"&&(c="warningafterdeath")):c=="fatal"&&(this.C=!0);this.B=!1;b.severity=c};
qn.prototype.M=function(){wn=!1;if(this.L)for(var a=this.L,b=v(a.et),c=b.next();!c.done;c=b.next()){c=c.value;var d=Hm(a.el,c);if(d&&(ab(d,a.hb),!d.length)){d=a.el;var e=Ja(d.getAttribute("jsaction")||"");c+=":.CLIENT";e=e.replace(c+";","");e=e.replace(c,"");Lm(d,e)}}ji(this.N,this.g,this.G);Ul.prototype.M.call(this)};var wn=!1,xn=null;function rn(){this.J=this.j=void 0;this.o=this.L=this.A=!1;this.g=void 0;this.C=this.l=!1;this.B=!0;this.F=[];this.N=!1;this.G=[]}
function zn(a,b){a instanceof mh&&(a=a.g);lb(a,"severity",b)};function En(a){this.g=null;this.j=a<1;this.l=a<.01}function Fn(a,b,c){c=c===void 0?{}:c;a.l&&(c.sampling_samplePercentage=(.01).toString(),a.g.info(b,c))}function Gn(a,b,c){c=c===void 0?{}:c;a.j&&(c.sampling_samplePercentage=(1).toString(),Bn(a.g,b,c))};function Hn(a){this.D=E(a)}u(Hn,H);Hn.prototype.getMessage=function(){return kd(this,1)};function In(a){this.D=E(a)}u(In,H);function Jn(a){this.D=E(a)}u(Jn,H);function Kn(a){this.D=E(a)}u(Kn,H);function Ln(a,b){return od(a,b)}Kn.prototype.Qa=function(){return G(this,In,3)};Kn.prototype.Ea=function(){return G(this,Hn,5)};function Mn(a){this.D=E(a)}u(Mn,H);function Nn(a){var b=new Mn;return od(b,a)}Mn.prototype.Ea=function(){return G(this,Hn,3)};Mn.prototype.Qa=function(){return G(this,In,4)};function On(a){var b=Oi();chrome.runtime.sendMessage(Gc(a),void 0,function(c){return Pn(b,function(d){return new Mn(d)},c)});return b.promise.catch(function(c){c=Ph(c);lb(c,"offscreenDocumentRequestType",ld(a,1).toString());throw c;})}
function Pn(a,b,c){var d=chrome.runtime;c!==void 0?(d=b(c),d.Ea()?(b=a.reject,c=Error,d=d.Ea(),d=md(d,1),b.call(a,c("Error from Offscreen page:"+d))):a.resolve(d)):a.reject(Error("No response from Offscreen page:"+(d.lastError?d.lastError.message:"without lastError")))};function Qn(){return Rn(chrome.storage.local,["optedInUserOuid"]).then(function(a){return a.optedInUserOuid||null})}function Sn(a){return Tn({offlineOptedIn:!0}).then(function(){if(a){var b={};return Tn((b.optedInUserOuid=a,b))}})}function Un(){return Tn({offlineOptedIn:!1}).then(function(){return Vn()})}
function Wn(){return Rn(chrome.storage.local,["offlineOptedIn"]).then(function(a){a=a.offlineOptedIn;switch(a){case void 0:return"unknown";case !0:return"opted_in";case !1:return"opted_out";default:throw Error("Cannot handle opt in value "+a);}})}function Mi(){return Rn(chrome.storage.managed,["allowedDocsOfflineDomains"]).then(function(a){return a&&a.allowedDocsOfflineDomains?a.allowedDocsOfflineDomains:[]})}
function Ni(){return Rn(chrome.storage.managed,["autoEnabledDocsOfflineDomains"]).then(function(a){return a&&a.autoEnabledDocsOfflineDomains?a.autoEnabledDocsOfflineDomains:[]})}function Rn(a,b){return new Y(function(c,d){a.get(b,function(e){chrome.runtime.lastError?d(Error(chrome.runtime.lastError)):c(e)})})}function Tn(a){return new Y(function(b,c){chrome.storage.local.set(a,function(){chrome.runtime.lastError?c(Error(chrome.runtime.lastError)):b()})})}
function Vn(){return new Y(function(a,b){chrome.storage.local.remove("optedInUserOuid",function(){chrome.runtime.lastError?b(Error(chrome.runtime.lastError)):a()})})}function Xn(){return Rn(chrome.storage.local,["lastSuccessfulFrameConnectTime"]).then(function(a){return a.lastSuccessfulFrameConnectTime||null})};function Yn(a){this.D=E(a)}u(Yn,H);function Zn(a){this.D=E(a)}u(Zn,H);function $n(a){this.D=E(a)}u($n,H);function ao(a){this.D=E(a)}u(ao,H);function bo(a){this.D=E(a)}u(bo,H);function co(a){this.D=E(a)}u(co,H);function eo(a){var b=new co;return od(b,a)}function fo(a,b){return gd(a,ao,5,b)};function go(a,b,c){X.call(this);this.B=null;this.J=a;this.C=b;this.o=c;this.j=Oi();this.l=!1;a=new Vk;Yk(a,"offscreendocument.html");il(a,"randomPercentageForSampling",this.o);il(a,"sessionId",this.C);this.G={url:a.toString(),reasons:["IFRAME_SCRIPTING"],justification:"Use iframe to access user data under docs.google.com domain"};this.g=new En(this.o)}u(go,X);function ho(a,b){a.B=b}
function io(a){return jo().then(function(b){return b?On(eo(5)).then(function(){return chrome.offscreen.closeDocument()}):Promise.resolve()}).then(function(){ko(a)})}function lo(a,b){return mo(no(a,oo(a,6,b)))}function po(a,b){b=oo(a,1,b);return mo(no(a,b))}function qo(a,b){return jo().then(function(c){b[ro(0)]=c.toString();return c?Promise.resolve():so(a)})}
function so(a){return chrome.offscreen.createDocument(a.G).catch(function(b){if(b instanceof Error&&b.message.includes("Only a single offscreen document may be created"))Fn(a.g,b);else return Promise.reject(b)})}function to(a,b){return chrome.offscreen.closeDocument().catch(function(c){c=c instanceof Error?c.message:c.toString();Fn(a.g,Error(c),b);b.errorWhenForceCloseOffscreenDoc=c}).then(function(){return so(a)})}
function oo(a,b,c){b=eo(b);var d=new Yn;c=nd(d,1,c);c=nd(c,2,a.B.toString());a=nd(c,3,a.J);a=nd(a,4,"opted_in");return gd(b,Yn,2,a)}
function uo(a,b){var c={sendingFrameRequestType:ld(b,1)},d=fo(eo(4),b);return mo(vo(a,d,c,0)).catch(function(e){if(e instanceof Error){c.offlineFrameConnected_afterFirstError=a.l;if(wo(e.message))return Fn(a.g,e,c),new Promise(function(f){return setTimeout(function(){return f(vo(a,d,c,1))},2E3)});if("Requests cancelled because user has been opted out"==e.message)return Promise.resolve(new In)}return Promise.reject(e instanceof Error?e:Error(e))})}
function vo(a,b,c,d){return a.j.promise.then(function(){return jo()}).then(function(e){c[ro(d)]=e.toString();return e?Hi():xo(a)}).then(function(){return a.j.promise}).then(function(){return On(b)}).then(function(e){return e.Qa()}).ca(function(e){return yo(e,c)})}function xo(a){ko(a);return Wn().then(function(b){return b=="opted_in"?Qn().then(function(c){return po(a,c)}):Ii()})}
function no(a,b){ld(b,1)==6||ld(b,1);var c={sendingOffscreenDocumentRequestType:ld(b,1).toString()};return Hi(qo(a,c)).then(function(){return Xl()}).then(function(){return On(b)}).ca(function(d){return d instanceof Error&&wo(d.message)?(Fn(a.g,d,c),Hi(jo()).then(function(e){c[ro(1)]=e.toString()}).then(function(){return to(a,c)}).then(function(){return Xl()}).then(function(){return On(b)})):Promise.reject(d instanceof Error?d:Error(d))}).ca(function(d){return yo(d,c)})}
function wo(a){return a.includes("Could not establish connection. Receiving end does not exist.")||a.includes("The message port closed before a response was received.")||a.includes("A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received")}function jo(){return self.clients.matchAll().then(function(a){return a.some(function(b){return b.url.includes(chrome.runtime.getURL("offscreendocument.html"))})})}
go.prototype.M=function(){io(this);X.prototype.M.call(this)};function mo(a){return Promise.resolve(a)}function ko(a){a.j=Oi();a.l=!1}function ro(a){switch(a){case 0:return"hasDocument_beforeCreatingOffscreenDoc_0";case 1:return"hasDocument_beforeCreatingOffscreenDoc_1";default:throw Error("Cannot get error context key with retryAttempt "+a);}}
function yo(a,b){a=Ph(a);b=v(Object.entries(b));for(var c=b.next();!c.done;c=b.next()){var d=v(c.value);c=d.next().value;d=d.next().value;lb(a,c,d)}throw a;};function zo(a){this.D=E(a)}u(zo,H);function Ao(a){this.D=E(a)}u(Ao,H);function Bo(a){this.D=E(a)}u(Bo,H);function Co(){Da(this.l,this);this.g=new on;this.g.j=!1;this.g.l=!1;this.j=this.g.g=!1;this.o={}}function Do(a){1!=a.j&&(a.j=!0)}Co.prototype.l=function(a){function b(f){if(f){if(f.value>=Vh.value)return"error";if(f.value>=Wh.value)return"warn";if(f.value>=Xh.value)return"log"}return"debug"}if(!this.o[a.j()]){var c=pn(this.g,a),d=Eo;if(d){var e=b(a.o());Fo(d,e,c,a.g())}}};var Eo=y.console;function Fo(a,b,c,d){if(a[b])a[b](c,d===void 0?"":d);else a.log(c,d===void 0?"":d)};function Go(a){this.o=a.Cb||null;this.l=a.wc||!1;this.g=void 0}z(Go,Um);Go.prototype.j=function(){var a=new Ho(this.o,this.l);this.g&&(a.G=this.g);return a};function Ho(a,b){Ul.call(this);this.ea=a;this.L=b;this.G=void 0;this.status=this.readyState=0;this.responseType=this.o=this.l=this.statusText="";this.onreadystatechange=null;this.P=new Headers;this.B=null;this.U="GET";this.ba="";this.g=!1;this.S=this.C=this.J=null;this.N=new AbortController}z(Ho,Ul);q=Ho.prototype;
q.open=function(a,b){if(this.readyState!=0)throw this.abort(),Error("Error reopening a connection");this.U=a;this.ba=b;this.readyState=1;Io(this)};q.send=function(a){if(this.readyState!=1)throw this.abort(),Error("need to call open() first. ");if(this.N.signal.aborted)throw this.abort(),Error("Request was aborted.");this.g=!0;var b={headers:this.P,method:this.U,credentials:this.G,cache:void 0,signal:this.N.signal};a&&(b.body=a);(this.ea||y).fetch(new Request(this.ba,b)).then(this.qb.bind(this),this.wa.bind(this))};
q.abort=function(){this.l=this.o="";this.P=new Headers;this.status=0;this.N.abort();this.C&&this.C.cancel("Request was aborted.").catch(k());this.readyState>=1&&this.g&&this.readyState!=4&&(this.g=!1,Jo(this));this.readyState=0};
q.qb=function(a){if(this.g&&(this.J=a,this.B||(this.status=this.J.status,this.statusText=this.J.statusText,this.B=a.headers,this.readyState=2,Io(this)),this.g&&(this.readyState=3,Io(this),this.g)))if(this.responseType==="arraybuffer")a.arrayBuffer().then(this.ob.bind(this),this.wa.bind(this));else if(typeof y.ReadableStream!=="undefined"&&"body"in a){this.C=a.body.getReader();if(this.L){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.l=
[]}else this.l=this.o="",this.S=new TextDecoder;Ko(this)}else a.text().then(this.pb.bind(this),this.wa.bind(this))};function Ko(a){a.C.read().then(a.nb.bind(a)).catch(a.wa.bind(a))}q.nb=function(a){if(this.g){if(this.L&&a.value)this.l.push(a.value);else if(!this.L){var b=a.value?a.value:new Uint8Array(0);if(b=this.S.decode(b,{stream:!a.done}))this.l=this.o+=b}a.done?Jo(this):Io(this);this.readyState==3&&Ko(this)}};q.pb=function(a){this.g&&(this.l=this.o=a,Jo(this))};
q.ob=function(a){this.g&&(this.l=a,Jo(this))};q.wa=function(){this.g&&Jo(this)};function Jo(a){a.readyState=4;a.J=null;a.C=null;a.S=null;Io(a)}q.setRequestHeader=function(a,b){this.P.append(a,b)};q.getResponseHeader=function(a){return this.B?this.B.get(a.toLowerCase())||"":""};q.getAllResponseHeaders=function(){if(!this.B)return"";for(var a=[],b=this.B.entries(),c=b.next();!c.done;)c=c.value,a.push(c[0]+": "+c[1]),c=b.next();return a.join("\r\n")};
function Io(a){a.onreadystatechange&&a.onreadystatechange.call(a)}Object.defineProperty(Ho.prototype,"withCredentials",{get:function(){return this.G==="include"},set:function(a){this.G=a?"include":"same-origin"}});var Lo=new Go({Cb:self});Lo.g="same-origin";Vm=Lo;
function Mo(){X.call(this);var a=this;this.G=ph();this.g=this.B=null;this.N=!1;this.W=new Co;Do(this.W);this.L=Oi();chrome.alarms.onAlarm.addListener(function(b){return a.L.promise.then(function(){return Dn(a.g,Cn(a.g,a.tb,a)(b))})});chrome.runtime.onMessageExternal.addListener(function(b,c,d){return No(a,b,d)});chrome.runtime.onMessage.addListener(this.ub.bind(this));this.C=new bm(this);ki(this,this.C);this.C.listen(y,"message",this.wb);this.o=Math.random()*100;this.J=this.o<1;this.l=new En(this.o);
this.j=new go(Oo(),this.G,this.o);chrome.runtime.onConnectExternal.addListener(k());Wl(this.zb,252E5,this)}u(Mo,X);q=Mo.prototype;q.load=function(){var a=this;this.B="docs.google.com";return Tn({docsDomain:this.B}).then(function(){a.g=Po(a);a.L.resolve();ki(a,a.g);a.l.g=a.g;a.j.g.g=a.g;ho(a.j,Qo(a));ki(a,a.j);var b=Cn(a.g,a.rb,a),c=Dn(a.g,Hi().then(function(){return b()}));return Hi(c)}).ca(function(b){Ph(b)})};
function Ro(a,b,c){return il(il(Yk(Qo(a),"/offline/extension/report"),"v",c),"optin",b).toString()}q.zb=function(){chrome.alarms.create("open",{delayInMinutes:1});Fn(this.l,Error("Called unsafeClose_"))};function So(a){return new Y(function(b){chrome.alarms.get("heartbeat",function(c){c||(chrome.alarms.create("heartbeat",{periodInMinutes:5}),To(a,"heartbeat"));b()})})}function Uo(){return new Y(function(a){chrome.alarms.clear("heartbeat",function(){a()})})}
q.rb=function(){var a=this;return Xn().then(function(b){a.g.l.lastSuccessfulFrameConnectTime=(b==null?void 0:b.toString())||"null"}).then(function(){return Wn()}).then(function(b){var c=Oo();a.g.l.extensionVersion=c;a.g.l.optInStatus=String(b);Vo(a,String(b),c);switch(b){case "unknown":break;case "opted_in":return Qn().then(function(d){return po(a.j,d)});case "opted_out":break;default:throw Error("Could not handle opt in status "+b);}})};
function Vo(a,b,c){a.J&&(b=Ro(a,b,c),y.fetch(new Request(b,{method:"post",mode:"cors"})).then(k()).catch(function(d){Bn(a.g,Ph(d))}))}q.wb=function(a){var b=a.g;b&&b.data&&b.ports&&b.ports.length?(a=new Bo(b.data),Wo(this,a,b.ports.length>1?b.ports[1]:void 0).then(function(c){b.ports[0].postMessage(Gc(c))})):Gn(this.l,Error("Dropped invalid event."),{event:String(a)})};
function No(a,b,c){var d=new Bo(b);Wo(a,d).then(function(e){c(Gc(e))}).ca(function(e){if(e instanceof Error&&e.message=="Attempting to use a disconnected port object")Gn(a.l,Error("Failed to reply to request because listen port was disconnected."),{requestType:qc(F(d,1,void 0,Sc))});else throw e;});return!0}
q.ub=function(a,b,c){var d=this;a=new co(a);switch(qc(F(a,1,void 0,Sc))){case 3:var e=G(a,Zn,4);a=wc(F(e,1))!=null?kd(e,1):null;var f=md(e,2);Sn(a).then(function(){return Tn({lastSuccessfulFrameConnectTime:parseInt(f,10)})}).then(function(){var h=d.j;h.l=!0;h.j.resolve()}).then(function(){var h=Nn(3);c(Gc(h))});break;case 7:var g=Nn(7);(a=(e=G(a,bo,6))==null?void 0:kd(e,1))?Xo(this,a).then(function(){return c(Gc(g))}):Yo(this).then(function(){return c(Gc(g))});break;default:throw Error("Unsupported OffscreenDocumentRequestType.");
}return!0};function Wo(a,b,c){return Hi().then(a.xb.bind(a,b,c)).ca(function(d){d=d instanceof Error?d:Error(d);var e=new Kn,f=new Hn;gd(e,Hn,5,f);nd(f,1,d.message);return e})}
q.xb=function(a){var b=this,c=Ln(new Kn,qc(F(a,1)));switch(qc(F(a,1,void 0,Sc))){case 1:return(a=(a=G(a,Zn,7))?md(a,1):null)||Fn(this.l,Error("Scheduler frame connect request sent without an ouid.")),Sn(a).then(function(){return Tn({lastSuccessfulFrameConnectTime:Date.now()})}).then(function(){var e=b.j;e.l=!0;e.j.resolve()}).then(function(){return c});case 2:var d=(a=G(a,zo,8))?md(a,1):null;return Sn(d).then(function(){return d?d:Qn()}).then(function(e){return lo(b.j,e).then(function(){return So(b)})}).then(function(){return c});
case 3:return(a=G(a,bo,3))&&kd(a,1)?(a=kd(a,1),Xo(this,a).then(function(){return c})):Yo(this).then(function(){return c});case 5:return Zo(G(a,Ao,5)).then(function(e){gd(c,Jn,4,e);return c});case 4:return a=G(a,ao,4),Hi(uo(this.j,a)).then(function(e){gd(c,In,3,e);return c})}throw Error("Dropped unknown message "+a);};
function Zo(a){var b=md(a,1);return Li().then(function(c){var d=c[0],e=c[1];c=new Jn;var f=Za(d,b)>=0;d=Za(e,b)>=0;e=f||d;e=e==null?e:nc(e);Uc(c,1,e);Uc(c,2,d==null?d:nc(d));return c})}function Xo(a,b){return a.N?Hi(io(a.j)):(Fn(a.l,Error("Extension frame connected with the wrong OUID.")),a.N=!0,Hi(po(a.j,b)))}function Yo(a){return Un().then(function(){return Uo()}).then(function(){return io(a.j)})}q.tb=function(a){return To(this,a.name)};
function To(a,b){var c=new ao;c=od(c,0);var d=new $n;b=nd(d,1,b);gd(c,$n,2,b);return Hi(uo(a.j,c))}function Po(a){var b=Yk(Qo(a),"/offline/jserror").toString(),c=a.J;a=String(a.G);var d=new rn;d.A=!1;d.o=!0;d.g=b;d.l=!0;b=sj();d.j=b;b=new qn(d);b.l.sessionTypeName="offline-event-page";b.l.reportsNonFatalErrors=String(c);b.l.sid=a;return b}function Qo(a){return Wk(new Vk("//"+a.B),"https")}function Oo(){var a=chrome.runtime.getManifest();return a.version?a.version:"unknown"};self.window=self;(new Mo).load();
