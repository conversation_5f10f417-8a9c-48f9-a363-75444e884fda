{"disable-features": "AsyncSetCookie<FasterSetCookie,AutofillEnableCardBenefitsForBmo<AutofillEnableCardBenefitsForBmo,AutofillFixSplitCreditCardImport<AutofillFixSplitCreditCardImport,AutofillOptimizeFormExtraction<AutofillOptimizeFormExtraction,BeaconLeakageLogging<BeaconLeakageLogging,CompositorLoadingAnimations<CompositorLoadingAnimations,DataSharing<SharedTabGroups,DefaultAllowPrivacySandboxAttestations<PrivacySandboxAttestationsDefaultDeny,DelayLayerTreeViewDeletionOnLocalSwap<RenderDocumentWithNavigationQueueing,GetCookiesOnSet<FasterSetCookie,GlicClosedCaptioning<GlicClosedCaptioning,LocalIpAddressInEvents<LocalIpAddressInEvents,LocalWebApprovals<LocalWebApprovalsLinuxMacWindows,MediaRecorderSeekableWebm<MediaRecorderSeekableWebmKillSwitch,OmniboxDocumentProviderEnterpriseEligibilityWhenUnknown<OmniboxDriveEligibility,PdfInfoBar<PdfInfoBar,PdfOopif<PdfOutOfProcessIframe,RenderDocumentCompositorReuse<RenderDocumentWithNavigationQueueing,RendererMainIsNormalThreadTypeForWebRTC<DisableRendererMainIsNormalThreadTypeForWebRTC,ShowSuggestionsOnAutofocus<ShowSuggestionsOnAutofocus,SupervisedUserBlockInterstitialV3<LocalWebApprovalsLinuxMacWindows,TabCaptureInfobarLinks<TabCaptureInfobarLinks,V8Flag_ignition_elide_redundant_tdz_checks<V8IgnitionElideRedundantTdzChecksKillSwitch,V8Flag_managed_zone_memory<V8ManagedZoneMemory,V8Flag_turbo_store_elimination<V8StoreStoreEliminationKillSwitch,VizDisplayCompositor,WebUIJSErrorReportingExtended<DialDownWebUIJSErrorReportingExtended", "enable-features": "*ANGLEPerContextBlobCache<ANGLEPerContextBlobCache,*AccessibilityBlockFlowIterator<AXBlockFlowIterator,AiSettingsPageEnterpriseDisabledUi<AiSettingsPageEnterpriseDisabledUi,AlwaysBlock3pcsIncognito<AlwaysBlock3pcsIncognito,*AppleKeychainUseSecItem<MacKeychainApiMigration,*AutoPictureInPictureForVideoPlayback<AutoPictureInPictureForVideoPlayback,AutofillAiVoteForFormatStringsFromMultipleFields<AutofillAiVoteForFormatStrings,AutofillAiVoteForFormatStringsFromSingleFields<AutofillAiVoteForFormatStrings,*AutofillDetectFieldVisibility<AutofillDetectFieldVisibility,AutofillEnableCardBenefitsIph<AutofillEnableCardBenefitsIph,*AutofillEnableImportWhenMultiplePhoneNumbers<AutofillRelaxAddressImport,AutofillGreekRegexes<AutofillGreekRegexes,*AutofillImproveCityFieldClassification<AutofillImproveCityFieldClassification,AutofillPopupZOrderSecuritySurface<AutofillPopupZOrderSecuritySurface_V2,*AutofillRelaxAddressImport<AutofillRelaxAddressImport,*AutofillUnifyRationalizationAndSectioningOrder<AutofillUnifyRationalizationAndSectioningOrder,*AvoidDuplicateDelayBeginFrame<AvoidDuplicateDelayBeginFrame,AvoidUnnecessaryBeforeUnloadCheckSync<AvoidUnnecessaryBeforeUnloadCheckSync,BoostRenderProcessForLoading<BoostRenderProcessForLoading,*CacheStorageAblation<CacheStorageAblation,*CameraMicPreview<CameraMicPreview,*Canvas2DHibernation<CanvasHibernationExperiments,*Canvas2DHibernationReleaseTransferMemory<CanvasHibernationExperiments,*CanvasHibernationSnapshotZstd<CanvasHibernationExperiments,CastStreamingVp9<CastStreamingVp9,*ChromeWebStoreNavigationThrottle<ChromeWebStoreNavigationThrottle,*ClearCanvasResourcesInBackground<CanvasHibernationExperiments,*ClientSideDetectionLlamaForcedTriggerInfoForScamDetection<ClientSideDetectionLlamaForcedTriggerInfoForScamDetection,*CookieDeprecationFacilitatedTesting<CookieDeprecationFacilitatedTestingLabelOnly,*DIPS<CookieDeprecationFacilitatedTestingLabelOnly,DataSharingJoinOnly<SharedTabGroups,*DefaultSiteInstanceGroups<DefaultSiteInstanceGroups,DeprecateUnload<DeprecateUnload,DeprecateUnloadByAllowList<DeprecateUnload,*DeviceBoundSessionAccessObserverSharedRemote<DeviceBoundSessionAccessObserverSharedRemote,DirectCompositorThreadIpc<DirectCompositorThreadIpcMacLinuxChromeOS,*DwaFeature<DwaFeature,EnableAsyncUploadAfterVerdict<EnableAsyncUploadAfterVerdict,EnableExtensionsExplicitBrowserSignin<EnableExtensionsExplicitBrowserSignin,*EnableFingerprintingProtectionFilter<FingerprintingProtectionFilter,*EnableHangWatcher<EnableHangWatcher,*EnableHistorySyncOptinExpansionPill<UnoDesktopHistorySyncPillExperiment,EnableICloudKeychainRecoveryFactor<MacICloudKeychainRecoveryFactor,*EnableIpPrivacyProxy<IPProtectionPhase0,EnablePolicyPromotionBanner<EnablePolicyPromotionBanner,*EnableProbabilisticRevealTokens<IPProtectionPhase0,EnhancedFieldsForSecOps<EnhancedFieldsForSecOps,EnhancedSecurityEventFields<EnhancedFieldsForSecOps,EnterpriseFileSystemAccessDeepScan<EnterpriseFileSystemAccessDeepScan,EnterpriseProfileBadgingForAvatar<DefaultProfileEnterpriseBadgingForAvatar,EscapeLtGtInAttributes<EscapeLtGtInAttributes,*EvictionUnlocksResources<CanvasHibernationExperiments,ExpiredHistogramLogic<ExpiredHistograms,ExportFrameTimingAfterFrameDone<ExportFrameTimingAfterFrameDoneExperiment,ExtensionManifestV2Unsupported<ExtensionManifestV2Deprecation,*ExtremeLightweightUAFDetector<ExtremeLightweightUAFDetector,FencedFramesCrossOriginAutomaticBeaconData<FencedFramesEnableCrossOriginAutomaticBeaconData,*FingerprintingProtectionUx<FingerprintingProtectionFilter,*FledgeAuctionDealSupport<ProtectedAudienceDealsSupport,FledgeCacheKAnonHashedKeys<ProtectedAudienceKAnonymityKeyCacheStudy,FledgeConsiderKAnonymity<ProtectedAudiencesKAnonymityEnforcementStudy,FledgeEnforceKAnonymity<ProtectedAudiencesKAnonymityEnforcementStudy,FledgeFacilitatedTestingSignalsHeaders<FledgeFacilitatedTestingSignalsHeaders,FledgeQueryKAnonymity<ProtectedAudiencesKAnonymityEnforcementStudy,*FledgeSellerWorkletThreadPool<ProtectedAudienceMultiThreadedSellerWorklet,*FledgeTrustedSignalsKVv1CreativeScanning<ProtectedAudienceTrustedSignalsKVv1CreativeScanning,FurtherOptimizeParsingDataUrls<SimdutfBase64Support,*GetUserMediaDeferredDeviceSettingsSelection<CameraMicPreview,*HappinessTrackingSurveysForDesktopSettings<SettingSearchExplorationHaTS,IPH_AutofillCreditCardBenefit<AutofillEnableCardBenefitsIph,IncreaseCookieAccessCacheSize<IncreaseCookieAccessCacheSize,LensOverlaySimplifiedSelection<ChromnientSimplifiedSelection,ListAccountsUsesBinaryFormat<ListAccountsUsesBinaryFormat,*LoadingPredictorLimitPreconnectSocketCount<LoadingPredictorLimitPreconnectSocketCount,*MacAccessibilityAPIMigration<MacAccessibilityAPIMigration,*MachPortRendezvousValidatePeerRequirements<UseAdHocSigningForWebAppShims,*MaskedDomainList<IPProtectionPhase0,MaskedDomainListFlatbufferImpl<IPProtectionMdlImpl,*MemoryPurgeOnFreezeLimit<MemoryPurgeOnFreezeLimit,NotificationTelemetry<NotificationTelemetryService,OmniboxDocumentProvider<OmniboxDriveEligibility,OmniboxDocumentProviderEnterpriseEligibility<OmniboxDriveEligibility,OmniboxDocumentProviderNoSyncRequirement<OmniboxDriveEligibility,OmniboxDocumentProviderPrimaryAccountRequirement<OmniboxDriveEligibility,*PartitionAllocFewerMemoryRegions<PartitionAllocFewerMemoryRegionsMac,*PartitionProxyChains<IPProtectionPhase0,PassHistogramSharedMemoryOnLaunch<PassHistogramSharedMemoryOnLaunch,PdfInk2<PdfInkSignatures,PerformanceInterventionNotificationImprovements<PerformanceInterventionAlgorithm,*PermissionsPromptSurvey<CameraMicPreview,PinnedTabToastOnClose<PinnedTabToastOnClose,*PrefetchServiceWorker<PrefetchServiceWorker,*PreloadingNoSamePageFragmentAnchorTracking<PreloadingNoSamePageFragmentAnchorTracking,PrivacyGuideAiSettings<PrivacyGuideAiSettings,PrivacySandboxAdTopicsContentParity<PrivacySandboxPrivacyGuideAdTopics,PrivacySandboxAdsApiUxEnhancements<PrivacySandboxAdsApiUxEnhancements,*PrivacySandboxNoticeQueue<PrivacySandboxNoticeQueue,*ProgrammaticScrollAnimationOverride<ProgrammaticScrollAnimationOverride,*ProgressiveAccessibility<ProgressiveAccessibility,PwaNavigationCapturing<PWANavigationCapturingV2WindowMacLinux,QueueNavigationsWhileWaitingForCommit<RenderDocumentWithNavigationQueueing,QuicDoesNotUseFeatures<QUIC,*ReadAnythingReadAloud<ReadAnythingReadAloudDesktop,RemoveCancelledScriptedIdleTasks<RemoveCancelledScriptedIdleTasks,RenderDocument<RenderDocumentWithNavigationQueueing,RestrictThreadPoolInBackground<UnimportantFramePolicy,ScriptStreamingForNonHTTP<WebUIInProcessResourceLoading,SetIsolatesPriority<UnimportantFramePolicy,*SidePanelResizing<SidePanelResizing,SimdutfBase64Support<SimdutfBase64Support,*SoftNavigationDetectionAdvancedPaintAttribution<SoftNavigationDetectionAdvancedPaintAttribution,*TabGroupShortcuts<TabGroupShortcuts,TabGroupSyncServiceDesktopMigration<TabGroupSyncServiceDesktopMigrationRelaunch,*TaskManagerDesktopRefresh<TaskManagerDesktopRefresh,*TrackingProtectionContentSettingUbControl<IPProtectionPhase0,TrustSafetySentimentSurveyV2<TrustSafetySentimentSurveyV2,UMAPseudoMetricsEffect<UMA-Pseudo-Metrics-Effect-Injection-25-Percent,Ukm<UKM,UkmSamplingRate<UkmSamplingRate,UnimportantFramesPriority<UnimportantFramePolicy,*UseAdHocSigningForWebAppShims<UseAdHocSigningForWebAppShims,UserRemoteCommands<ProfileRemoteCommands,UserRemoteCommandsInvalidationWithDirectMessagesEnabled<ProfileRemoteCommands,UserVisibleProcessPriority<UnimportantFramePolicy,VSyncAlignedPresent<VSyncAlignedPresent,WebContentsDiscard<WebContentsDiscard,WebRtcEncodedTransformDirectCallback<WebRtcEncodedTransformDirectCallback,WebUIInProcessResourceLoading<WebUIInProcessResourceLoading", "force-fieldtrial-params": "AvoidUnnecessaryBeforeUnloadCheckSync.WithSendBeforeUnload_20250715:AvoidUnnecessaryBeforeUnloadCheckSyncMode/WithSendBeforeUnload,BoostRenderProcessForLoading.EnableAll_20250715:prioritize_prerendering/true/prioritize_prerendering_only/false/prioritize_renderer_initiated/true/prioritize_restore/false/target_urls/%5B%5D,DeprecateUnload.Enabled_138_100:allowlist/a%2Ecom%2Cb%2Ecom%2Cc%2Ecom%2Cd%2Ecom%2Ce%2Ecom%2Cf%2Ecom%2Cweb-platform%2Etest%2Cwww1%2Eweb-platform%2Etest%2C127%2E0%2E0%2E1%2Cexample%2Etest%2Cwww%2Egoogle%2Ecom/rollout_percent/0,EnableAsyncUploadAfterVerdict.EnabledLaunch:max_parallel_requests/15,ExpiredHistograms.ExpiryEnabledWithAllowlist:allowlist/OptimizationGuide%2EModelExecutor%2EModelLoadingDuration2%2EGeolocationPermissions%2COptimizationGuide%2EModelExecutor%2EModelLoadingDuration2%2EGeolocationPermissionsV3%2COptimizationGuide%2EModelExecutor%2EModelLoadingDuration2%2ENotificationPermissions%2COptimizationGuide%2EModelExecutor%2EModelLoadingDuration2%2ENotificationPermissionsV3%2CNet%2EQuicMultiPort%2ENumMultiPortFailureWhenPathDegrading%2CNet%2EQuicMultiPort%2ENumMultiPortFailureWhenPathNotDegrading,PWANavigationCapturingV2WindowMacLinux.EnabledSettingOnByDefault20250625:link_capturing_state/reimpl_default_on,PerformanceInterventionAlgorithm.EnabledLessAggressive_Launched:availability/any/event_trigger/name%3Aperformance_intervention_dialog_trigger%3Bcomparator%3A%3C3%3Bwindow%3A1%3Bstorage%3A360/event_used/name%3Aperformance_intervention_dialog_used%3Bcomparator%3Aany%3Bwindow%3A0%3Bstorage%3A360/event_weekly_trigger/name%3Aperformance_intervention_dialog_trigger%3Bcomparator%3A%3C21%3Bwindow%3A7%3Bstorage%3A360/minimum_time_reshow/1h/no_acceptance_back_off/7d/scale_max_times_per_day/3/scale_max_times_per_week/21/session_rate/any/session_rate_impact/none/window_size/10,QUIC.ControlOriginFrameJul2025:channel/F/epoch/30000000/retransmittable_on_wire_timeout_milliseconds/200,RenderDocumentWithNavigationQueueing.Default_20250311:level/subframe/queueing_level/full,SharedTabGroups.JoinOnlyEnabled_20240506:show_send_feedback/true,TrustSafetySentimentSurveyV2.EnabledLaunched:browsing-data-probability/0%2E006/browsing-data-trigger-id/1iSgej9Tq0ugnJ3q1cK0QwXZ12oo/control-group-probability/0%2E000025/control-group-trigger-id/CXMbsBddw0ugnJ3q1cK0QJM1Hu8m/download-warning-ui-probability/0%2E05213384/download-warning-ui-trigger-id/7SS4sg4oR0ugnJ3q1cK0TNvCvd8U/max-time-to-prompt/60m/min-session-time/30s/min-time-to-prompt/2m/ntp-visits-max-range/4/ntp-visits-min-range/2/password-check-probability/0%2E195/password-check-trigger-id/Xd54YDVNJ0ugnJ3q1cK0UYBRruNH/password-protection-ui-probability/0%2E5/password-protection-ui-trigger-id/bQBRghu5w0ugnJ3q1cK0RrqdqVRP/privacy-guide-probability/0%2E5/privacy-guide-trigger-id/tqR1rjeDu0ugnJ3q1cK0P9yJEq7Z/probability/1%2E0/safe-browsing-interstitial-probability/0%2E18932671/safe-browsing-interstitial-trigger-id/Z9pSWP53n0ugnJ3q1cK0Y6YkGRpU/safety-check-probability/0%2E12121/safety-check-trigger-id/YSDfPVMnX0ugnJ3q1cK0RxEhwkay/trusted-surface-probability/0%2E012685/trusted-surface-time/5s/trusted-surface-trigger-id/CMniDmzgE0ugnJ3q1cK0U6PaEn1f,UKM.Enabled_20180314:WhitelistEntries/AboutThisSiteStatus%2CAbusiveExperienceHeuristic%2CAbusiveExperienceHeuristic%2EJavaScriptDialog%2CAbusiveExperienceHeuristic%2ETabUnder%2CAbusiveExperienceHeuristic%2EWindowOpen%2CAccessibility%2EImageDescriptions%2CAccessibility%2ERenderer%2CAccuracyTipDialog%2CAccuracyTipStatus%2CAdFrameLoad%2CAdPageLoad%2CAdsIntervention%2ELastIntervention%2CAmpPageLoad%2CAndroid%2EDarkTheme%2EAutoDarkMode%2CAndroid%2EMultiWindowChangeActivity%2CAndroid%2EMultiWindowState%2CAndroid%2EScreenRotation%2CAndroid%2EUserRequestedUserAgentChange%2CAppListAppClickData%2CAppListAppLaunch%2CAppListNonAppImpression%2CAutofill%2ECardUploadDecision%2CAutofill%2EDeveloperEngagement%2CAutofill%2EEditedAutofilledFieldAtSubmission%2CAutofill%2EFieldFillStatus%2CAutofill%2EFieldTypeValidation%2CAutofill%2EFormEvent%2CAutofill%2EFormFillSuccessIOS%2CAutofill%2EFormSubmitted%2CAutofill%2EHiddenRepresentationalFieldSkipDecision%2CAutofill%2EInteractedWithForm%2CAutofill%2ERepeatedServerTypePredictionRationalized%2CAutofill%2ESelectedMaskedServerCard%2CAutofill%2ESuggestionFilled%2CAutofill%2ESuggestionsShown%2CAutofill%2ETextFieldDidChange%2CAutofillAssistant%2EInChromeTriggering%2CAutofillAssistant%2ELiteScriptFinished%2CAutofillAssistant%2ELiteScriptOnboarding%2CAutofillAssistant%2ELiteScriptShownToUser%2CAutofillAssistant%2ELiteScriptStarted%2CAutofillAssistant%2ETiming%2CBackForwardCacheDisabledForRenderFrameHostReason%2CBackForwardCacheDisallowActivationReason%2CBackgroundFetch%2CBackgroundFetchDeletingRegistration%2CBackgroundSyncCompleted%2CBackgroundSyncRegistered%2CBadging%2CBlink%2EContextMenu%2EImageSelection%2CBlink%2EFindInPage%2CBlink%2EHTMLParsing%2CBlink%2EPageLoad%2CBlink%2EPaintTiming%2CBlink%2EScript%2EAsyncScripts%2CBlink%2EUpdateTime%2CBlink%2EUseCounter%2CBloatedRenderer%2CChromeOSApp%2EInstalledApp%2CChromeOSApp%2ELaunch%2CChromeOSApp%2EUninstallApp%2CChromeOSApp%2EUsageTime%2CClickInput%2CClientRenderingAPI%2CCompositor%2ERendering%2CCompositor%2EUserInteraction%2CContactsPicker%2EShareStatistics%2CContentIndex%2EAdded%2CContentIndex%2EDeletedByUser%2CContentIndex%2EOpened%2CContextMenuAndroid%2ESelected%2CContextMenuAndroid%2EShown%2CContextualSearch%2CContextualSuggestions%2CCPUUsageMeasurement%2CCrossOriginSubframeWithoutEmbeddingControl%2CDataReductionProxy%2CDetachedWindows%2EExperimental%2CDocument%2EOutliveTimeAfterShutdown%2CDocumentCreated%2CDownload%2ECompleted%2CDownload%2EInterrupted%2CDownload%2EResumed%2CDownload%2EStarted%2CEvent%2EScrollBegin%2ETouch%2CEvent%2EScrollBegin%2EWheel%2CEvent%2EScrollUpdate%2ETouch%2CEvent%2EScrollUpdate%2EWheel%2CExtensions%2ECrossOriginFetchFromContentScript3%2CExtensions%2EWebRequest%2EKeepaliveRequestFinished%2CFileSystemAPI%2EWebRequest%2CFlocPageLoad%2CFontMatchAttempts%2CGeneratedNavigation%2CGoogleDocsOfflineExtension%2CGraphics%2ESmoothness%2EEventLatency%2CGraphics%2ESmoothness%2ELatency%2CGraphics%2ESmoothness%2ENormalizedPercentDroppedFrames%2CGraphics%2ESmoothness%2EPercentDroppedFrames%2CGraphics%2ESmoothness%2EThroughput%2CHistoryClusters%2CHistoryManipulationIntervention%2CHistoryNavigation%2CIdentifiability%2CInputEvent%2CInputMethod%2EAssistive%2EMatch%2CInputMethod%2ENonCompliantApi%2CInstalledRelatedApps%2CIntervention%2EDocumentWrite%2EScriptBlock%2CIOS%2EFindInPageSearchMatches%2CIOS%2EIsDefaultBrowser%2CIOS%2EPageAddedToReadingList%2CIOS%2EPageReadability%2CIOS%2EPageZoomChanged%2CIOS%2ERendererGone%2CIOS%2EURLMismatchInLegacyAndSlimNavigationManager%2CJavascriptFrameworkPageLoad%2CLayout%2EDisplayCutout%2EStateChanged%2CLiteVideo%2CLoadCountsPerTopLevelDocument%2CLoadingPredictor%2CLocalNetworkRequests%2CLoginDetection%2CLookalikeUrl%2ENavigationSuggestion%2CMainFrameDownload%2CMainFrameNavigation%2CMedia%2EAutoplay%2EAttempt%2CMedia%2EAutoplay%2EAudioContext%2CMedia%2EAutoplay%2EMuted%2EUnmuteAction%2CMedia%2EBasicPlayback%2CMedia%2EEME%2EApiPromiseRejection%2CMedia%2EEME%2ECreateMediaKeys%2CMedia%2EEME%2ERequestMediaKeySystemAccess%2CMedia%2EEngagement%2ESessionFinished%2CMedia%2EEngagement%2EShortPlaybackIgnored%2CMedia%2EFeed%2EDiscover%2CMedia%2EGlobalMediaControls%2EActionButtonPressed%2CMedia%2EKaleidoscope%2ENavigation%2CMedia%2ELearning%2EPredictionRecord%2CMedia%2ESiteMuted%2CMedia%2EVideoDecodePerfRecord%2CMedia%2EWatchTime%2CMedia%2EWebAudio%2EAudioContext%2EAudibleTime%2CMedia%2EWebMediaPlayerState%2CMediaRouter%2ECastWebSenderExtensionLoadUrl%2CMediaRouter%2ESiteInitiatedMirroringStarted%2CMediaRouter%2ETabMirroringStarted%2CMemory%2EExperimental%2CMemory%2ETabFootprint%2CMixedContentAutoupgrade%2EResourceRequest%2CMobileFriendliness%2CMobileMenu%2EDirectShare%2CMobileMenu%2EFindInPage%2CMobileMenu%2EShare%2CNavigationPredictorAnchorElementMetrics%2CNavigationPredictorPageLinkClick%2CNavigationPredictorPageLinkMetrics%2CNavigationPredictorRendererWarmup%2CNavigationTiming%2CNet%2ELegacyTLSVersion%2CNoStatePrefetch%2CNotification%2COfflineMeasurements%2COfflinePages%2ESavePageRequested%2COmniboxSecurityIndicator%2EFormSubmission%2COptimizationGuide%2COptimizationGuideAutotuning%2CPageContentAnnotations%2CPageDomainInfo%2CPageForegroundSession%2CPageInfoBubble%2CPageLoad%2CPageLoad%2EFromGoogleSearch%2CPageLoad%2EInternal%2CPageLoad%2EServiceWorkerControlled%2CPageLoad%2ESignedExchange%2CPageLoadCapping%2CPageWithPassword%2CPaintPreviewCapture%2CPasswordForm%2CPasswordManager%2EWellKnownChangePasswordResult%2CPaymentApp%2ECheckoutEvents%2CPaymentRequest%2ECheckoutEvents%2CPaymentRequest%2ETransactionAmount%2CPepper%2EBroker%2CPerfectHeuristics%2CPerformanceAPI%2ELongTask%2CPerformanceAPI%2EMemory%2CPerformanceAPI%2EMemory%2ELegacy%2CPeriodicBackgroundSyncEventCompleted%2CPeriodicBackgroundSyncRegistered%2CPermission%2CPermissionUsage%2CPlugins%2EFlashInstance%2CPopup%2EClosed%2CPopup%2EPage%2CPortal%2EActivate%2CPostMessage%2EIncoming%2EFrame%2CPostMessage%2EIncoming%2EPage%2CPowerUsageScenariosIntervalData%2CPrefetchProxy%2CPrefetchProxy%2EAfterSRPClick%2CPrefetchProxy%2EPrefetchedResource%2CPrerenderPageLoad%2CPreviews%2CPreviewsCoinFlip%2CPreviewsDeferAllScript%2CPreviewsResourceLoadingHints%2CPublicImageCompressionDataUse%2CPublicImageCompressionImageLoad%2CPWA%2EVisit%2CReaderModeActivated%2CReaderModeReceivedDistillability%2CRendererSchedulerTask%2CRenderViewContextMenu%2EUsed%2CResponsiveness%2EUserInteraction%2CResponsivenessMeasurement%2CSameSiteDifferentSchemeRequest%2CSameSiteDifferentSchemeResponse%2CSchemefulSameSiteContextDowngrade%2CScreenBrightness%2CSecurity%2ESafetyTip%2CSecurity%2ESiteEngagement%2CSharedHighlights%2ELinkGenerated%2CSharedHighlights%2ELinkOpened%2CSharing%2EClickToCall%2CShopping%2EChromeCart%2CShopping%2EFormSubmitted%2CShopping%2EWillSendRequest%2CSiteIsolation%2EXSD%2EBrowser%2EBlocked%2CSmartCharging%2CSMSReceiver%2CSSL%2EMixedContentShown%2CSSL%2EMixedContentShown2%2CSubframeDownload%2CSubresourceFilter%2CSubresourceRedirect%2EPublicSrcVideoCompression%2CTab%2ERendererOOM%2CTab%2EScreenshot%2CTabManager%2EBackground%2EFirstAlertFired%2CTabManager%2EBackground%2EFirstAudioStarts%2CTabManager%2EBackground%2EFirstFaviconUpdated%2CTabManager%2EBackground%2EFirstNonPersistentNotificationCreated%2CTabManager%2EBackground%2EFirstTitleUpdated%2CTabManager%2EBackground%2EForegroundedOrClosed%2CTabManager%2EExperimental%2EBackgroundTabOpening%2ETabSwitchLoadStopped%2CTabManager%2EExperimental%2ESessionRestore%2EForegroundTab%2EPageLoad%2CTabManager%2EExperimental%2ESessionRestore%2ETabSwitchLoadStopped%2CTabManager%2ELifecycleStateChange%2CTabManager%2ETabLifetime%2CTabManager%2ETabMetrics%2CTabManager%2EWindowMetrics%2CTouchToFill%2EShown%2CTranslate%2CTranslatePageLoad%2CTrustedWebActivity%2ELocationDelegation%2CTrustedWebActivity%2EOpen%2CTrustedWebActivity%2EQualityEnforcementViolation%2CUnload%2CUserActivity%2CUserActivityId%2CUserSettingsEvent%2CV8%2EWasm%2EModuleCompiled%2CV8%2EWasm%2EModuleDecoded%2CV8%2EWasm%2EModuleInstantiated%2CV8%2EWasm%2EModuleTieredUp%2CVirtualKeyboard%2EOpen%2CWebAPK%2EInstall%2CWebAPK%2ESessionEnd%2CWebAPK%2EUninstall%2CWebAPK%2EVisit%2CWebApp%2EDailyInteraction%2CWebOTPImpact%2CWebRTC%2EAddressHarvesting%2CWebRTC%2EComplexSdp%2CWorker%2EClientAdded%2CXR%2EPageSession%2CXR%2EWebXR%2CXR%2EWebXR%2EPresentationSession%2CXR%2EWebXR%2ESession%2CXR%2EWebXR%2ESessionRequest,UMA-Pseudo-Metrics-Effect-Injection-25-Percent.BigEffect_18_20241105:multiplicative_factor/1%2E05,UkmSamplingRate.Downsampled_202309:AbandonedSRPNavigation/4/AbusiveExperienceHeuristic%2EJavaScriptDialog/5/AdFrameLoad/80/AdPageLoad/14/AdsIntervention%2ELastIntervention/2/Android%2EMultiWindowState/8/Autofill%2EDeveloperEngagement/16/Autofill%2EFieldFillStatus/70/Autofill%2EFieldTypeValidation/240/Autofill%2EFormEvent/18/Autofill%2ETextFieldDidChange/2/BTM%2EShortVisit/8/BTM%2EShortVisitNeighbor/14/Blink%2EFrameLoader/1200/Blink%2EHTMLParsing/35/Blink%2EJavaScriptFramework%2EVersions/3/Blink%2EPageLoad/45/Blink%2EPaintTiming/240/Blink%2EUpdateTime/90/BrowsingTopics%2EDocumentBrowsingTopicsApiResult2/800/BrowsingTopics%2EPageLoad/3/ChargeEventHistory/4/ChromeOSApp%2EInputEvent/ChromeOSApp/ChromeOSApp%2EInstalledApp/ChromeOSApp/ChromeOSApp%2ELaunch/ChromeOSApp/ChromeOSApp%2EUninstallApp/ChromeOSApp/ChromeOSApp%2EUsageTime/ChromeOSApp/ChromeOSApp%2EUsageTimeReusedSourceId/ChromeOSApp/ClientHints%2EAcceptCHFrameUsage/300/ClientHints%2EAcceptCHHeaderUsage/120/ClientHints%2EDelegateCHMetaUsage/6/ClientRenderingAPI/750/Compose%2ETextElementUsage/4/ContentManagementSystemPageLoad/40/Conversions%2ESourceRegistration/8/Conversions%2ETriggerRegistration/4/Cookies%2EBlocked%2EDueToOriginMismatch/140/Cookies%2EFirstPartyPartitionedInCrossSiteContextV3/1100/CrossOriginSubframeWithoutEmbeddingControl/200/DIPS%2EChainEnd/3/DIPS%2ENavigationFlowNode/3/DIPS%2ERedirect/5/DIPS%2ETrustIndicator%2EDirectNavigationV2/3/DailyChargeSummary/5/DocumentCreated/300/Download%2ECompleted/Download/Download%2EInterrupted/Download/Download%2EResumed/Download/Download%2EStarted/Download/Event%2EScroll/300/Event%2EScrollJank%2EPredictorJank/600/Extensions%2EOnNavigation/160/Extensions%2EWebRequest%2EKeepaliveRequestFinished/240/GeneratedNavigation/4/GoogleDocsOfflineExtension/40/Graphics%2ESmoothness/30/Graphics%2ESmoothness%2EEventLatency/Graphics%2ESmoothness/Graphics%2ESmoothness%2EFrameSequence/Graphics%2ESmoothness/Graphics%2ESmoothness%2ELatency/Graphics%2ESmoothness/Graphics%2ESmoothness%2ENormalizedPercentDroppedFrames/Graphics%2ESmoothness/HistoryApi/450/HistoryApi%2EAdvanceMethod/20/HistoryManipulationIntervention/6/InstalledRelatedApps/14/JavascriptFrameworkPageLoad/40/LoadCountsPerTopLevelDocument/60/LoadingPredictor/25/LocalNetworkRequests/30/MainFrameNavigation/2/MainFrameNavigation%2EZstdContentEncoding/5/Media/80/Media%2EAutoplay%2EAttempt/35/Media%2EAutoplay%2EAudioContext/2/Media%2EBasicPlayback/Media/Media%2EEME%2EGetStatusForPolicy/25/Media%2EEME%2ERequestMediaKeySystemAccess/2/Media%2EVideoDecodePerfRecord/16/Media%2EWebAudio%2EAudioContext%2EAudibleTime/Media/Media%2EWebMediaPlayerState/Media/Memory%2EExperimental/240/Memory%2ETabFootprint/40/MixedContentAutoupgrade%2EResourceRequest/6/MobileFriendliness/4/MobileFriendliness%2ETappedBadTargets/35/Navigation%2EFromGoogleSearch%2EAbandoned/3/Navigation%2EFromGoogleSearch%2ETimingInformation/5/Navigation%2EReceivedResponse/50/NavigationPredictor/30/NavigationPredictorAnchorElementMetrics/NavigationPredictor/NavigationPredictorPageLinkClick/NavigationPredictor/NavigationPredictorPageLinkMetrics/NavigationPredictor/NavigationPredictorPreloadOnHover/NavigationPredictor/NavigationPredictorUserInteractions/NavigationPredictor/NavigationThrottleDeferredTime/10/NavigationTiming/30/Network%2EDataUrls/240/Notification/3/OmniboxSecurityIndicator%2EFormSubmission/6/OpenerHeuristic%2EPostPopupCookieAccess/30/OptimizationGuide/120/OptimizationGuide%2EAnnotatedPageContent/3/PageContentAnnotations2/60/PageDomainInfo/35/PageForegroundSession/60/PageLoadInitiatorForAdTagging/40/PaintPreviewCapture/2/PartitionedCookiePresentV2/9000/PasswordManager%2EFirstCCTPageLoad/2/PerformanceAPI%2ELongAnimationFrame/1100/PerformanceAPI%2ELongTask/50/PerformanceAPI%2EMemory%2ELegacy/1700/PerformanceManager%2EFreezingEligibility/6/PerformanceManager%2EPageResourceUsage2/390/PermissionUsage/16/PermissionUsage%2ENotificationShown/6/Popup/3/Popup%2EClosed/Popup/Popup%2EPage/Popup/PowerUsageScenariosIntervalData/40/PrefetchProxy/35/PrefetchProxy%2EAfterSRPClick/12/PrefetchProxy%2EPrefetchedResource/8/Preloading%2EAttempt/PreloadingAttempt/Preloading%2EAttempt%2EPreviousPrimaryPage/PreloadingAttempt/Preloading%2EPrediction/220/Preloading%2EPrediction%2EPreviousPrimaryPage/270/RedirectHeuristic%2ECookieAccess2/4/RedirectHeuristic%2ECookieAccessThirdParty2/4/Responsiveness%2EUserInteraction/70/SamesiteRedirectContextDowngrade/10/Security%2ESiteEngagement/35/ServiceWorker%2EMainResourceLoadCompleted/18/ServiceWorker%2EOnLoad/4/SharedStorage%2EWorklet%2EOnDestroyed/14/Site%2EInstall/8/Site%2EManifest/8/Site%2EQuality/8/SiteFamiliarityHeuristicResult/45/SiteInstance/6/SoftNavigation/6/SubresourceFilter/45/SubresourceLoad%2EZstdContentEncoding/850/TabManager%2ETabLifetime/8/TabRevisitTracker%2ETabStateChange/45/ThirdPartyCookies%2EBreakageIndicator%2EHTTPError/60/Translate/40/TranslatePageLoad/50/Unload/35/UserPerceivedPageVisit/120/V8%2EWasm/12/V8%2EWasm%2EModuleCompiled/V8%2EWasm/V8%2EWasm%2EModuleDecoded/V8%2EWasm/V8%2EWasm%2EModuleInstantiated/V8%2EWasm/WebOTPImpact/18/WindowProxyUsage/4/XR%2EWebXR/2/_default_sampling/1/_webdx_features_sampling/1,VSyncAlignedPresent.EnabledLaunch:Target/Interaction", "force-fieldtrials": "ANGLEPerContextBlobCache/Default/AXBlockFlowIterator/Default/*AdsP4V2/Default/AiSettingsPageEnterpriseDisabledUi/Launch/AlwaysBlock3pcsIncognito/EnabledLaunch/AutoPictureInPictureForVideoPlayback/Default/AutofillAiVoteForFormatStrings/EnabledLaunch/AutofillDetectFieldVisibility/Default/AutofillEnableCardBenefitsForBmo/Control_50/AutofillEnableCardBenefitsIph/EnabledLaunch/AutofillFixSplitCreditCardImport/Control_50/AutofillGreekRegexes/Enabled_20250415_50/AutofillImproveCityFieldClassification/Default/AutofillOptimizeFormExtraction/Control_20250626/AutofillPopupZOrderSecuritySurface_V2/EnabledLaunch/AutofillRelaxAddressImport/Default/AutofillUnifyRationalizationAndSectioningOrder/Default/AvoidDuplicateDelayBeginFrame/Default/*AvoidUnnecessaryBeforeUnloadCheckSync/WithSendBeforeUnload_20250715/*BackForwardCachePrioritizedEntry/Default/*BeaconLeakageLogging/Control_50/*BoostRenderProcessForLoading/EnableAll_20250715/*CacheSharingForPervasiveScripts/Preperiod75/CacheStorageAblation/Default/CameraMicPreview/Default/CanvasHibernationExperiments/Default/CastStreamingVp9/EnabledLaunch/*ChromeChannelStable/Enabled/ChromeWebStoreNavigationThrottle/Default/*ChromnientFetchSrp/FetchSrpEnabled_20250313/*ChromnientNewFeedback/Default/*ChromnientSimplifiedSelection/Launched/ClientSideDetectionLlamaForcedTriggerInfoForScamDetection/Default/*ComposeV3Migration/Default/CompositorLoadingAnimations/Control_50/CookieDeprecationFacilitatedTestingLabelOnly/LabelOnly4_20250402_Buffer/DefaultProfileEnterpriseBadgingForAvatar/Enabled_137Launch/DefaultSiteInstanceGroups/Default/*DeprecateUnload/Enabled_138_100/*DesktopNewTabPage/Preperiod4_PP_20250710/DeviceBoundSessionAccessObserverSharedRemote/Default/DialDownWebUIJSErrorReportingExtended/Default/DirectCompositorThreadIpcMacLinuxChromeOS/EnabledLaunch/DisableRendererMainIsNormalThreadTypeForWebRTC/NoDeprioritization_20250617/*DwaFeature/Default/EnableAsyncUploadAfterVerdict/EnabledLaunch/EnableExtensionsExplicitBrowserSignin/EnabledLaunch/*EnableHangWatcher/Default/EnablePolicyPromotionBanner/EnabledLaunch/*EnableTLS13EarlyData/Preperiod10/EnhancedFieldsForSecOps/EnabledLaunch/EnterpriseFileSystemAccessDeepScan/EnabledLaunch/EscapeLtGtInAttributes/EnabledLaunch/*EventTimingIgnorePresentationTimeFromUnexpectedFrameSource/Default/*ExpiredHistograms/ExpiryEnabledWithAllowlist/ExportFrameTimingAfterFrameDoneExperiment/EnabledLaunch/*ExtensionManifestV2Deprecation/Enabled_HardDeprecation_Launched_Stable/*ExtremeLightweightUAFDetector/Default/FasterSetCookie/Control_20250715_50/*FedCmIntrusionMitigation/Default/*FedCmSegmentationPlatform/Default/*FencedFramesEnableCrossOriginAutomaticBeaconData/EnabledLaunch/*FenderAutoPreconnectLcpOrigins/Default/*FieldRankServerClassification/Default/FingerprintingProtectionFilter/Default/FledgeFacilitatedTestingSignalsHeaders/EnabledLaunch/GlicClosedCaptioning/Control_50/*GraphicsCombinedHoldback/Enabled_20250623/*HttpCacheNoVarySearch/Default/IPProtectionMdlImpl/EnabledLaunch/*IPProtectionPhase0/default_Stable_2025-07-15_Google_Only_MDL/IncreaseCookieAccessCacheSize/EnabledLaunch/*LensSearchSidePanelScrollToAPI/Default/ListAccountsUsesBinaryFormat/EnabledLaunch/*LoadingNetworking2025/Default_20250501/LoadingPredictorLimitPreconnectSocketCount/Default/LocalIpAddressInEvents/Control_20250618/*LocalWebApprovalsLinuxMacWindows/Control_20250520/*LowPriorityAsyncScriptExecutionForDesktop/Default/MacAccessibilityAPIMigration/Default/MacICloudKeychainRecoveryFactor/Enabled_50/*MacKeychainApiMigration/Default/MediaRecorderSeekableWebmKillSwitch/Disabled_EmergencyKillSwitch/MemoryPurgeOnFreezeLimit/Default/*MultipleLayerMemberRefTestStudy/GroupB_20241008/NotificationTelemetryService/EnabledLaunch/*OmniboxDriveEligibility/Launched/*OptimizationTargetClientSidePhishingGradualRollout/Default/*OptimizationTargetGeolocationPermissionPredictionsGradualRollout/Default/*OptimizationTargetModelExecutionFeatureScamDetectionGradualRollout/Enabled_20250710/*OptimizationTargetNotificationPermissionPredictionsGradualRollout/Enabled_20250716/*PWANavigationCapturingV2WindowMacLinux/EnabledSettingOnByDefault20250625/*PartitionAllocFewerMemoryRegionsMac/Default/*PassHistogramSharedMemoryOnLaunch/EnabledLaunch/*PassageEmbeddingsPerformance/Default_20250429/PdfInfoBar/Control20250707/*PdfInkSignatures/Default_20250618/PdfOutOfProcessIframe/Control_20250618/PerformanceInterventionAlgorithm/EnabledLessAggressive_Launched/*PermissionSiteSettingsRadioButton/Default/*PermissionsAIv3/Default/PinnedTabToastOnClose/EnabledLaunch/*PrefetchScheduler/Default/PrefetchServiceWorker/Default/*PreloadInlineDeferredImages/Default/*PreloadingConfig/Default_20250509/PreloadingNoSamePageFragmentAnchorTracking/Default/PrivacyGuideAiSettings/Launch/PrivacySandboxAdsApiUxEnhancements/EnabledLaunch/*PrivacySandboxAttestationsDefaultDeny/Disabled_Launch/PrivacySandboxNoticeQueue/Default/PrivacySandboxPrivacyGuideAdTopics/Enabled20250714/ProfileRemoteCommands/EnabledLaunch/ProgrammaticScrollAnimationOverride/Default/ProgressiveAccessibility/Default/ProtectedAudienceDealsSupport/Default/ProtectedAudienceKAnonymityKeyCacheStudy/EnabledLaunch/ProtectedAudienceMultiThreadedSellerWorklet/Default/ProtectedAudienceTrustedSignalsKVv1CreativeScanning/Default/ProtectedAudiencesKAnonymityEnforcementStudy/Enabled_20250304/*QUIC/ControlOriginFrameJul2025/*ReadAnythingReadAloudDesktop/Default/RemoveCancelledScriptedIdleTasks/EnabledLaunch/RenderDocumentWithNavigationQueueing/Default_20250311/*RetroactivePreperiod/Default_Mac/*SearchPrefetchHighPriorityPrefetches/Default/*SeedFileTrial/Default/*ServiceWorkerBackgroundUpdateForRegisteredStorageKeys/Default/*ServiceWorkerStaticRouterRaceNetworkRequestPerformanceImprovement/Default/SettingSearchExplorationHaTS/Default/*SharedTabGroups/JoinOnlyEnabled_20240506/ShowSuggestionsOnAutofocus/Control_20250630/SidePanelResizing/Default/*SimdutfBase64Support/Enabled_SimdutfWithFurtherOptimization_20250708/SoftNavigationDetectionAdvancedPaintAttribution/Default/*SpeculativeImageDecodes/Default/TabCaptureInfobarLinks/Disabled_TabCaptureInfobarLinks/TabGroupShortcuts/Default/TabGroupSyncServiceDesktopMigrationRelaunch/EnabledLaunch/*TabSearchToolbarButtonNonEnUS/Default/TaskManagerDesktopRefresh/Default/TrustSafetySentimentSurveyV2/EnabledLaunched/*UKM/Enabled_20180314/*UMA-Population-Restrict/normal/UMA-Pseudo-Metrics-Effect-Injection-25-Percent/BigEffect_18_20241105/*UMA-Uniformity-Trial-0.5-Percent-1/group_124/*UMA-Uniformity-Trial-0.5-Percent-2/group_131/*UMA-Uniformity-Trial-1-Percent/group_96/*UMA-Uniformity-Trial-10-Percent/group_02/*UMA-Uniformity-Trial-10-Percent-sanity/group_09/*UMA-Uniformity-Trial-100-Percent/group_01/*UMA-Uniformity-Trial-20-Percent/group_01/*UMA-Uniformity-Trial-20-Percent-Session/group_03/*UMA-Uniformity-Trial-5-Percent/group_18/*UMA-Uniformity-Trial-50-Percent/default/UkmSamplingRate/Downsampled_202309/UnimportantFramePolicy/All20250703/*UnoDesktopBookmarksAndReadingList/Default/UnoDesktopHistorySyncPillExperiment/Default/*UseAdHocSigningForWebAppShims/Default/V8IgnitionElideRedundantTdzChecksKillSwitch/Disabled_EmergencyKillSwitch/V8ManagedZoneMemory/Control_50/V8StoreStoreEliminationKillSwitch/Disabled_EmergencyKillSwitch/VSyncAlignedPresent/EnabledLaunch/WebContentsDiscard/Enabled_20250617/*WebRTC-ForceDtls13/Control,_20250506/*WebRTC-SwitchEncoderFollowCodecPreferenceOrder/Disabled/WebRtcEncodedTransformDirectCallback/EnabledLaunch/WebUIInProcessResourceLoading/launched"}