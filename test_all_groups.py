#!/usr/bin/env python3
"""
Test script to click on each group and extract first 3 messages
"""

import subprocess
import sys
import json
import time

def run_playwright_script(script_content):
    """Run playwright script in a separate process to avoid asyncio issues"""
    try:
        # Write script to temp file
        with open('/tmp/playwright_script.py', 'w') as f:
            f.write(script_content)
        
        # Run in separate process
        result = subprocess.run([sys.executable, '/tmp/playwright_script.py'], 
                              capture_output=True, text=True, timeout=45)
        
        if result.returncode == 0:
            return json.loads(result.stdout)
        else:
            return {'success': False, 'error': result.stderr}
    except Exception as e:
        return {'success': False, 'error': str(e)}

def get_messages_from_group(group_name):
    """Click on a group and extract first 3 messages"""
    script = f'''
import json
from playwright.sync_api import sync_playwright
import time

try:
    with sync_playwright() as p:
        browser = p.chromium.connect_over_cdp("http://localhost:9222")
        context = browser.contexts[0]
        pages = context.pages
        
        whatsapp_page = None
        for page in pages:
            if 'web.whatsapp.com' in page.url:
                whatsapp_page = page
                break
        
        if not whatsapp_page:
            result = {{'success': False, 'error': 'WhatsApp page not found', 'messages': []}}
        else:
            # Get chat elements
            chat_elements = whatsapp_page.query_selector_all('#pane-side div[role="listitem"]')
            
            # Find target group
            target_index = -1
            for i, chat_elem in enumerate(chat_elements):
                try:
                    title_elem = chat_elem.query_selector('span[title]')
                    if title_elem:
                        title = title_elem.get_attribute('title') or title_elem.text_content()
                        if title and title.strip() == "{group_name}":
                            target_index = i
                            break
                except:
                    continue
            
            if target_index == -1:
                result = {{'success': False, 'error': 'Group not found', 'messages': []}}
            else:
                # Click the group
                chat_elements[target_index].click()
                time.sleep(4)  # Wait for messages to load
                
                # Check if conversation loaded
                conv_panel = whatsapp_page.query_selector('#main')
                if not conv_panel:
                    result = {{'success': False, 'error': 'Conversation panel not found', 'messages': []}}
                else:
                    # Extract messages
                    messages = []
                    
                    # Use the working selectors from debug
                    message_selectors = [
                        'div[role="row"]',  # This works - contains message containers
                        'div[class*="message"]'  # Alternative that also works
                    ]

                    message_elements = []
                    for selector in message_selectors:
                        message_elements = whatsapp_page.query_selector_all(selector)
                        if message_elements:
                            break
                    
                    # Get first 3 messages
                    for i, msg_elem in enumerate(message_elements[:3]):
                        try:
                            # Use working selectors for message text
                            text_selectors = [
                                'span.selectable-text',  # This works best
                                '.selectable-text',      # Alternative
                                'span[data-testid="msg-text"]'  # Fallback
                            ]
                            
                            message_text = None
                            for text_selector in text_selectors:
                                text_elem = msg_elem.query_selector(text_selector)
                                if text_elem:
                                    message_text = text_elem.text_content()
                                    if message_text and message_text.strip():
                                        message_text = message_text.strip()
                                        break
                            
                            # Try to get sender info
                            sender_selectors = [
                                '[data-testid="msg-meta"] span',
                                'div[data-testid="conversation-info-header"] span',
                                'span[dir="auto"]'
                            ]
                            
                            sender = "Unknown"
                            for sender_selector in sender_selectors:
                                sender_elem = msg_elem.query_selector(sender_selector)
                                if sender_elem:
                                    sender_text = sender_elem.text_content()
                                    if sender_text and sender_text.strip() and len(sender_text.strip()) < 50:
                                        sender = sender_text.strip()
                                        break
                            
                            if message_text:
                                messages.append({{
                                    'index': i + 1,
                                    'sender': sender,
                                    'text': message_text[:200]  # Limit text length
                                }})
                        except Exception as e:
                            messages.append({{
                                'index': i + 1,
                                'sender': 'Error',
                                'text': f'Error extracting message: {{str(e)}}'
                            }})
                    
                    result = {{'success': True, 'error': None, 'messages': messages, 'total_found': len(message_elements)}}
        
        print(json.dumps(result))

except Exception as e:
    print(json.dumps({{'success': False, 'error': str(e), 'messages': []}}))
'''
    
    return run_playwright_script(script)

def test_all_groups():
    """Test clicking on each group and extracting messages"""
    # First get all groups
    from whatsapp_simple import get_whatsapp_groups
    
    print("🔍 Getting list of all groups...")
    groups_result = get_whatsapp_groups()
    
    if not groups_result['success']:
        print(f"❌ Failed to get groups: {{groups_result['error']}}")
        return
    
    groups = groups_result['groups']
    print(f"✅ Found {{len(groups)}} groups to test")
    print("=" * 60)
    
    results = []
    
    for i, group in enumerate(groups, 1):
        group_name = group['name']
        print(f"\n📱 Testing Group {i}/{len(groups)}: {group_name}")
        print("-" * 50)
        
        # Get messages from this group
        messages_result = get_messages_from_group(group_name)
        
        if messages_result['success']:
            messages = messages_result['messages']
            total_found = messages_result.get('total_found', 0)
            
            print(f"✅ Successfully accessed group ({total_found} total messages found)")

            if messages:
                print("📝 First 3 messages:")
                for msg in messages:
                    print(f"  {msg['index']}. [{msg['sender']}]: {msg['text']}")
            else:
                print("📝 No messages found or couldn't extract message text")

            results.append({
                'group_name': group_name,
                'success': True,
                'messages': messages,
                'total_messages_found': total_found
            })
        else:
            print(f"❌ Failed to access group: {messages_result['error']}")
            results.append({
                'group_name': group_name,
                'success': False,
                'error': messages_result['error'],
                'messages': []
            })
        
        # Small delay between groups
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"✅ Successfully accessed: {len(successful)}/{len(results)} groups")
    print(f"❌ Failed to access: {len(failed)}/{len(results)} groups")

    if failed:
        print("\n❌ Failed groups:")
        for result in failed:
            print(f"  - {result['group_name']}: {result['error']}")
    
    return results

if __name__ == "__main__":
    print("🚀 Testing all WhatsApp groups...")
    print("This will click on each group and extract the first 3 messages")
    print("=" * 60)
    
    results = test_all_groups()
    
    print(f"\n🎉 Test completed! Processed {len(results)} groups.")
