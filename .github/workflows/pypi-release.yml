name: Publish Python 🐍distribution 📦 to Py<PERSON> and TestPy<PERSON>

on: push
jobs:
  build:
    name: Build distribution 📦
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"
    - name: Install pypa/build
      run: >-
        python3 -m
        pip install
        build
        --user
    - name: Build a binary wheel and a source tarball
      run: python3 -m build
    - name: Store the distribution packages
      uses: actions/upload-artifact@v4
      with:
        name: python-package-distributions
        path: dist/


  test-pypi-package:
    name: Test the built 📦 package works basically.
    runs-on: ubuntu-latest
    needs:
    - build
    steps:
    - name: Download all the dists
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions
        path: dist/
    - name: Set up Python 3.11
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    - name: Test that the basic pip built package runs without error
      run: |
        set -ex
        ls -alR 
        
        # Find and install the first .whl file
        find dist -type f -name "*.whl" -exec pip3 install {} \; -quit
        changedetection.io -d /tmp -p 10000 &
        
        sleep 3
        curl --retry-connrefused --retry 6 http://127.0.0.1:10000/static/styles/pure-min.css >/dev/null
        curl --retry-connrefused --retry 6 http://127.0.0.1:10000/ >/dev/null
        killall changedetection.io


  publish-to-pypi:
    name: >-
      Publish Python 🐍 distribution 📦 to PyPI
    if: startsWith(github.ref, 'refs/tags/')  # only publish to PyPI on tag pushes
    needs:
    - test-pypi-package
    runs-on: ubuntu-latest
    environment:
      name: release
      url: https://pypi.org/p/changedetection.io
    permissions:
      id-token: write  # IMPORTANT: mandatory for trusted publishing

    steps:
    - name: Download all the dists
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions
        path: dist/
    - name: Publish distribution 📦 to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
