name: Build and push containers

on:
  # Automatically triggered by a testing workflow passing, but this is only checked when it lands in the `master`/default branch
#  workflow_run:
#    workflows: ["ChangeDetection.io Test"]
#    branches: [master]
#    tags: ['0.*']
#    types: [completed]

  # Or a new tagged release
  release:
    types: [published, edited]

  push:
    branches:
      - master

jobs:
  metadata:
    runs-on: ubuntu-latest
    steps:
    - name: Show metadata
      run: |
        echo SHA ${{ github.sha }}
        echo github.ref:  ${{ github.ref }}
        echo github_ref: $GITHUB_REF
        echo Event name: ${{ github.event_name }}
        echo Ref ${{ github.ref }}
        echo c: ${{ github.event.workflow_run.conclusion }}
        echo r: ${{ github.event.workflow_run }}
        echo tname: "${{ github.event.release.tag_name }}"
        echo headbranch: -${{ github.event.workflow_run.head_branch }}-
        set

  build-push-containers:
    runs-on: ubuntu-latest
    # If the testing workflow has a success, then we build to :latest
    # Or if we are in a tagged release scenario.
    if: ${{ github.event.workflow_run.conclusion == 'success' }} || ${{ github.event.release.tag_name }} != ''
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: 3.11

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8 pytest
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: Create release metadata
        run: |
          # COPY'ed by Dockerfile into changedetectionio/ of the image, then read by the server in store.py
          echo ${{ github.sha }} > changedetectionio/source.txt
          echo ${{ github.ref }} > changedetectionio/tag.txt

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          image: tonistiigi/binfmt:latest
          platforms: all

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Login to Docker Hub Container Registry
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3
        with:
          install: true
          version: latest
          driver-opts: image=moby/buildkit:master

      # master branch -> :dev container tag
      - name: Build and push :dev
        id: docker_build
        if: ${{ github.ref }} == "refs/heads/master"
        uses: docker/build-push-action@v6
        with:
          context: ./
          file: ./Dockerfile
          push: true
          tags: |
            ${{ secrets.DOCKER_HUB_USERNAME }}/changedetection.io:dev,ghcr.io/${{ github.repository }}:dev
          platforms: linux/amd64,linux/arm64,linux/arm/v7,linux/arm/v8,linux/arm64/v8
          cache-from: type=gha
          cache-to: type=gha,mode=max

# Looks like this was disabled
#          provenance: false

      # A new tagged release is required, which builds :tag and :latest
      - name: Debug release info
        if: github.event_name == 'release' && startsWith(github.event.release.tag_name, '0.')
        run: |
          echo "Release tag: ${{ github.event.release.tag_name }}"
          echo "Github ref: ${{ github.ref }}"
          echo "Github ref name: ${{ github.ref_name }}"
          
      - name: Docker meta :tag
        if: github.event_name == 'release' && startsWith(github.event.release.tag_name, '0.')
        uses: docker/metadata-action@v5
        id: meta
        with:
            images: |
                ${{ secrets.DOCKER_HUB_USERNAME }}/changedetection.io
                ghcr.io/dgtlmoon/changedetection.io
            tags: |
                type=semver,pattern={{version}},value=${{ github.event.release.tag_name }}
                type=semver,pattern={{major}}.{{minor}},value=${{ github.event.release.tag_name }}
                type=semver,pattern={{major}},value=${{ github.event.release.tag_name }}
                type=raw,value=latest

      - name: Build and push :tag
        id: docker_build_tag_release
        if: github.event_name == 'release' && startsWith(github.event.release.tag_name, '0.')
        uses: docker/build-push-action@v6
        with:
          context: ./
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          platforms: linux/amd64,linux/arm64,linux/arm/v7,linux/arm/v8,linux/arm64/v8
          cache-from: type=gha
          cache-to: type=gha,mode=max
# Looks like this was disabled
#          provenance: false

      - name: Image digest
        run: echo step SHA ${{ steps.vars.outputs.sha_short }} tag ${{steps.vars.outputs.tag}} branch ${{steps.vars.outputs.branch}} digest ${{ steps.docker_build.outputs.digest }}

