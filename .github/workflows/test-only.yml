name: ChangeDetection.io App Test

# Triggers the workflow on push or pull request events
on: [push, pull_request]

jobs:
  lint-code:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Lint with Ruff
        run: |
          pip install ruff
          # Check for syntax errors and undefined names
          ruff check . --select E9,F63,F7,F82
          # Complete check with errors treated as warnings
          ruff check . --exit-zero

  test-application-3-10:
    needs: lint-code
    uses: ./.github/workflows/test-stack-reusable-workflow.yml
    with:
      python-version: '3.10'


  test-application-3-11:
    needs: lint-code
    uses: ./.github/workflows/test-stack-reusable-workflow.yml
    with:
      python-version: '3.11'

  test-application-3-12:
    needs: lint-code
    uses: ./.github/workflows/test-stack-reusable-workflow.yml
    with:
      python-version: '3.12'
      skip-pypuppeteer: true

  test-application-3-13:
    needs: lint-code
    uses: ./.github/workflows/test-stack-reusable-workflow.yml
    with:
      python-version: '3.13'
      skip-pypuppeteer: true