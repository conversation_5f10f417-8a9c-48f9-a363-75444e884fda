name: ChangeDetection.io Container Build Test

# Triggers the workflow on push or pull request events

# This line doesnt work, even tho it is the documented one
#on: [push, pull_request]

on:
  push:
    paths:
      - requirements.txt
      - Dockerfile
      - .github/workflows/*
      - .github/test/Dockerfile*

  pull_request:
    paths:
      - requirements.txt
      - Dockerfile
      - .github/workflows/*
      - .github/test/Dockerfile*

  # Changes to requirements.txt packages and Dockerfile may or may not always be compatible with arm etc, so worth testing
  # @todo: some kind of path filter for requirements.txt and Dockerfile
jobs:
  builder:
    name: Build ${{ matrix.platform }} (${{ matrix.dockerfile == './Dockerfile' && 'main' || 'alpine' }})
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          # Main Dockerfile platforms
          - platform: linux/amd64
            dockerfile: ./Dockerfile
          - platform: linux/arm64
            dockerfile: ./Dockerfile
          - platform: linux/arm/v7
            dockerfile: ./Dockerfile
          - platform: linux/arm/v8
            dockerfile: ./Dockerfile
          - platform: linux/arm64/v8
            dockerfile: ./Dockerfile
          # Alpine Dockerfile platforms (musl via alpine check)
          - platform: linux/amd64
            dockerfile: ./.github/test/Dockerfile-alpine
          - platform: linux/arm64
            dockerfile: ./.github/test/Dockerfile-alpine
    steps:
        - uses: actions/checkout@v4
        - name: Set up Python 3.11
          uses: actions/setup-python@v5
          with:
            python-version: 3.11

        # Just test that the build works, some libraries won't compile on ARM/rPi etc
        - name: Set up QEMU
          uses: docker/setup-qemu-action@v3
          with:
            image: tonistiigi/binfmt:latest
            platforms: all

        - name: Set up Docker Buildx
          id: buildx
          uses: docker/setup-buildx-action@v3
          with:
            install: true
            version: latest
            driver-opts: image=moby/buildkit:master

        - name: Test that the docker containers can build (${{ matrix.platform }} - ${{ matrix.dockerfile }})
          id: docker_build
          uses: docker/build-push-action@v6
          # https://github.com/docker/build-push-action#customizing
          with:
            context: ./
            file: ${{ matrix.dockerfile }}
            platforms: ${{ matrix.platform }}
            cache-from: type=gha
            cache-to: type=gha,mode=max

