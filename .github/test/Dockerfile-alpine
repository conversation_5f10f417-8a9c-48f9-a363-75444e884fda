# Taken from https://github.com/linuxserver/docker-changedetection.io/blob/main/Dockerfile
# Test that we can still build on Alpine (musl modified libc https://musl.libc.org/)
# Some packages wont install via pypi because they dont have a wheel available under this architecture.

FROM ghcr.io/linuxserver/baseimage-alpine:3.21
ENV PYTHONUNBUFFERED=1

COPY requirements.txt /requirements.txt

RUN \
 apk add --update --no-cache --virtual=build-dependencies \
    build-base \
    cargo \
    git \
    jpeg-dev \
    libc-dev \
    libffi-dev \
    libxslt-dev \
    openssl-dev \
    python3-dev \
    file \
    zip \
    zlib-dev && \
  apk add --update --no-cache \
    libjpeg \
    libxslt \
    nodejs \
    poppler-utils \
    python3 && \
  echo "**** pip3 install test of changedetection.io ****" && \
  python3 -m venv /lsiopy  && \
  pip install -U pip wheel setuptools && \
  pip install -U --no-cache-dir --find-links https://wheel-index.linuxserver.io/alpine-3.21/ -r /requirements.txt && \
  apk del --purge \
    build-dependencies
