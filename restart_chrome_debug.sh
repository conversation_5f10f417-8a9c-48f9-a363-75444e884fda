#!/bin/bash

echo "🔄 Restarting Chrome with debugging enabled..."

# Kill existing Chrome processes
echo "Stopping existing Chrome processes..."
pkill -f "Google Chrome"
sleep 2

# Wait for processes to fully terminate
while pgrep -f "Google Chrome" > /dev/null; do
    echo "Waiting for Chrome to close..."
    sleep 1
done

echo "✅ Chrome processes stopped"

# Start Chrome with debugging
echo "🚀 Starting Chrome with debugging on port 9222..."

# Create user data directory
mkdir -p ./chrome_session

# Launch Chrome with debugging
/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome \
    --remote-debugging-port=9222 \
    --user-data-dir=./chrome_session \
    --no-first-run \
    --no-default-browser-check \
    --disable-web-security \
    --disable-features=VizDisplayCompositor \
    https://web.whatsapp.com &

echo "⏳ Waiting for Chrome to start..."
sleep 3

# Check if debugging is available
echo "🔍 Checking debugging connection..."
python3 chrome_launcher.py --check

echo ""
echo "📱 Chrome should now be open with WhatsApp Web"
echo "🔗 Debugging available on: http://localhost:9222"
echo "📝 Please log into WhatsApp Web if needed"
echo ""
echo "Press Enter when ready to test message parsing..."
read
