#!/usr/bin/env python3
"""
Real-time WhatsApp Message Monitor
Detects messages that arrive between scans (true real-time detection)
"""

import subprocess
import sys
import json
import time
from datetime import datetime

class RealtimeMonitor:
    def __init__(self, group_name="let me test, pls", interval=60):
        self.group_name = group_name
        self.interval = interval
        self.last_message_count = 0
        self.last_messages = []
        self.monitoring = False
        
    def run_playwright_script(self, script_content):
        """Run playwright script in a separate process"""
        try:
            with open('/tmp/realtime_script.py', 'w') as f:
                f.write(script_content)
            
            result = subprocess.run([sys.executable, '/tmp/realtime_script.py'], 
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                return json.loads(result.stdout)
            else:
                return {'success': False, 'error': result.stderr}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def get_recent_messages(self, limit=10):
        """Get the most recent messages from the group"""
        script = f'''
import json
from playwright.sync_api import sync_playwright
import time

try:
    with sync_playwright() as p:
        browser = p.chromium.connect_over_cdp("http://localhost:9222")
        context = browser.contexts[0]
        pages = context.pages
        
        whatsapp_page = None
        for page in pages:
            try:
                if 'web.whatsapp.com' in page.url:
                    page.query_selector('body')
                    whatsapp_page = page
                    break
            except:
                continue
        
        if not whatsapp_page:
            result = {{'success': False, 'error': 'WhatsApp page not found', 'messages': []}}
        else:
            try:
                whatsapp_page.wait_for_selector('#pane-side', timeout=10000)
                chat_elements = whatsapp_page.query_selector_all('#pane-side div[role="listitem"]')
            except:
                result = {{'success': False, 'error': 'Chat list not found', 'messages': []}}
                print(json.dumps(result))
                exit()
            
            target_index = -1
            for i, chat_elem in enumerate(chat_elements):
                try:
                    title_elem = chat_elem.query_selector('span[title]')
                    if title_elem:
                        title = title_elem.get_attribute('title') or title_elem.text_content()
                        if title and title.strip() == "{self.group_name}":
                            target_index = i
                            break
                except:
                    continue
            
            if target_index == -1:
                result = {{'success': False, 'error': 'Group not found', 'messages': []}}
            else:
                chat_elements[target_index].click()
                time.sleep(2)
                
                conv_panel = whatsapp_page.query_selector('#main')
                if not conv_panel:
                    result = {{'success': False, 'error': 'Conversation panel not found', 'messages': []}}
                else:
                    # Get message elements (most recent first by reversing)
                    message_elements = whatsapp_page.query_selector_all('div[role="row"]')
                    
                    # Take only the most recent messages to reduce processing
                    recent_elements = message_elements[-{limit}:] if len(message_elements) > {limit} else message_elements
                    
                    messages = []
                    for i, msg_elem in enumerate(recent_elements):
                        try:
                            # Get message text
                            text_selectors = ['span.selectable-text', '.selectable-text']
                            message_text = None
                            for text_selector in text_selectors:
                                text_elem = msg_elem.query_selector(text_selector)
                                if text_elem:
                                    message_text = text_elem.text_content()
                                    if message_text and message_text.strip():
                                        message_text = message_text.strip()
                                        break
                            
                            if not message_text:
                                continue
                            
                            # Get timestamp
                            timestamp = "Unknown"
                            time_selectors = ['span[data-testid="msg-time"]', 'span[title*=":"]']
                            for time_selector in time_selectors:
                                time_elem = msg_elem.query_selector(time_selector)
                                if time_elem:
                                    time_text = time_elem.get_attribute('title') or time_elem.text_content()
                                    if time_text and ':' in time_text:
                                        timestamp = time_text.strip()
                                        break
                            
                            # Detect sender
                            is_outgoing = False
                            sender = "Unknown"
                            
                            # Check if it's your message
                            if msg_elem.query_selector('div[class*="message-out"]'):
                                is_outgoing = True
                                sender = "You"
                            else:
                                # Try to get sender from aria-label
                                aria_label = msg_elem.get_attribute('aria-label')
                                if aria_label and not aria_label.startswith('You'):
                                    if ':' in aria_label:
                                        potential_sender = aria_label.split(':')[0].strip()
                                        if len(potential_sender) < 50 and potential_sender:
                                            sender = potential_sender
                            
                            # Create unique message identifier
                            message_id = f"{{message_text[:30]}}_{{timestamp}}_{{sender}}"
                            
                            messages.append({{
                                'id': message_id,
                                'text': message_text,
                                'timestamp': timestamp,
                                'sender': sender,
                                'is_outgoing': is_outgoing,
                                'scan_time': "{datetime.now().isoformat()}"
                            }})
                            
                        except Exception as e:
                            continue
                    
                    result = {{
                        'success': True, 
                        'messages': messages, 
                        'total_elements': len(message_elements),
                        'processed_elements': len(recent_elements)
                    }}
        
        print(json.dumps(result))

except Exception as e:
    print(json.dumps({{'success': False, 'error': str(e), 'messages': []}}))
'''
        
        return self.run_playwright_script(script)

    def detect_new_messages(self, current_messages):
        """Detect messages that are new since last scan"""
        if not self.last_messages:
            # First scan - don't report anything as "new"
            return []
        
        # Create sets of message IDs for comparison
        last_ids = {msg['id'] for msg in self.last_messages}
        current_ids = {msg['id'] for msg in current_messages}
        
        # Find truly new message IDs
        new_ids = current_ids - last_ids
        
        # Return new messages
        new_messages = [msg for msg in current_messages if msg['id'] in new_ids]
        
        # Sort by timestamp if possible (most recent first)
        try:
            new_messages.sort(key=lambda x: x['timestamp'], reverse=True)
        except:
            pass
        
        return new_messages

    def start_monitoring(self):
        """Start real-time monitoring"""
        print(f"🚀 Real-time WhatsApp Monitor")
        print(f"📱 Group: '{self.group_name}'")
        print(f"⏱️  Scan interval: {self.interval} seconds")
        print(f"🎯 Detecting messages that arrive between scans")
        print("=" * 80)
        print("Press Ctrl+C to stop monitoring")
        print("=" * 80)
        
        self.monitoring = True
        scan_count = 0
        total_new_messages = 0
        
        try:
            while self.monitoring:
                scan_count += 1
                current_time = datetime.now().strftime('%H:%M:%S')
                
                print(f"\n🔍 SCAN #{scan_count} at {current_time}")
                
                # Get recent messages
                result = self.get_recent_messages(limit=20)
                
                if result['success']:
                    current_messages = result['messages']
                    
                    # Detect new messages since last scan
                    new_messages = self.detect_new_messages(current_messages)
                    
                    if new_messages:
                        total_new_messages += len(new_messages)
                        
                        print(f"🚨 {len(new_messages)} NEW MESSAGE(S) DETECTED!")
                        print("=" * 60)
                        
                        for msg in new_messages:
                            direction = "📤 OUT" if msg['is_outgoing'] else "📥 IN"
                            print(f"{direction} [{msg['timestamp']}] {msg['sender']}: {msg['text']}")
                            
                            # Log to file with detailed info
                            with open('realtime_messages.log', 'a', encoding='utf-8') as f:
                                f.write(f"{datetime.now().isoformat()} | SCAN#{scan_count} | {direction} | [{msg['timestamp']}] {msg['sender']}: {msg['text']}\n")
                        
                        print("=" * 60)
                    else:
                        if scan_count == 1:
                            print(f"✅ Monitoring started - {len(current_messages)} recent messages loaded")
                        else:
                            print("✅ No new messages since last scan")
                    
                    # Update last messages for next comparison
                    self.last_messages = current_messages
                    
                    # Show status every 10 scans
                    if scan_count % 10 == 0:
                        print(f"📊 Status: {scan_count} scans completed, {total_new_messages} new messages detected")
                    
                else:
                    print(f"❌ Error: {result['error']}")
                
                # Wait for next scan
                if self.monitoring:
                    print(f"⏳ Next scan in {self.interval} seconds...")
                    time.sleep(self.interval)
                
        except KeyboardInterrupt:
            print(f"\n\n🛑 Monitoring stopped by user")
            self.stop_monitoring(scan_count, total_new_messages)
        except Exception as e:
            print(f"\n\n❌ Monitoring error: {e}")
            self.stop_monitoring(scan_count, total_new_messages)

    def stop_monitoring(self, scan_count, total_new_messages):
        """Stop monitoring and show summary"""
        self.monitoring = False
        
        print("\n" + "=" * 80)
        print("📊 MONITORING SESSION SUMMARY")
        print("=" * 80)
        print(f"📱 Group monitored: {self.group_name}")
        print(f"⏱️  Scan interval: {self.interval} seconds")
        print(f"🔍 Total scans: {scan_count}")
        print(f"🚨 New messages detected: {total_new_messages}")
        print(f"📝 Log file: realtime_messages.log")
        
        if scan_count > 0:
            avg_time = (scan_count * self.interval) / 60
            print(f"⏰ Total monitoring time: {avg_time:.1f} minutes")
        
        print("👋 Session ended")

if __name__ == "__main__":
    monitor = RealtimeMonitor(group_name="let me test, pls", interval=60)
    monitor.start_monitoring()
