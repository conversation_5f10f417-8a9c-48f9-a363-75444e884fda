"""
ChangeDetection.io Configuration for WhatsApp Monitoring
"""

# Configuration for connecting to existing Chrome session
PLAYWRIGHT_CONFIG = {
    "browser_connection_url": "http://localhost:9222",
    "use_existing_session": True,
    "headless": False
}

# CSS Selector to monitor for changes
WHATSAPP_SELECTOR = '[data-testid="conversation-panel-messages"]'

# Browser steps (minimal since we're using existing session)
BROWSER_STEPS = """
// Ensure we're on the right page and scroll to bottom
if (window.location.href.includes('web.whatsapp.com')) {
    // Scroll to bottom to get latest messages
    const messagesContainer = document.querySelector('[data-testid="conversation-panel-messages"]');
    if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Wait for messages to load
    await new Promise(resolve => setTimeout(resolve, 1000));
}
"""

# Watch configuration for ChangeDetection.io
WATCH_CONFIG = {
    "url": "https://web.whatsapp.com",
    "fetch_backend": "playwright",
    "browser_steps": BROWSER_STEPS,
    "css_filter": WHATSAPP_SELECTOR,
    "check_interval": 30,  # Check every 30 seconds
    "notification_urls": [
        "json://localhost:8000/webhook"  # Local webhook for testing
    ]
}

# Instructions for manual setup in ChangeDetection.io UI
SETUP_INSTRUCTIONS = """
Manual Setup in ChangeDetection.io:

1. Start Chrome with debugging:
   python chrome_launcher.py

2. In ChangeDetection.io, create new watch:
   - URL: https://web.whatsapp.com
   - Fetch Method: Playwright
   - Browser Connection: http://localhost:9222
   
3. Browser Steps tab:
   {browser_steps}

4. Filters & Text tab:
   - CSS/JSONPath Filter: {css_selector}
   
5. Notifications tab:
   - Add webhook URL for your pipeline
   
6. Settings tab:
   - Check interval: 30 seconds
   - Trigger: Text changes

7. Save and start monitoring!
""".format(
    browser_steps=BROWSER_STEPS,
    css_selector=WHATSAPP_SELECTOR
)

if __name__ == "__main__":
    print("ChangeDetection.io Configuration for WhatsApp Monitoring")
    print("=" * 60)
    print(SETUP_INSTRUCTIONS)
